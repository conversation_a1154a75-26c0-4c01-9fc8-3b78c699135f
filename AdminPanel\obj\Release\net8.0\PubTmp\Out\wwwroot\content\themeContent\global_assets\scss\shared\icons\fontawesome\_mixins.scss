/* ------------------------------------------------------------------------------
 *
 *  # Font Awesome mixins
 *
 *  Custom mixins for Font Awesome icon set only.
 *
 * ---------------------------------------------------------------------------- */

// Rotate icon
@mixin fa-icon-rotate($degrees, $rotation) {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation})";
    transform: rotate($degrees);
}

// Flip icon
@mixin fa-icon-flip($horiz, $vert, $rotation) {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation}, mirror=1)";
    transform: scale($horiz, $vert);
}
