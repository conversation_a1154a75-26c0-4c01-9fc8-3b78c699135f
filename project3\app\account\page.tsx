"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Link from "next/link";
import { useSettings } from "@/contexts/settings-context";
import { useUser } from "@/contexts/user-context";
import { MakeApiCallAsync, Config } from "@/lib/api-helper";
import { useToast } from "@/hooks/use-toast";
import {
  User,
  Package,
  CreditCard,
  Heart,
  LogOut,
  Save,
  Eye,
  EyeOff,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Shield,
  Star,
  Globe,
  UserCheck,
  Settings,
  Grid3X3,
} from "lucide-react";

export default function AccountPage() {
  const { t, primaryColor, primaryTextColor } = useSettings();
  const { user, isLoggedIn, isLoading, logout, updateProfile } = useUser();
  const { toast } = useToast();
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [categorySearch, setCategorySearch] = useState("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [filteredCategories, setFilteredCategories] = useState<any[]>([]);
  const [profileData, setProfileData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    gender: "",
    category: "",
    newPassword: "",
    confirmPassword: "",
    currentPassword: "",
  });
  const [error, setError] = useState("");

  // All useEffect hooks must be at the top before any conditional returns
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const param = {
          PageNumber: 1,
          PageSize: 100,
          SortColumn: "Name",
          SortOrder: "ASC",
        };
        const headers = {
          "Content-Type": "application/json",
          Accept: "application/json",
          // Authorization header will be automatically added by MakeApiCallAsync
        };
        const categoriesResponse = await MakeApiCallAsync(
          Config.END_POINT_NAMES.GET_CATEGORIES_LIST,
          null,
          param,
          headers,
          "POST",
          true
        );

        if (categoriesResponse?.data?.data) {
          try {
            const parsedData = JSON.parse(categoriesResponse.data.data);
            setCategories(parsedData);
            setFilteredCategories(parsedData);
          } catch (parseError) {
            console.error("Error parsing categories data:", parseError);
            setCategories([]);
            setFilteredCategories([]);
          }
        } else {
          setCategories([]);
          setFilteredCategories([]);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        setCategories([]);
        setFilteredCategories([]);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // Update profile data when user data changes
  useEffect(() => {
    if (user) {
      // Map all possible field variations from user data
      const userData = {
        firstName: user.FirstName || user.firstname || user.first_name || "",
        lastName: user.LastName || user.lastname || user.last_name || "",
        email:
          user.Email ||
          user.EmailAddress ||
          user.email ||
          user.email_address ||
          "",
        phone:
          user.PhoneNumber ||
          user.PhoneNo ||
          user.MobileNo ||
          user.phone ||
          user.mobile ||
          "",
        gender: user.Gender || user.gender || "",
        category: user.CategoryID || user.CategoryId || user.category_id || user.categoryId || user.SpecialistId || user.specialist_id || user.CatID || "",
      };

      setProfileData((prev) => ({
        ...prev,
        ...userData,
      }));
    }
  }, [user]);

  // Update category search when categories load and user has a category
  useEffect(() => {
    if (categories.length > 0 && profileData.category) {
      const categoryId = parseInt(profileData.category.toString());
      const userCategory = categories.find(
        (cat) => cat.CategoryID === categoryId
      );
      if (userCategory) {
        setCategorySearch(userCategory.CategoryName || userCategory.Name || "");
        setFilteredCategories(categories);
      }
    }
  }, [categories, profileData.category]);

  // Handle active tab styling with theme colors
  useEffect(() => {
    const updateTabStyles = () => {
      const tabTriggers = document.querySelectorAll('[data-active-bg]');
      tabTriggers.forEach((trigger) => {
        const element = trigger as HTMLElement;
        const isActive = element.getAttribute('data-state') === 'active';
        
        if (isActive) {
          element.style.setProperty('--state-active', primaryColor);
          element.style.setProperty('--state-active-text', primaryTextColor);
          element.style.setProperty('--state-active-scale', 'scale(1.05)');
          element.style.setProperty('--state-active-shadow', '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)');
          element.style.setProperty('--state-active-border', primaryColor);
          element.style.backgroundColor = primaryColor;
          element.style.color = primaryTextColor;
          element.style.transform = 'scale(1.05)';
          element.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
          element.style.borderColor = primaryColor;
        } else {
          element.style.setProperty('--state-active', 'rgb(209 213 219)');
          element.style.setProperty('--state-active-text', 'rgb(55 65 81)');
          element.style.setProperty('--state-active-scale', 'scale(1)');
          element.style.setProperty('--state-active-shadow', 'none');
          element.style.setProperty('--state-active-border', 'transparent');
          element.style.backgroundColor = 'rgb(209 213 219)';
          element.style.color = 'rgb(55 65 81)';
          element.style.transform = 'scale(1)';
          element.style.boxShadow = 'none';
          element.style.borderColor = 'transparent';
        }
      });
    };

    // Initial update
    updateTabStyles();

    // Set up observer for tab changes
    const observer = new MutationObserver(updateTabStyles);
    const tabsList = document.querySelector('[role="tablist"]');
    if (tabsList) {
      observer.observe(tabsList, {
        attributes: true,
        subtree: true,
        attributeFilter: ['data-state']
      });
    }

    return () => observer.disconnect();
  }, [primaryColor, primaryTextColor]);

  // Redirect to login if not authenticated (only after loading is complete)
  useEffect(() => {
    console.log('🔍 Account page auth check:', { isLoading, isLoggedIn, user: !!user });
    if (!isLoading && !isLoggedIn) {
      console.log('🚨 Account page: Redirecting to login');
      router.push('/login?redirect=/account');
    }
  }, [isLoading, isLoggedIn, router, user]);





  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Show loading state while checking authentication
  if (isLoading) {
    console.log('🔄 Account page: Showing loading state');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // If not loading and not logged in, don't render anything (redirect will happen)
  if (!isLoading && !isLoggedIn) {
    console.log('🚨 Account page: Not logged in, should redirect');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-muted-foreground">Redirecting to login...</p>
        </div>
      </div>
    );
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Check if this is a password update
    const isPasswordUpdate = profileData.newPassword || profileData.confirmPassword || profileData.currentPassword;

    if (isPasswordUpdate) {
      if (profileData.newPassword !== profileData.confirmPassword) {
        setError("New password and confirm password do not match");
        setLoading(false);
        return;
      }

      if (profileData.newPassword && profileData.newPassword.length < 8) {
        setError("Password must be at least 8 characters long");
        setLoading(false);
        return;
      }

      // TODO: Implement password update API call
      setError("Password update functionality is not yet implemented");
      setLoading(false);
      return;
    }

    // Profile update
    try {
      const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
      };

      const param = {
        requestParameters: {
          FirstName: profileData.firstName,
          LastName: profileData.lastName,
          Gender: profileData.gender || "Male",
          CategoryId: profileData.category || "1024"
        }
      };

      console.log("Sending update profile request:", param);

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.UPDATE_PROFILE,
        null,
        param,
        headers,
        "POST",
        true
      );

      console.log("Profile update response:", response);

      if (response?.data && !response.data.errorMessage) {
        // Parse the response data
        let responseData;
        if (typeof response.data.data === 'string') {
          responseData = JSON.parse(response.data.data);
        } else {
          responseData = response.data.data;
        }

        if (Array.isArray(responseData) && responseData.length > 0 &&
            responseData[0].ResponseMsg === "Saved Successfully") {

          setSuccess(true);

          // Update user context with new profile data
          updateProfile({
            FirstName: profileData.firstName,
            LastName: profileData.lastName,
            UserName: `${profileData.firstName} ${profileData.lastName}`.trim()
          });

          // Update cookies with new user data
          try {
            const updateResponse = await fetch('/api/auth/update-user-cookies', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include',
              body: JSON.stringify({
                FirstName: profileData.firstName,
                LastName: profileData.lastName,
                UserName: `${profileData.firstName} ${profileData.lastName}`.trim()
              })
            });

            if (updateResponse.ok) {
              console.log('User cookies updated successfully');
            }
          } catch (cookieError) {
            console.warn('Failed to update user cookies:', cookieError);
          }

          toast({
            title: "Success!",
            description: "Profile updated successfully!",
          });

          // Reset success message after 3 seconds
          setTimeout(() => setSuccess(false), 3000);
        } else {
          throw new Error(responseData?.[0]?.ResponseMsg || 'Failed to update profile');
        }
      } else {
        throw new Error(response?.data?.errorMessage || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile. Please try again.';
      setError(errorMessage);

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t("home")}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t("myAccount")}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Content */}
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">{t("myAccount")}</h1>

        <div className="grid grid-cols-1 md:grid-cols-[250px_1fr] gap-6">
          {/* Sidebar */}
          <div className="space-y-4">
            <Card>
              <div className="p-6">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4 relative">
                    <User className="h-10 w-10 text-primary" />
                    {user?.IsVerified && (
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <UserCheck className="h-3 w-3 text-white" />
                      </div>
                    )}
                  </div>
                  <h3 className="font-medium">
                    {user?.FirstName} {user?.LastName}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {user?.Email || user?.EmailAddress}
                  </p>
                  {user?.Pointno !== undefined && (
                    <div className="flex items-center gap-1 mt-2 px-2 py-1 bg-yellow-100 rounded-full">
                      <Star className="h-3 w-3 text-yellow-600" />
                      <span className="text-xs font-medium text-yellow-700">
                        {user.Pointno} Credit
                      </span>
                    </div>
                  )}
                </div>

                <div className="space-y-1">
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link href="/account">
                      <User className="mr-2 h-4 w-4" />
                      Profile
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link href="/orders">
                      <Package className="mr-2 h-4 w-4" />
                      Orders
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link href="/addresses">
                      <MapPin className="mr-2 h-4 w-4" />
                      Addresses
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link href="/payment-methods">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Payment Methods
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link href="/wishlist">
                      <Heart className="mr-2 h-4 w-4" />
                      Wishlist
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
                    onClick={() => {
                      logout();
                      toast({
                        title: "Logged Out",
                        description: "You have been successfully logged out.",
                      });
                      router.push("/");
                    }}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Main Content */}
          <div>
            <Tabs defaultValue="profile">
              <TabsList className="grid w-full grid-cols-3 mb-6 gap-1 sm:gap-2 bg-transparent p-0 h-auto">
                <TabsTrigger 
                  value="profile"
                  className="rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102"
                  style={{
                    backgroundColor: 'var(--state-active, rgb(209 213 219))',
                    color: 'var(--state-active-text, rgb(55 65 81))',
                    transform: 'var(--state-active-scale, scale(1))',
                    boxShadow: 'var(--state-active-shadow, none)',
                    borderColor: 'var(--state-active-border, transparent)'
                  }}
                  data-active-bg={primaryColor}
                  data-active-text={primaryTextColor}
                >
                  Personal Information
                </TabsTrigger>
                <TabsTrigger 
                  value="password"
                  className="rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102"
                  style={{
                    backgroundColor: 'var(--state-active, rgb(209 213 219))',
                    color: 'var(--state-active-text, rgb(55 65 81))',
                    transform: 'var(--state-active-scale, scale(1))',
                    boxShadow: 'var(--state-active-shadow, none)',
                    borderColor: 'var(--state-active-border, transparent)'
                  }}
                  data-active-bg={primaryColor}
                  data-active-text={primaryTextColor}
                >
                  Security
                </TabsTrigger>
                <TabsTrigger 
                  value="overview"
                  className="rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102"
                  style={{
                    backgroundColor: 'var(--state-active, rgb(209 213 219))',
                    color: 'var(--state-active-text, rgb(55 65 81))',
                    transform: 'var(--state-active-scale, scale(1))',
                    boxShadow: 'var(--state-active-shadow, none)',
                    borderColor: 'var(--state-active-border, transparent)'
                  }}
                  data-active-bg={primaryColor}
                  data-active-text={primaryTextColor}
                >
                  Account Details
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4 bg-white rounded-lg shadow-sm">
                <div className="p-8">
                  <h3 className="text-xl font-semibold mb-6">Account Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Account Information */}
                  <Card>
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <User className="h-5 w-5 text-primary" />
                        Account Information
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            User Type
                          </span>
                          <span className="font-medium">
                            {user?.UserTypeID === 1
                              ? "Admin"
                              : user?.UserTypeID === 2
                              ? "Customer"
                              : "User"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Gender
                          </span>
                          <span className="font-medium">
                            {user?.Gender || "Not specified"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Specialist
                          </span>
                          <span className="font-medium">
                            {categories.find(
                              (cat) =>
                                cat.CategoryID === parseInt(profileData.category?.toString() || "0")
                            )?.Name ||
                            categories.find(
                              (cat) =>
                                cat.CategoryID ===
                                (user?.CategoryID ||
                                  user?.CategoryId ||
                                  user?.categoryId)
                            )?.Name || "Not specified"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Account Status
                          </span>
                          <div className="flex items-center gap-2">
                            {user?.IsActive ? (
                              <>
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-green-600 font-medium">
                                  Active
                                </span>
                              </>
                            ) : (
                              <>
                                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                <span className="text-red-600 font-medium">
                                  Inactive
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                        <div className="flex justify-between items-center py-2">
                          <span className="text-sm text-muted-foreground">
                            Verification Status
                          </span>
                          <div className="flex items-center gap-2">
                            {user?.IsVerified ? (
                              <>
                                <UserCheck className="w-4 h-4 text-green-500" />
                                <span className="text-green-600 font-medium">
                                  Verified
                                </span>
                              </>
                            ) : (
                              <>
                                <Shield className="w-4 h-4 text-orange-500" />
                                <span className="text-orange-600 font-medium">
                                  Pending
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Contact Information */}
                  <Card>
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <Phone className="h-5 w-5 text-primary" />
                        Contact Information
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <Mail className="h-5 w-5 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-500">
                              Email Address
                            </p>
                            <p className="font-medium">
                              {user?.EmailAddress || user?.Email}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <Phone className="h-5 w-5 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-500">
                              Phone Number
                            </p>
                            <p className="font-medium">
                              {user?.PhoneNo || user?.MobileNo}
                            </p>
                          </div>
                        </div>
                        {user?.MobileNo &&
                          user?.PhoneNo &&
                          user?.MobileNo !== user?.PhoneNo && (
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                              <Phone className="h-5 w-5 text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-500">
                                  Mobile Number
                                </p>
                                <p className="font-medium">{user?.MobileNo}</p>
                              </div>
                            </div>
                          )}
                      </div>
                    </div>
                  </Card>

                  {/* Location Information */}
                  <Card>
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-primary" />
                        Location Information
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <Globe className="h-5 w-5 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-500">Country</p>
                            <p className="font-medium">
                              {user?.CountryName || "Not specified"}
                            </p>
                          </div>
                        </div>
                        {user?.AddressLineOne && (
                          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                            <MapPin className="h-5 w-5 text-gray-500" />
                            <div>
                              <p className="text-sm text-gray-500">Address</p>
                              <p className="font-medium">
                                {user?.AddressLineOne}
                              </p>
                              {(user?.CityName || user?.StateName) && (
                                <p className="text-sm text-gray-500">
                                  {[user?.CityName, user?.StateName]
                                    .filter(Boolean)
                                    .join(", ")}
                                </p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>

                  {/* Account Activity */}
                  <Card>
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-primary" />
                        Account Activity
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Member Since
                          </span>
                          <span className="font-medium">
                            {user?.CreatedOn
                              ? new Date(user.CreatedOn).toLocaleDateString()
                              : "N/A"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Credit Balance
                          </span>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">
                              {user?.Pointno || 0}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="profile" className="mt-4 bg-white rounded-lg shadow-sm">
                <div className="p-8">
                  <h3 className="text-xl font-semibold mb-6">Personal Information</h3>
                  <Card>
                  <form onSubmit={handleSubmit} className="p-6">
                    {success && (
                      <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        Profile updated successfully!
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          name="firstName"
                          value={profileData.firstName}
                          onChange={handleChange}
                          placeholder="Enter your first name"
                        />
                      </div>

                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          name="lastName"
                          value={profileData.lastName}
                          onChange={handleChange}
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <Label htmlFor="gender">Gender</Label>
                        <select
                          id="gender"
                          name="gender"
                          value={profileData.gender}
                          onChange={(e) => handleChange(e as any)}
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-black ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          <option value="">Select Gender</option>
                          <option value="Male">Male</option>
                          <option value="Female">Female</option>
                        </select>
                      </div>

                      <div className="relative">
                        <Label htmlFor="category">Specialist</Label>
                        <div className="relative">
                          <Input
                            id="category"
                            name="category"
                            value={categorySearch}
                            onChange={(e) => {
                              setCategorySearch(e.target.value);
                              setShowCategoryDropdown(true);
                            }}
                            onFocus={() => setShowCategoryDropdown(true)}
                            placeholder="Search and select specialist..."
                            disabled={loadingCategories}
                            className="pr-10"
                          />
                          {categorySearch && (
                            <button
                              type="button"
                              onClick={() => {
                                setCategorySearch("");
                                setProfileData((prev) => ({
                                  ...prev,
                                  category: "",
                                }));
                                setShowCategoryDropdown(false);
                              }}
                              className="absolute right-8 top-3 h-4 w-4 text-muted-foreground hover:text-red-500"
                            >
                              ×
                            </button>
                          )}
                          <Grid3X3 className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />

                          {showCategoryDropdown && !loadingCategories && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                              {categories
                                .filter((category) =>
                                  category.Name.toLowerCase().includes(
                                    categorySearch.toLowerCase()
                                  )
                                )
                                .map((category) => (
                                  <div
                                    key={category.CategoryID}
                                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                                    onClick={() => {
                                      setProfileData((prev) => ({
                                        ...prev,
                                        category: category.CategoryID,
                                      }));
                                      setCategorySearch(category.Name);
                                      setShowCategoryDropdown(false);
                                    }}
                                  >
                                    <div className="font-medium">
                                      {category.Name}
                                    </div>
                                    {category.ParentCategoryID && (
                                      <div className="text-xs text-muted-foreground">
                                        Subcategory of:{" "}
                                        {
                                          categories.find(
                                            (cat) =>
                                              cat.CategoryID ===
                                              category.ParentCategoryID
                                          )?.Name
                                        }
                                      </div>
                                    )}
                                  </div>
                                ))}
                              {categories.filter((category) =>
                                category.Name.toLowerCase().includes(
                                  categorySearch.toLowerCase()
                                )
                              ).length === 0 && (
                                <div className="px-3 py-2 text-sm text-muted-foreground">
                                  No specialists found
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        {loadingCategories && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Loading specialists...
                          </p>
                        )}

                        {/* Click outside to close dropdown */}
                        {showCategoryDropdown && (
                          <div
                            className="fixed inset-0 z-5"
                            onClick={() => setShowCategoryDropdown(false)}
                          />
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={profileData.email}
                          onChange={handleChange}
                          disabled
                          className="bg-gray-50"
                          placeholder="Email address"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Email address cannot be changed
                        </p>

                      </div>

                      <div>
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={profileData.phone}
                          onChange={handleChange}
                          disabled
                          className="bg-gray-50"
                          placeholder="Phone number"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Phone number cannot be changed
                        </p>

                      </div>
                    </div>

                    <Button type="submit" disabled={loading}>
                      {loading ? (
                        <span className="flex items-center gap-2">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Updating...
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <Save className="h-4 w-4" />
                          Save Changes
                        </span>
                      )}
                    </Button>
                  </form>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="password" className="mt-4 bg-white rounded-lg shadow-sm">
                <div className="p-8">
                  <h3 className="text-xl font-semibold mb-6">Security Settings</h3>
                  <Card>
                  <form onSubmit={handleSubmit} className="p-6">
                    {success && (
                      <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        Password updated successfully!
                      </div>
                    )}

                    <div className="space-y-4 mb-6">
                      <div>
                        <Label htmlFor="currentPassword">
                          Current Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="currentPassword"
                            name="currentPassword"
                            type={showPassword ? "text" : "password"}
                            value={profileData.currentPassword}
                            onChange={handleChange}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="newPassword">New Password</Label>
                        <div className="relative">
                          <Input
                            id="newPassword"
                            name="newPassword"
                            type={showPassword ? "text" : "password"}
                            value={profileData.newPassword}
                            onChange={handleChange}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="confirmPassword">
                          Confirm New Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="confirmPassword"
                            name="confirmPassword"
                            type={showPassword ? "text" : "password"}
                            value={profileData.confirmPassword}
                            onChange={handleChange}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      disabled={
                        loading ||
                        !profileData.currentPassword ||
                        !profileData.newPassword ||
                        !profileData.confirmPassword ||
                        profileData.newPassword !== profileData.confirmPassword
                      }
                    >
                      {loading ? (
                        <span className="flex items-center gap-2">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Updating...
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <Save className="h-4 w-4" />
                          Update Password
                        </span>
                      )}
                    </Button>
                    {error && (
                      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        {error}
                      </div>
                    )}
                  </form>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
