/* ------------------------------------------------------------------------------
 *
 *  # Prism
 *
 *  Styles for prism.min.js - lightweight, extensible syntax highlighter
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-prism {

	// Base
	code,
	pre {
		&[class*="language-"] {
			text-shadow: 0 1px #fff;
			word-break: break-all;
			line-height: 1.5;
			background-color: transparent;
			/*rtl:ignore*/
			direction: ltr;

			@media print {
				text-shadow: none;
			}
		}
	}

	// Token colors
	.token {
		&.comment,
		&.prolog,
		&.doctype,
		&.cdata,
		&.punctuation {
			color: #999;
		}

		&.property,
		&.tag,
		&.boolean,
		&.number,
		&.constant,
		&.symbol {
			color: #905;
		}

		&.selector,
		&.attr-name,
		&.string,
		&.builtin {
			color: #690;
		}

		&.operator,
		&.entity,
		&.url,
		.language-css &.string,
		.style &.string,
		&.variable {
			color: #a67f59;
			background: hsla(0,0%,100%,.5);
		}

		&.atrule,
		&.attr-value,
		&.keyword {
			color: #07a;
		}

		&.function {
			color: #DD4A68;
		}

		&.regex,
		&.important {
			color: #e90;
		}

		&.important {
			font-weight: bold;
		}

		&.entity {
			cursor: help;
		}
	}

	// Namespace opacity
	.namespace {
		opacity: 0.7;
	}

	// Line highlight
	.line-highlight {
		position: absolute;
		left: 0;
		right: 0;
		margin-top: 1.25rem;
		background: rgba(#678bdc, 0.12);  
		pointer-events: none;
		white-space: pre;
		line-height: inherit;

		&:before,
		&:after {
			content: attr(data-start);
			position: absolute;
			top: 0px;
			/*rtl:ignore*/
			left: 10px;
			padding: 2px;
			text-align: center;
			font-size: 10px;
			color: $text-muted;
		}

		&[data-end]:after {
			content: attr(data-end);
			top: auto;
			bottom: 0;
		}
	}

	// Line numbers
	.line-numbers {
		.line-numbers-rows {
			position: absolute;
			pointer-events: none;
			top: 0;
			font-size: 100%;
			width: 36px; // works for line-numbers below 1000 lines
			letter-spacing: -1px;
			/*rtl:begin:ignore*/
			left: -50px;
			border-right: 1px solid #999;
			/*rtl:end:ignore*/
			user-select: none;

			> span {
				pointer-events: none;
				display: block;
				counter-increment: linenumber;

				&:before {
					content: counter(linenumber);
					color: $text-muted;
					display: block;
					/*rtl:begin:ignore*/
					padding-right: 10px;
					text-align: right;
					/*rtl:end:ignore*/
				}
			}
		}
	}
	pre.line-numbers {
		position: relative;
		/*rtl:ignore*/
		padding-left: 50px;
		counter-reset: linenumber;

		> code {
			position: relative;
		}
	}


	//
	// Code blocks
	//

	pre {
		&[class*="language-"] {
			overflow: auto;
			position: relative;
		    background-color: #fdfdfd;
		    background-image: -webkit-linear-gradient(transparent 50%, rgba($color-primary-700, 0.025) 50%);
		    background-image: -moz-linear-gradient(transparent 50%, rgba($color-primary-700, 0.025) 50%);
		    background-image: -ms-linear-gradient(transparent 50%, rgba($color-primary-700, 0.025) 50%);
		    background-image: -o-linear-gradient(transparent 50%, rgba($color-primary-700, 0.025) 50%);
		    background-image: linear-gradient(transparent 50%, rgba($color-primary-700, 0.025) 50%);
		    background-size: 3em 3em;
		    background-origin: content-box;
		    /*rtl:begin:ignore*/
			border-left-width: 0;
		    box-shadow: 3px 0 0 0 $color-primary-500 inset;
			@include border-left-radius(0);
			/*rtl:end:ignore*/
		}

		&[data-line] {
			position: relative;
			/*rtl:ignore*/
			padding-left: 36px;
		}
	}


	//
	// Toolbar
	//

	pre.code-toolbar {
		position: relative;

		> .toolbar {
			background-color: #f8f8f8;
			display: inline-block;
			position: absolute;
			top: 0;
			text-shadow: none;
			color: $body-color;
			border-bottom: $border-width solid $border-color;
			/*rtl:begin:ignore*/
			border-left: $border-width solid $border-color;
			right: 0;
			border-radius: 0 0 0 $border-radius;
			/*rtl:end:ignore*/

			// Items
			span,
			.toolbar-item {
				display: inline-block;
			}

			// Links
			a {
				cursor: pointer;
			}

			// Buttons
			button {
				background: none;
				border: 0;
				color: inherit;
				font: inherit;
				line-height: normal;
				overflow: visible;
				user-select: none;
			}

			// Basic styles
			a,
			button,
			span {
				padding: 0.25rem 0.5rem;
				font-size: $font-size-xs;
				outline: 0;

				@include hover-focus {
					color: inherit;
					text-decoration: none;
				}
			}
		}
	}
}
