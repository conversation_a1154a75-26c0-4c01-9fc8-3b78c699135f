/* ------------------------------------------------------------------------------
 *
 *  # Pace. Squares theme
 *
 *  Squares css spinner theme for Pace.
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-pace {

    // Define variables
    $pace-show-text: true;
    $pace-overlay-color: $color-slate-900;
    $pace-loader-color: $white;
    $pace-loader-size: 1.875rem;
    $pace-loader-border-width: 0.125rem;
    $pace-loader-border-spacer: ($pace-loader-border-width + 0.5rem);


    // Pace theme styles
    // ------------------------------

    // Overlay
    .pace-running {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: $pace-overlay-color;

        // Hide all content
        > *:not(.pace) {
            opacity: 0;
        }
    }

    // Base
    .pace {
        position: fixed;
        top: 50%;
        left: 0;
        right: 0;
        margin-top: -($pace-loader-size / 2);
        z-index: 9999;
        user-select: none;
        pointer-events: none;

        // Change colors on light/dark backgrounds
        @if (lightness($pace-overlay-color) < 75) {
            color: $white;
        }
    }

    // Progress
    .pace-progress {
        width: 100% !important;
    }

    // Activity
    .pace-activity {

        // Squares
        &:before,
        &:after {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin: auto;
            content: "";
            border: $pace-loader-border-width solid $pace-loader-color;
        }

        // Big
        &:before {
            animation: rotation_reverse 2s linear infinite;
            @include size($pace-loader-size);
        }

        // Small
        &:after {
            top: ($pace-loader-border-spacer / 2);
            animation: rotation 2s linear infinite;
            @include size($pace-loader-size - $pace-loader-border-spacer);
        }
    }

    // Hide inactive
    .pace-inactive {
        display: none;
    }

    // Progress text
    @if $pace-show-text {
        .pace-progress:after {
            content: attr(data-progress-text);
            text-align: center;
            width: 100%;
            display: inline-block;
            white-space: nowrap;
            margin-top: $pace-loader-size + ($spacer / 1.5);
        }
    }
}
