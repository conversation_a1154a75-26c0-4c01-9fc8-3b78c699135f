/* ------------------------------------------------------------------------------
 *
 *  # Pace. Perspective theme
 *
 *  Perspective css spinner theme for Pace.
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-pace {

    // Define variables
    $pace-show-text: true;
    $pace-overlay-color: $color-slate-900;
    $pace-loader-color: $white;
    $pace-loader-size: 1.5rem;


    // Pace theme styles
    // ------------------------------

    // Overlay
    .pace-running {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: $pace-overlay-color;

        // Hide all content
        > *:not(.pace) {
            opacity: 0;
        }
    }

    // Base
    .pace {
        position: fixed;
        top: 50%;
        left: 0;
        right: 0;
        margin-top: -($pace-loader-size / 2);
        pointer-events: none;
        user-select: none;
        z-index: 9999;

        // Change colors on light/dark backgrounds
        @if (lightness($pace-overlay-color) < 75) {
            color: $white;
        }
    }

    // Progress
    .pace-progress {
        width: 100% !important;
    }

    // Activity
    .pace-activity {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        margin: auto;
        background-color: $pace-loader-color;
        animation: perspective 1.2s infinite ease-in-out;
        @include size($pace-loader-size);
        @include border-radius($border-radius-sm);
    }

    // Hide inactive
    .pace-inactive {
        display: none;
    }

    // Progress text
    @if $pace-show-text {
        .pace-progress:after {
            content: attr(data-progress-text);
            text-align: center;
            width: 100%;
            display: inline-block;
            white-space: nowrap;
            margin-top: $pace-loader-size + ($spacer / 1.5);
        }
    }

    // Animations
    @keyframes perspective {
        0% { transform: perspective(7.5rem); }
        50% { transform: perspective(7.5rem) rotateY(180deg); }
        100% { transform: perspective(7.5rem) rotateY(180deg) rotateX(180deg); }
    }

    @-webkit-keyframes perspective {
        0% { -webkit-transform: perspective(7.5rem); }
        50% { -webkit-transform: perspective(7.5rem) rotateY(180deg); }
        100% { -webkit-transform: perspective(7.5rem) rotateY(180deg) rotateX(180deg); }
    }

    @-moz-keyframes perspective {
        0% { -moz-transform: perspective(7.5rem); }
        50% { -moz-transform: perspective(7.5rem) rotateY(180deg); }
        100% { -moz-transform: perspective(7.5rem) rotateY(180deg) rotateX(180deg); }
    }
}
