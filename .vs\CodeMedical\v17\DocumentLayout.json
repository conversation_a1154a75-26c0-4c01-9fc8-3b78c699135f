{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\areas\\v1\\controllers\\apiverificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\areas\\v1\\controllers\\apiverificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\controllers\\usermanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\controllers\\usermanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\areas\\v1\\controllers\\apidynamiccontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\areas\\v1\\controllers\\apidynamiccontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\areas\\v1\\controllers\\apicommoncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\areas\\v1\\controllers\\apicommoncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbinheritedmodels\\inheritedentitiesapimodule.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbinheritedmodels\\inheritedentitiesapimodule.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\controllers\\salescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\controllers\\salescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\sales\\partialviews\\_orderslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\sales\\partialviews\\_orderslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\sales\\orderdetail.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\sales\\orderdetail.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\usermanagement\\partialviews\\_userslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\usermanagement\\partialviews\\_userslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\usermanagement\\userslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\usermanagement\\userslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\iservices\\iapioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\iservices\\iapioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\services\\apioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\services\\apioperationservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\order.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\order.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\user.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\user.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\notifications\\partialviews\\_siteheadernotifications.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\notifications\\partialviews\\_siteheadernotifications.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{577C456B-45E8-45E1-AD1C-D83A6A7F99A2}|React.Web\\React.Web.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\react.web\\codemedical2\\src\\web\\views\\checkout\\checkout.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{577C456B-45E8-45E1-AD1C-D83A6A7F99A2}|React.Web\\React.Web.csproj|solutionrelative:react.web\\codemedical2\\src\\web\\views\\checkout\\checkout.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\mainmodels\\salesmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\mainmodels\\salesmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\notifications\\partialviews\\_adminpanelnotificationslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\notifications\\partialviews\\_adminpanelnotificationslist.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\common\\_listingdeletebutton.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\common\\_listingdeletebutton.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\controllers\\productscatalogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\controllers\\productscatalogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\helpers\\serviceextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\helpers\\serviceextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\services\\productservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\services\\productservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\iservices\\iproductservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\iservices\\iproductservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\areas\\v1\\controllers\\apiproductscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\areas\\v1\\controllers\\apiproductscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\services\\basicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\services\\basicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\productscatalog\\updateproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\productscatalog\\updateproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\adminpanel\\views\\productscatalog\\createnewproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B22D784D-A877-4E71-B6D5-5F5A9070DF2B}|AdminPanel\\AdminPanel.csproj|solutionrelative:adminpanel\\views\\productscatalog\\createnewproduct.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\product.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\product.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\producttype.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\producttype.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\manufacturer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\manufacturer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\dal\\repository\\iservices\\ibasicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{92047E6A-828E-47EE-9D53-71F584E9E5EA}|DAL\\DAL.csproj|solutionrelative:dal\\repository\\iservices\\ibasicdataservicesdal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbinheritedmodels\\inheritedentitieslevelone.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbinheritedmodels\\inheritedentitieslevelone.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|c:\\users\\<USER>\\downloads\\ec\\.net 8 version - latest\\project\\codemedical\\entities\\dbmodels\\bankstatus.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2C6AA03-5F94-4108-A8E0-92DD51A7F60A}|Entities\\Entities.csproj|solutionrelative:entities\\dbmodels\\bankstatus.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 13, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{********-fc2c-11d2-a433-00c04f72d18a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{0504ff91-9d61-11d0-a794-00a0c9110051}"}, {"$type": "Bookmark", "Name": "ST:0:0:{be4d7042-ba3f-11d2-840e-00c04f9902c1}"}, {"$type": "Bookmark", "Name": "ST:0:0:{605322a2-17ae-43f4-b60f-766556e46c87}"}, {"$type": "Bookmark", "Name": "ST:0:0:{ecb7191a-597b-41f5-9843-03a4cf275dde}"}, {"$type": "Bookmark", "Name": "ST:0:0:{0ad07096-bba9-4900-a651-0598d26f6d24}"}, {"$type": "Bookmark", "Name": "ST:0:0:{57d563b6-44a5-47df-85be-f4199ad6b651}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:2:0:{d212f56b-c48a-434c-a121-1c5d80b59b9f}"}, {"$type": "Bookmark", "Name": "ST:1:0:{d212f56b-c48a-434c-a121-1c5d80b59b9f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ApiVerificationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiVerificationController.cs", "RelativeDocumentMoniker": "AdminPanel\\Areas\\V1\\Controllers\\ApiVerificationController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiVerificationController.cs", "RelativeToolTip": "AdminPanel\\Areas\\V1\\Controllers\\ApiVerificationController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGEAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T10:58:25.916Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "UsersList.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\UserManagement\\UsersList.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\UserManagement\\UsersList.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\UserManagement\\UsersList.cshtml", "RelativeToolTip": "AdminPanel\\Views\\UserManagement\\UsersList.cshtml", "ViewState": "AgIAAHsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-06T20:18:40.792Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ApiCommonController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "RelativeDocumentMoniker": "AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "RelativeToolTip": "AdminPanel\\Areas\\V1\\Controllers\\ApiCommonController.cs", "ViewState": "AgIAAFQDAAAAAAAAAAAcwG0DAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T03:34:30.061Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Program.cs", "RelativeDocumentMoniker": "AdminPanel\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Program.cs", "RelativeToolTip": "AdminPanel\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T09:11:44.229Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "Order.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Order.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\Order.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Order.cs", "RelativeToolTip": "Entities\\DBModels\\Order.cs", "ViewState": "AgIAABcAAAAAAAAAAAA9wBsAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T07:05:59.13Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "IApiOperationServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\IServices\\IApiOperationServicesDAL.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T13:14:01.854Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\appsettings.json", "RelativeDocumentMoniker": "AdminPanel\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\appsettings.json", "RelativeToolTip": "AdminPanel\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAACCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T09:11:35.873Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "OrderDetail.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Sales\\OrderDetail.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\Sales\\OrderDetail.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Sales\\OrderDetail.cshtml", "RelativeToolTip": "AdminPanel\\Views\\Sales\\OrderDetail.cshtml", "ViewState": "AgIAAD8BAAAAAAAAAAAiwFQBAACyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-06T20:32:04.798Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "UserManagementController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\UserManagementController.cs", "RelativeDocumentMoniker": "AdminPanel\\Controllers\\UserManagementController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\UserManagementController.cs", "RelativeToolTip": "AdminPanel\\Controllers\\UserManagementController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACcEAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T07:46:19.578Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ApiDynamicController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "RelativeDocumentMoniker": "AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "RelativeToolTip": "AdminPanel\\Areas\\V1\\Controllers\\ApiDynamicController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAALgAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:06:21.831Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "_UsersList.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\UserManagement\\PartialViews\\_UsersList.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\UserManagement\\PartialViews\\_UsersList.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\UserManagement\\PartialViews\\_UsersList.cshtml", "RelativeToolTip": "AdminPanel\\Views\\UserManagement\\PartialViews\\_UsersList.cshtml", "ViewState": "AgIAAKgAAAAAAAAAAAAAAMIAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-06T20:19:01.874Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "InheritedEntitiesApiModule.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBInheritedModels\\InheritedEntitiesApiModule.cs", "RelativeDocumentMoniker": "Entities\\DBInheritedModels\\InheritedEntitiesApiModule.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBInheritedModels\\InheritedEntitiesApiModule.cs", "RelativeToolTip": "Entities\\DBInheritedModels\\InheritedEntitiesApiModule.cs", "ViewState": "AgIAAGEAAAAAAAAAAAA4wGkAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-03T20:24:45.077Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "SalesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\SalesController.cs", "RelativeDocumentMoniker": "AdminPanel\\Controllers\\SalesController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\SalesController.cs", "RelativeToolTip": "AdminPanel\\Controllers\\SalesController.cs", "ViewState": "AgIAACkBAAAAAAAAAAAwwDcBAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T09:40:10.109Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "_OrdersList.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Sales\\PartialViews\\_OrdersList.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\Sales\\PartialViews\\_OrdersList.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Sales\\PartialViews\\_OrdersList.cshtml", "RelativeToolTip": "AdminPanel\\Views\\Sales\\PartialViews\\_OrdersList.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-31T20:04:17.339Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "_SiteHeaderNotifications.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Notifications\\PartialViews\\_SiteHeaderNotifications.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\Notifications\\PartialViews\\_SiteHeaderNotifications.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Notifications\\PartialViews\\_SiteHeaderNotifications.cshtml", "RelativeToolTip": "AdminPanel\\Views\\Notifications\\PartialViews\\_SiteHeaderNotifications.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAACUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-31T17:37:57.303Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "ApiOperationServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\Services\\ApiOperationServicesDAL.cs", "ViewState": "AgIAACkAAAAAAAAAAAAiwG0AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:06:21.848Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "User.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\User.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\User.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\User.cs", "RelativeToolTip": "Entities\\DBModels\\User.cs", "ViewState": "AgIAACMAAAAAAAAAAAAAwCwAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T20:40:03.053Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "_AdminPanelNotificationsList.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Notifications\\PartialViews\\_AdminPanelNotificationsList.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\Notifications\\PartialViews\\_AdminPanelNotificationsList.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Notifications\\PartialViews\\_AdminPanelNotificationsList.cshtml", "RelativeToolTip": "AdminPanel\\Views\\Notifications\\PartialViews\\_AdminPanelNotificationsList.cshtml", "ViewState": "AgIAAEgAAAAAAAAAAAAAAGMAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-31T17:36:55.387Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "_ListingDeleteButton.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Common\\_ListingDeleteButton.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\Common\\_ListingDeleteButton.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\Common\\_ListingDeleteButton.cshtml", "RelativeToolTip": "AdminPanel\\Views\\Common\\_ListingDeleteButton.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-31T17:44:11.342Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Checkout.js", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\React.Web\\codemedical2\\src\\web\\views\\checkout\\Checkout.js", "RelativeDocumentMoniker": "React.Web\\codemedical2\\src\\web\\views\\checkout\\Checkout.js", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\React.Web\\codemedical2\\src\\web\\views\\checkout\\Checkout.js", "RelativeToolTip": "React.Web\\codemedical2\\src\\web\\views\\checkout\\Checkout.js", "ViewState": "AgIAAHQBAAAAAAAAAAAAAHwBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-01T14:04:15.351Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "ServiceExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Helpers\\ServiceExtensions.cs", "RelativeDocumentMoniker": "AdminPanel\\Helpers\\ServiceExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Helpers\\ServiceExtensions.cs", "RelativeToolTip": "AdminPanel\\Helpers\\ServiceExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACQAAAC8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:04:36.24Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "SalesModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\MainModels\\SalesModel.cs", "RelativeDocumentMoniker": "Entities\\MainModels\\SalesModel.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\MainModels\\SalesModel.cs", "RelativeToolTip": "Entities\\MainModels\\SalesModel.cs", "ViewState": "AgIAACIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T20:41:41.082Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "ProductsCatalogController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\ProductsCatalogController.cs", "RelativeDocumentMoniker": "AdminPanel\\Controllers\\ProductsCatalogController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Controllers\\ProductsCatalogController.cs", "RelativeToolTip": "AdminPanel\\Controllers\\ProductsCatalogController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:30:40.463Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "ProductServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ProductServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\Services\\ProductServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\ProductServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\Services\\ProductServicesDAL.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAiwBMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T08:16:28.188Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "IProductServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IProductServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\IServices\\IProductServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IProductServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\IServices\\IProductServicesDAL.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAuwBgAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T10:01:27.136Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ApiProductsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "RelativeDocumentMoniker": "AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "RelativeToolTip": "AdminPanel\\Areas\\V1\\Controllers\\ApiProductsController.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAYwCsAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T17:11:23.025Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "UpdateProduct.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "RelativeToolTip": "AdminPanel\\Views\\ProductsCatalog\\UpdateProduct.cshtml", "ViewState": "AgIAABgHAAAAAAAAAAAQwDAHAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-31T07:57:42.375Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "CreateNewProduct.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "RelativeDocumentMoniker": "AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "RelativeToolTip": "AdminPanel\\Views\\ProductsCatalog\\CreateNewProduct.cshtml", "ViewState": "AgIAAHgAAAAAAAAAAAAAAIgAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-31T07:46:43.483Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "BasicDataServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\Services\\BasicDataServicesDAL.cs", "ViewState": "AgIAAMECAAAAAAAAAAAcwKECAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:39:33.529Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "IBasicDataServicesDAL.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "RelativeDocumentMoniker": "DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "RelativeToolTip": "DAL\\Repository\\IServices\\IBasicDataServicesDAL.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAlwBUAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:33:43.47Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "producttype.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\producttype.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\producttype.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\producttype.cs", "RelativeToolTip": "Entities\\DBModels\\producttype.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:28:37.743Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Product.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Product.cs", "RelativeToolTip": "Entities\\DBModels\\Product.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAMwBsAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:25:09.494Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "Manufacturer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Manufacturer.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\Manufacturer.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\Manufacturer.cs", "RelativeToolTip": "Entities\\DBModels\\Manufacturer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:24:15.865Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "InheritedEntitiesLevelOne.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "RelativeDocumentMoniker": "Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "RelativeToolTip": "Entities\\DBInheritedModels\\InheritedEntitiesLevelOne.cs", "ViewState": "AgIAAFgAAAAAAAAAAAAmwGkAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:34:38.358Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "BankStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\BankStatus.cs", "RelativeDocumentMoniker": "Entities\\DBModels\\BankStatus.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\Entities\\DBModels\\BankStatus.cs", "RelativeToolTip": "Entities\\DBModels\\BankStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T07:24:54.019Z"}]}]}]}