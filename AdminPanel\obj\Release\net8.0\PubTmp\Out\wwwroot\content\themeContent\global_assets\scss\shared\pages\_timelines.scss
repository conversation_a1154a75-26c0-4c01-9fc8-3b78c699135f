/* ------------------------------------------------------------------------------
 *
 *  # Timeline
 *
 *  Styles for timeline in 3 layouts: left, right and centered
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-timeline {

	// Base
	.timeline {
		position: relative;

		// Dots base
		&:before,
		&:after {
			content: "";
			position: absolute;
			left: 50%;
			margin-left: -($timeline-line-width);
			background-color: $timeline-line-color;
			z-index: 1;
			@include size($timeline-line-width * 2);
			@include border-radius($border-radius-circle);
		}

		// Top dot
		&:before {
			top: 0;
		}

		// Bottom dot
		&:after {
			bottom: 0;
		}
	}

	// Container
	.timeline-container {
		position: relative;
		padding-top: ($spacer / 2);
		margin-top: -($spacer / 2);
		padding-bottom: 1px;

		// Vertical line
		&:before {
			content: "";
			position: absolute;
			top: 0;
			left: 50%;
			margin-left: -($timeline-line-width / 2);
			background-color: $timeline-line-color;
			height: 100%;
			width: $timeline-line-width;
		}
	}

	// Row
	.timeline-row {
		position: relative;
	}

	// Date
	.timeline-date {
		text-align: center;
		background-color: $body-bg;
		position: relative;
		z-index: 1;
		padding-top: $spacer;
		padding-bottom: $spacer;
		margin-bottom: $spacer;

		// Dots base
		&:before,
		&:after {
			content: "";
			position: absolute;
			left: 50%;
			margin-left: -($timeline-line-width);
			background-color: $timeline-line-color;
			z-index: 1;
			@include size($timeline-line-width * 2);
			@include border-radius($border-radius-circle);
		}

		// Top dot
		&:before {
			top: 0;
		}

		// Bottom dot
		&:after {
			bottom: 0;
		}

		// Change bg color to card bg if inside card
		.card & {
			background-color: $card-bg;
		}
	}

	// Time
	.timeline-time {
		text-align: center;
		padding-top: $spacer;
		padding-bottom: $spacer;
		background-color: $body-bg;
		position: relative;
		margin-bottom: $spacer;

		// Dots base
		&:before,
		&:after {
			content: "";
			position: absolute;
			left: 50%;
			margin-left: -($timeline-line-width);
			background-color: $timeline-line-color;
			@include size($timeline-line-width * 2);
			@include border-radius($border-radius-circle);
		}

		// Top dot
		&:before {
			top: 0;
		}

		// Bottom dot
		&:after {
			bottom: 0;
		}

		// Change bg color to card bg if inside card
		.card & {
			background-color: $card-bg;
		}

		// Hide dots no desktop
		@include media-breakpoint-up(md) {
			&:before,
			&:after {
				content: none;
			}
		}
	}

	// Icon and image
	.timeline-icon {
		margin: 0 auto $spacer auto;
		background-color: $timeline-icon-bg;
		border: $timeline-icon-border-width solid $body-bg;
		@include size($timeline-icon-size);
		@include border-radius($border-radius-circle);

		// Change border color if it's inside card
		.card & {
			border-color: $card-bg;
		}

		// Icon
		div {
			height: 100%;
			display: flex;
		    align-items: center;
		    justify-content: center;
			box-shadow: 0 0 0 $timeline-line-width $timeline-line-color inset;
			@include border-radius($border-radius-circle);

			// Hide border if icon wrapper has dark color
			&[class*=bg-]:not(.bg-white):not(.bg-light):not(.bg-transparent) {
				box-shadow: none;
			}

			// Icon
			> i {
				top: 0;
			}
		}

		// Image
		img {
			@include size($timeline-icon-size - rem-calc($timeline-icon-border-width * 2));
			@include border-radius($border-radius-circle);
		}
	}


	//
	// Responsive config
	//

	@include media-breakpoint-up(md) {


		// Positions
		// ------------------------------

		// Center layout
		.timeline-center {

			// Left blocks
			.timeline-row-left {
				margin-right: 50%;
				padding-right: $timeline-icon-size - rem-calc($timeline-icon-border-width);
			}

			// Right blocks
			.timeline-row-right {
				margin-left: 50%;
				padding-left: $timeline-icon-size - rem-calc($timeline-icon-border-width);
			}
		}

		// Left layout
		.timeline-left {

			// Vertical line
			.timeline-container:after,
			&:before,
			&:after,
			.timeline-date:before,
			.timeline-date:after {
				left: ($timeline-icon-size / 2);
			}

			// Content padding
			.timeline-row,
			.timeline-date {
				padding-left: ($timeline-icon-size + $timeline-content-padding-x);
			}
		}

		// Right layout
		.timeline-right {

			// Vertical line
			.timeline-container:after,
			&:before,
			&:after,
			.timeline-date:before,
			.timeline-date:after {
				left: auto;
				right: ($timeline-icon-size / 2);
				margin-left: 0;
				margin-right: -($timeline-line-width);
			}
			.timeline-container:after {
				margin-right: -($timeline-line-width / 2);
			}

			// Content padding
			.timeline-row,
			.timeline-date {
				padding-right: ($timeline-icon-size + $timeline-content-padding-x);
			}
		}

		// Reduce date vertical spacing in both
		.timeline-left .timeline-date,
		.timeline-right .timeline-date {
			padding-top: ($spacer / 2);
			padding-bottom: ($spacer / 2);
		}


		// Icon
		// ------------------------------

		// Base
		.timeline-icon {
			position: absolute;
			top: ($spacer / 2);

			// Horizontal line base
			&:after {
				content: "";
				position: absolute;
				top: 50%;
				margin-top: -($timeline-line-width / 2);
				height: $timeline-line-width;
				width: ($timeline-icon-size / 2) - rem-calc($timeline-icon-border-width * 2);
				background-color: $timeline-line-color;
				z-index: 1;
			}
		}

		// In left layout
		.timeline-left {
			.timeline-icon {
				left: 0;

				// Right line
				&:after {
					left: 100%;
					margin-left: $timeline-icon-border-width;
				}
			}
		}

		// In right layout
		.timeline-right {
			.timeline-icon {
				right: 0;

				// Left line
				&:after {
					right: 100%;
					margin-right: $timeline-icon-border-width;
				}
			}
		}

		// In centered layout
		.timeline-center {

			// In left block
			.timeline-row-left {
				.timeline-icon {
					left: 100%;
					margin-left: -($timeline-icon-size / 2);

					// Left horizontal line
					&:after {
						right: 100%;
						margin-right: $timeline-icon-border-width;
					}
				}
			}

			// In right block
			.timeline-row-right {
				.timeline-icon {
					right: 100%;
					margin-right: -($timeline-icon-size / 2);

					// Right horizontal line
					&:after {
						left: 100%;
						margin-left: $timeline-icon-border-width;
					}
				}
			}

			// Full width post
			.timeline-row-full .timeline-icon {
				position: static;

				// Remove horizontal line
				&:after {
					content: none;
				}
			}
		}


		// Time
		// ------------------------------

		.timeline-time {
			padding: 0;
			text-align: inherit;
			background-color: transparent;

			// Hide dots
			&:before {
				content: none;
			}
		}

		// In left and right layouts
		.timeline-left,
		.timeline-right {
			.timeline-time {
				padding-top: ($spacer / 2);
				margin-bottom: $spacer;
				padding-left: ($timeline-content-padding-x / 2);
			}
		}
		.timeline-right .timeline-time {
			text-align: right;
			padding-left: 0;
			padding-right: ($timeline-content-padding-x / 2);
		}

		// In center layout
		.timeline-center {
			.timeline-time,
			.timeline-row-full .timeline-time {
				position: absolute;
				left: 100%;
				top: ($spacer / 2) + rem-calc($timeline-icon-border-width);
				width: 100%;
				padding-left: $timeline-icon-size - rem-calc($timeline-icon-border-width);
			}

			// Right aligned post
			.timeline-row-right,
			.timeline-row-full {
				.timeline-time {
					left: auto;
					right: 100%;
					padding-left: 0;
					padding-right: $timeline-icon-size - rem-calc($timeline-icon-border-width);
					text-align: right;
				}
			}

			// Full width post
			.timeline-row-full .timeline-time {
				right: 50%;
				top: $timeline-icon-border-width;
			}
		}
	}
}
