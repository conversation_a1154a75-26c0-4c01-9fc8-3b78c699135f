/* ------------------------------------------------------------------------------
 *
 *  # Helper classes
 *
 *  Custom helper classes used in the template.
 *
 * ---------------------------------------------------------------------------- */


// Content helpers
// -------------------------

// Font size
.font-size-lg {
    font-size: $font-size-lg;
}
.font-size-base {
    font-size: $font-size-base;
}
.font-size-sm {
    font-size: $font-size-sm;
}
.font-size-xs {
    font-size: $font-size-xs;
}

// Line height
.line-height-lg {
    line-height: $line-height-lg;
}
.line-height-base {
    line-height: $line-height-base;
}
.line-height-sm {
    line-height: $line-height-sm;
}
.line-height-xs {
    line-height: $line-height-xs;
}
.line-height-1 {
    line-height: 1;
}

// Opacity
.opacity-75 {
    opacity: 0.75;
}
.opacity-50 {
    opacity: 0.5;
}
.opacity-25 {
    opacity: 0.25;
}
.opacity-0 {
    opacity: 0;
}

// Edges
.top {
    &-0 {
        top: 0;
    }
    &-auto {
        top: auto;
    }
}
.bottom {
    &-0 {
        bottom: 0;
    }
    &-auto {
        bottom: auto;
    }
}
.left {
    &-0 {
        left: 0;
    }
    &-auto {
        left: auto;
    }
}
.right {
    &-0 {
        right: 0;
    }
    &-auto {
        right: auto;
    }
}

// Cursors
.cursor-pointer {
    cursor: pointer;
}
.cursor-move {
    cursor: move;
}
.cursor-default {
    cursor: default;
}
.cursor-disabled {
    cursor: $cursor-disabled;
}

// Overflow
.overflow-hidden {
    overflow: hidden;
}
.overflow-visible {
    overflow: visible;
}
.overflow-auto {
    overflow: auto;
}



// Borders
// -------------------------

// Set border width to 0
.border-width-0 {
    border-width: 0;
}

// Vertical borders
.border-y-0 {
    border-top: 0 !important;
    border-bottom: 0 !important;
}

// Horizontal borders
.border-x-0 {
    border-left: 0 !important;
    border-right: 0 !important;
}


//
// Border widths
//

// Default border width
.border-1 {
    border: $border-width solid;
}
.border-top-1 {
    border-top: $border-width solid;
}
.border-bottom-1 {
    border-bottom: $border-width solid;
}
.border-left-1 {
    border-left: $border-width solid;
}
.border-right-1 {
    border-right: $border-width solid;
}
.border-y-1 {
    border-top: $border-width solid;
    border-bottom: $border-width solid;
}
.border-x-1 {
    border-left: $border-width solid;
    border-right: $border-width solid;
}

// Double border
.border-2 {
    border: ($border-width * 2) solid;
}
.border-top-2 {
    border-top: ($border-width * 2) solid;
}
.border-bottom-2 {
    border-bottom: ($border-width * 2) solid;
}
.border-left-2 {
    border-left: ($border-width * 2) solid;
}
.border-right-2 {
    border-right: ($border-width * 2) solid;
}
.border-y-2 {
    border-top: ($border-width * 2) solid;
    border-bottom: ($border-width * 2) solid;
}
.border-x-2 {
    border-left: ($border-width * 2) solid;
    border-right: ($border-width * 2) solid;
}

// Tripple border
.border-3 {
    border: ($border-width * 3) solid;
}
.border-top-3 {
    border-top: ($border-width * 3) solid;
}
.border-bottom-3 {
    border-bottom: ($border-width * 3) solid;
}
.border-left-3 {
    border-left: ($border-width * 3) solid;
}
.border-right-3 {
    border-right: ($border-width * 3) solid;
}
.border-y-3 {
    border-top: ($border-width * 3) solid;
    border-bottom: ($border-width * 3) solid;
}
.border-x-3 {
    border-left: ($border-width * 3) solid;
    border-right: ($border-width * 3) solid;
}

// Border style
.border {

    // Dashed
    &-dashed {
        border-style: dashed;
    }
    &-top-dashed {
        border-top-style: dashed;
    }
    &-bottom-dashed {
        border-bottom-style: dashed;
    }
    &-left-dashed {
        border-left-style: dashed;
    }
    &-right-dashed {
        border-right-style: dashed;
    }

    // Dotted
    &-dotted {
        border-style: dotted;
    }
}



// Rounded corners
// -------------------------

// Fully rounded borders
.rounded-round {
    @include border-radius($border-radius-round !important);
}
.rounded-top-round {
    @include border-top-radius($border-radius-round !important);
}
.rounded-bottom-round {
    @include border-bottom-radius($border-radius-round !important);
}
.rounded-left-round {
    @include border-left-radius($border-radius-round !important);
}
.rounded-right-round {
    @include border-right-radius($border-radius-round !important);
}


//
// Remove rounded corners from specific side
//

.rounded-top-0 {
    @include border-top-radius(0 !important);
}
.rounded-bottom-0 {
    @include border-bottom-radius(0 !important);
}
.rounded-left-0 {
    @include border-left-radius(0 !important);
}
.rounded-right-0 {
    @include border-right-radius(0 !important);
}



// Transforms
// -------------------------

//
// Rotation
//

// Basic direction
.rotate-45 {
    transform: rotate(45deg);
}
.rotate-90 {
    transform: rotate(90deg);
}
.rotate-180 {
    transform: rotate(180deg);
}

// Inversed
.rotate-45-inverse {
    transform: rotate(-45deg);
}
.rotate-90-inverse {
    transform: rotate(-90deg);
}
.rotate-180-inverse {
    transform: rotate(-180deg);
}


//
// Spinner animation
//

// Default
.spinner {
    display: inline-block;
    animation: rotation 1s linear infinite;
}

// Reversed
.spinner-reverse {
    display: inline-block;
    animation: rotation_reverse 1s linear infinite;
}

// Default rotation
@-webkit-keyframes rotation {
    0% {-webkit-transform: rotate(0deg);}
    100% {-webkit-transform: rotate(360deg);}
}
@-moz-keyframes rotation {
    0% {-moz-transform: rotate(0deg);}
    100% {-moz-transform: rotate(360deg);}
}
@-ms-keyframes rotation {
    0% {-ms-transform: rotate(0deg);}
    100% {-ms-transform: rotate(360deg);}
}
@-o-keyframes rotation {
    0% {-o-transform: rotate(0deg);}
    100% {-o-transform: rotate(360deg);}
}
@keyframes rotation {
    0% {transform: rotate(0deg);}
    100% {transform: rotate(360deg);}
}

// Inversed rotation
@-webkit-keyframes rotation_reverse {
    0% {-webkit-transform: rotate(0deg);}
    100% {-webkit-transform: rotate(-360deg);}
}
@-moz-keyframes rotation_reverse {
    0% {-moz-transform: rotate(0deg);}
    100% {-moz-transform: rotate(-360deg);}
}
@-ms-keyframes rotation_reverse {
    0% {-ms-transform: rotate(0deg);}
    100% {-ms-transform: rotate(-360deg);}
}
@-o-keyframes rotation_reverse {
    0% {-o-transform: rotate(0deg);}
    100% {-o-transform: rotate(-360deg);}
}
@keyframes rotation_reverse {
    0% {transform: rotate(0deg);}
    100% {transform: rotate(-360deg);}
}



// Sizing
// -------------------------

// Auto height
.h-auto {
    height: auto;
}

// Element widths
@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);
        .w#{$infix}-auto { width: auto !important; }

        .w#{$infix}-25 { width: 25% !important; }
        .w#{$infix}-50 { width: 50% !important; }
        .w#{$infix}-75 { width: 75% !important; }
        .w#{$infix}-100 { width: 100% !important; }

        .wmin#{$infix}-0 { min-width: 0 !important; }
        .wmin#{$infix}-200 { min-width: 200px !important; }
        .wmin#{$infix}-250 { min-width: 250px !important; }
        .wmin#{$infix}-300 { min-width: 300px !important; }
        .wmin#{$infix}-350 { min-width: 350px !important; }
        .wmin#{$infix}-400 { min-width: 400px !important; }
        .wmin#{$infix}-450 { min-width: 450px !important; }
        .wmin#{$infix}-500 { min-width: 500px !important; }
        .wmin#{$infix}-550 { min-width: 550px !important; }
        .wmin#{$infix}-600 { min-width: 600px !important; }

        .flex#{$infix}-1 { flex: 1; }
    }
}



// Misc
// -------------------------

// Disable all transitions
.no-transitions * {
    &,
    &:before,
    &:after {
        @include transition(none !important);
    }
}

// Remove caret from .dropdown-toggle element
.caret-0 {
    &::before,
    &::after {
        content: none;
    }
}

// Remove box shadow
.shadow-0 {
    box-shadow: none !important;
}

// Remove outline
.outline-0 {
    @include plain-hover-focus {
        outline: 0;
    }
}

// Text shadow
.text-shadow-dark {
    text-shadow: 0 0 0.1875rem rgba($black, 0.5);
}
.text-shadow-light {
    text-shadow: 0 0 0.1875rem rgba($white, 0.5);
}


/* ------------------------------
 *
 *  Theme specific styles
 *
 * ------------------------------ */

// Material theme
@if $theme == 'material' {

    // Shadows
    .shadow-1 {
        box-shadow: $shadow-depth1;
    }
    .shadow-2 {
        box-shadow: $shadow-depth2;
    }
    .shadow-3 {
        box-shadow: $shadow-depth3;
    }
    .shadow-4 {
        box-shadow: $shadow-depth4;
    }
    .shadow-5 {
        box-shadow: $shadow-depth5;
    }
}
