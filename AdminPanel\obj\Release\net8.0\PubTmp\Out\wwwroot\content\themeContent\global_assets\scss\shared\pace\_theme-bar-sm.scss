/* ------------------------------------------------------------------------------
 *
 *  # Pace. Progress bar theme
 *
 *  Bar css spinner theme for Pace. Default size
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-pace {

    // Define variables
    $pace-show-text: true;
    $pace-overlay-color: $color-slate-900;
    $pace-loader-color: $color-success-400;
    $pace-loader-width: 14rem;
    $pace-loader-height: 0.25rem;
    $pace-loader-spacer: 0.0625rem;


    // Pace theme styles
    // ------------------------------

    // Overlay
    .pace-running {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: $pace-overlay-color;

        // Hide all content
        > *:not(.pace) {
            opacity: 0;
        }
    }

    // Base
    .pace {
        position: fixed;
        margin: auto;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: $pace-loader-width;
        height: $pace-loader-height;
        background-color: rgba($black, 0.4);
        border-radius: $border-radius-round;
        background-clip: padding-box;
        pointer-events: none;
        user-select: none;
        z-index: 9999;

        // Change colors on light/dark backgrounds
        @if (lightness($pace-overlay-color) < 75) {
            background-color: rgba($black, 0.4);
            color: $white;
        }
        @else {
            background-color: rgba($black, 0.8);
        }
    }

    // Progress
    .pace-progress {
        max-width: ($pace-loader-width - ($pace-loader-spacer * 2));
        z-index: 9999;
        display: block;
        position: absolute;
        left: $pace-loader-spacer;
        top: $pace-loader-spacer;
        height: ($pace-loader-height - ($pace-loader-spacer * 2));
        background-color: $pace-loader-color;
        font-size: $font-size-sm;
        border-radius: $border-radius-round;
        background-clip: padding-box;
        width: 0;
    }

    // Hide inactive
    .pace-inactive {
        display: none;
    }

    // Progress text
    @if $pace-show-text {
        .pace-progress:after {
            content: attr(data-progress-text);
            text-align: center;
            width: 100%;
            display: inline-block;
            white-space: nowrap;
            margin-top: $pace-loader-height + 0.5rem;
        }
    }
}
