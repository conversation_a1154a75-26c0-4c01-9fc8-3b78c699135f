/* ------------------------------------------------------------------------------
 *
 *  # Sticky kit
 *
 *  Styles for sticky.min.js - extension that makes elements sticky
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-sticky {

    // Apply correct z-index to sticked element
    .is_stuck {
        z-index: $zindex-fixed !important;
    }

    .navbar-top {
        .navbar.is_stuck {
            margin-top: $nav-link-height + rem-calc($navbar-border-width * 2);
        }
    }
}
