/* ------------------------------------------------------------------------------
 *
 *  # Perfect Scrollbar
 *
 *  Styles for perfect_scrollbar.min.js - custom scrollbar extension
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-scrollbar {

    // Container
    .ps {
        overflow: hidden !important;
        overflow-anchor: none;
        -ms-overflow-style: none;
        touch-action: auto;
        -ms-touch-action: auto;
    }


    //
    // Scrollbar rail styles
    //

    // Base
    .ps__rail-y,
    .ps__rail-x {
        opacity: 0;
        position: absolute;
        @include transition(opacity $component-transition-timer linear);
    }
    .ps__rail-x {
        bottom: 0;
    }
    .ps__rail-y {
        /*rtl:ignore*/
        right: 0;
    }

    // Hover state
    .ps:hover > .ps__rail-x,
    .ps:hover > .ps__rail-y,
    .ps--focus > .ps__rail-x,
    .ps--focus > .ps__rail-y,
    .ps--scrolling-x > .ps__rail-x,
    .ps--scrolling-y > .ps__rail-y {
        opacity: $ps-opacity;
    }

    
    //
    // Scrollbar thumb styles
    //

    // Base
    .ps__thumb-y,
    .ps__thumb-x {
        background-color: $ps-bg;
        position: absolute;
        pointer-events: none;
        @include border-radius($border-radius-round);
    }
    .ps__thumb-y {
        width: $ps-size;
        right: $ps-gap;
    }
    .ps__thumb-x {
        height: $ps-size;
        bottom: $ps-gap;
    }


    //
    // MS supports
    //

    @supports (-ms-overflow-style: none) {
        .ps {
            overflow: auto !important;
        }
    }

    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        .ps {
            overflow: auto !important;
        }
    }
}
