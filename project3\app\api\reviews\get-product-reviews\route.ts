import { NextRequest, NextResponse } from 'next/server';
import { Config } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    console.log('🔍 Get Product Reviews API: Received request body:', body);

    // Prepare headers for the remote API call
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Extract JWT token from Authorization header (optional for public reviews)
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;

    // Add JWT token to headers if available (for potential user-specific features)
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      headers['Token'] = token;
      console.log('🔐 Get Product Reviews API: Added JWT token to headers');
    } else {
      console.log('ℹ️ Get Product Reviews API: No JWT token provided (public request)');
    }

    console.log('🔍 Get Product Reviews API: Request body:', body);

    // Forward the request to the remote API
    const response = await fetch(
      `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-product-reviews`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      }
    );

    console.log('🔍 Get Product Reviews API: Backend response status:', response.status);

    const data = await response.json();
    console.log('🔍 Get Product Reviews API: Backend response data:', data);

    if (!response.ok) {
      console.error('❌ Get Product Reviews API: External API error:', data);
      return NextResponse.json(
        { 
          error: 'Failed to fetch product reviews', 
          details: data,
          errorMessage: data.message || 'Failed to fetch product reviews'
        },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('❌ Get Product Reviews API: Route error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'An error occurred while fetching product reviews.',
        errorMessage: 'Internal server error'
      },
      { status: 500 }
    );
  }
}
