/* ------------------------------------------------------------------------------
 *
 *  # Custom form inputs
 *
 *  Custom checkboxes, radios, selects and file inputs
 *
 * ---------------------------------------------------------------------------- */


//
// Checkboxes and radios
//

// Container
.custom-control {
    margin-bottom: $form-check-margin-y;

    // Last item doesn't have spacing
    &:last-child {
        margin-bottom: 0;
    }
}

// Inline list
.custom-control-inline {
    margin-bottom: 0;

    // Last item doesn't have spacing
    &:last-child {
        margin-right: 0;
    }
}

// Input
.custom-control-input {

    // Active state
    &:active ~ .custom-control-label::before {
        @include transition(all ease-in-out $component-transition-timer);
    }

    // Disabled state
    &:disabled {
        ~ .custom-control-label {
            cursor: $cursor-disabled;
        }
    }
}

// Custom control indicators
.custom-control-label {
    cursor: pointer;

    // Background
    &::before {
        top: (($line-height-computed - $custom-control-indicator-size) / 2);
    }

    // Foreground (icon)
    &::after {
        top: (($line-height-computed - $custom-control-indicator-size) / 2);
    }
}

// Right input alignment
.custom-control-right {
    padding-left: 0;
    padding-right: ($custom-control-gutter + $custom-control-indicator-size);

    // Input
    .custom-control-input {
        right: 0;
    }

    // Label
    .custom-control-label {
        &:before,
        &:after {
            left: auto;
            right: 0;
        }
    }
}


//
// Select
//

.custom-select {
    cursor: pointer;
    @include transition(all ease-in-out $component-transition-timer);

    // Hover state
    @include hover-focus {
        @include box-shadow($hover-shadow-lighter);
    }

    // Disabled state
    &:disabled {
        cursor: $cursor-disabled;

        // Hover state
        @include hover-focus {
            @include box-shadow(none);
        }
    }
}


//
// File
//

.custom-file-label {
    margin-bottom: 0;
}
