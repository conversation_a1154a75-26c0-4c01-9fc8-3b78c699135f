/* ------------------------------------------------------------------------------
 *
 *  # Material icon classes
 *
 *  Material set uses the Unicode Private Use Area (PUA) to ensure screen
 *  readers do not read off random characters that represent icons
 *
 * ---------------------------------------------------------------------------- */

.#{$mi-css-prefix}-3d-rotation:before { content: mi-content($mi-var-3d-rotation); }
.#{$mi-css-prefix}-ac-unit:before { content: mi-content($mi-var-ac-unit); }
.#{$mi-css-prefix}-alarm:before { content: mi-content($mi-var-alarm); }
.#{$mi-css-prefix}-access-alarms:before { content: mi-content($mi-var-access-alarms); }
.#{$mi-css-prefix}-schedule:before { content: mi-content($mi-var-schedule); }
.#{$mi-css-prefix}-accessibility:before { content: mi-content($mi-var-accessibility); }
.#{$mi-css-prefix}-accessible:before { content: mi-content($mi-var-accessible); }
.#{$mi-css-prefix}-account-balance:before { content: mi-content($mi-var-account-balance); }
.#{$mi-css-prefix}-account-balance-wallet:before { content: mi-content($mi-var-account-balance-wallet); }
.#{$mi-css-prefix}-account-box:before { content: mi-content($mi-var-account-box); }
.#{$mi-css-prefix}-account-circle:before { content: mi-content($mi-var-account-circle); }
.#{$mi-css-prefix}-adb:before { content: mi-content($mi-var-adb); }
.#{$mi-css-prefix}-add:before { content: mi-content($mi-var-add); }
.#{$mi-css-prefix}-add-a-photo:before { content: mi-content($mi-var-add-a-photo); }
.#{$mi-css-prefix}-alarm-add:before { content: mi-content($mi-var-alarm-add); }
.#{$mi-css-prefix}-add-alert:before { content: mi-content($mi-var-add-alert); }
.#{$mi-css-prefix}-add-box:before { content: mi-content($mi-var-add-box); }
.#{$mi-css-prefix}-add-circle:before { content: mi-content($mi-var-add-circle); }
.#{$mi-css-prefix}-control-point:before { content: mi-content($mi-var-control-point); }
.#{$mi-css-prefix}-add-location:before { content: mi-content($mi-var-add-location); }
.#{$mi-css-prefix}-add-shopping-cart:before { content: mi-content($mi-var-add-shopping-cart); }
.#{$mi-css-prefix}-queue:before { content: mi-content($mi-var-queue); }
.#{$mi-css-prefix}-add-to-queue:before { content: mi-content($mi-var-add-to-queue); }
.#{$mi-css-prefix}-adjust:before { content: mi-content($mi-var-adjust); }
.#{$mi-css-prefix}-airline-seat-flat:before { content: mi-content($mi-var-airline-seat-flat); }
.#{$mi-css-prefix}-airline-seat-flat-angled:before { content: mi-content($mi-var-airline-seat-flat-angled); }
.#{$mi-css-prefix}-airline-seat-individual-suite:before { content: mi-content($mi-var-airline-seat-individual-suite); }
.#{$mi-css-prefix}-airline-seat-legroom-extra:before { content: mi-content($mi-var-airline-seat-legroom-extra); }
.#{$mi-css-prefix}-airline-seat-legroom-normal:before { content: mi-content($mi-var-airline-seat-legroom-normal); }
.#{$mi-css-prefix}-airline-seat-legroom-reduced:before { content: mi-content($mi-var-airline-seat-legroom-reduced); }
.#{$mi-css-prefix}-airline-seat-recline-extra:before { content: mi-content($mi-var-airline-seat-recline-extra); }
.#{$mi-css-prefix}-airline-seat-recline-normal:before { content: mi-content($mi-var-airline-seat-recline-normal); }
.#{$mi-css-prefix}-flight:before { content: mi-content($mi-var-flight); }
.#{$mi-css-prefix}-airplanemode-inactive:before { content: mi-content($mi-var-airplanemode-inactive); }
.#{$mi-css-prefix}-airplay:before { content: mi-content($mi-var-airplay); }
.#{$mi-css-prefix}-airport-shuttle:before { content: mi-content($mi-var-airport-shuttle); }
.#{$mi-css-prefix}-alarm-off:before { content: mi-content($mi-var-alarm-off); }
.#{$mi-css-prefix}-alarm-on:before { content: mi-content($mi-var-alarm-on); }
.#{$mi-css-prefix}-album:before { content: mi-content($mi-var-album); }
.#{$mi-css-prefix}-all-inclusive:before { content: mi-content($mi-var-all-inclusive); }
.#{$mi-css-prefix}-all-out:before { content: mi-content($mi-var-all-out); }
.#{$mi-css-prefix}-android:before { content: mi-content($mi-var-android); }
.#{$mi-css-prefix}-announcement:before { content: mi-content($mi-var-announcement); }
.#{$mi-css-prefix}-apps:before { content: mi-content($mi-var-apps); }
.#{$mi-css-prefix}-archive:before { content: mi-content($mi-var-archive); }
.#{$mi-css-prefix}-arrow-back:before { content: mi-content($mi-var-arrow-back); }
.#{$mi-css-prefix}-arrow-downward:before { content: mi-content($mi-var-arrow-downward); }
.#{$mi-css-prefix}-arrow-drop-down:before { content: mi-content($mi-var-arrow-drop-down); }
.#{$mi-css-prefix}-arrow-drop-down-circle:before { content: mi-content($mi-var-arrow-drop-down-circle); }
.#{$mi-css-prefix}-arrow-drop-up:before { content: mi-content($mi-var-arrow-drop-up); }
.#{$mi-css-prefix}-arrow-forward:before { content: mi-content($mi-var-arrow-forward); }
.#{$mi-css-prefix}-arrow-upward:before { content: mi-content($mi-var-arrow-upward); }
.#{$mi-css-prefix}-art-track:before { content: mi-content($mi-var-art-track); }
.#{$mi-css-prefix}-aspect-ratio:before { content: mi-content($mi-var-aspect-ratio); }
.#{$mi-css-prefix}-poll:before { content: mi-content($mi-var-poll); }
.#{$mi-css-prefix}-assignment:before { content: mi-content($mi-var-assignment); }
.#{$mi-css-prefix}-assignment-ind:before { content: mi-content($mi-var-assignment-ind); }
.#{$mi-css-prefix}-assignment-late:before { content: mi-content($mi-var-assignment-late); }
.#{$mi-css-prefix}-assignment-return:before { content: mi-content($mi-var-assignment-return); }
.#{$mi-css-prefix}-assignment-returned:before { content: mi-content($mi-var-assignment-returned); }
.#{$mi-css-prefix}-assignment-turned-in:before { content: mi-content($mi-var-assignment-turned-in); }
.#{$mi-css-prefix}-assistant:before { content: mi-content($mi-var-assistant); }
.#{$mi-css-prefix}-flag:before { content: mi-content($mi-var-flag); }
.#{$mi-css-prefix}-attach-file:before { content: mi-content($mi-var-attach-file); }
.#{$mi-css-prefix}-attach-money:before { content: mi-content($mi-var-attach-money); }
.#{$mi-css-prefix}-attachment:before { content: mi-content($mi-var-attachment); }
.#{$mi-css-prefix}-audiotrack:before { content: mi-content($mi-var-audiotrack); }
.#{$mi-css-prefix}-autorenew:before { content: mi-content($mi-var-autorenew); }
.#{$mi-css-prefix}-av-timer:before { content: mi-content($mi-var-av-timer); }
.#{$mi-css-prefix}-backspace:before { content: mi-content($mi-var-backspace); }
.#{$mi-css-prefix}-cloud-upload:before { content: mi-content($mi-var-cloud-upload); }
.#{$mi-css-prefix}-battery-alert:before { content: mi-content($mi-var-battery-alert); }
.#{$mi-css-prefix}-battery-charging-full:before { content: mi-content($mi-var-battery-charging-full); }
.#{$mi-css-prefix}-battery-std:before { content: mi-content($mi-var-battery-std); }
.#{$mi-css-prefix}-battery-unknown:before { content: mi-content($mi-var-battery-unknown); }
.#{$mi-css-prefix}-beach-access:before { content: mi-content($mi-var-beach-access); }
.#{$mi-css-prefix}-beenhere:before { content: mi-content($mi-var-beenhere); }
.#{$mi-css-prefix}-block:before { content: mi-content($mi-var-block); }
.#{$mi-css-prefix}-bluetooth:before { content: mi-content($mi-var-bluetooth); }
.#{$mi-css-prefix}-bluetooth-searching:before { content: mi-content($mi-var-bluetooth-searching); }
.#{$mi-css-prefix}-bluetooth-connected:before { content: mi-content($mi-var-bluetooth-connected); }
.#{$mi-css-prefix}-bluetooth-disabled:before { content: mi-content($mi-var-bluetooth-disabled); }
.#{$mi-css-prefix}-blur-circular:before { content: mi-content($mi-var-blur-circular); }
.#{$mi-css-prefix}-blur-linear:before { content: mi-content($mi-var-blur-linear); }
.#{$mi-css-prefix}-blur-off:before { content: mi-content($mi-var-blur-off); }
.#{$mi-css-prefix}-blur-on:before { content: mi-content($mi-var-blur-on); }
.#{$mi-css-prefix}-class:before { content: mi-content($mi-var-class); }
.#{$mi-css-prefix}-turned-in:before { content: mi-content($mi-var-turned-in); }
.#{$mi-css-prefix}-turned-in-not:before { content: mi-content($mi-var-turned-in-not); }
.#{$mi-css-prefix}-border-all:before { content: mi-content($mi-var-border-all); }
.#{$mi-css-prefix}-border-bottom:before { content: mi-content($mi-var-border-bottom); }
.#{$mi-css-prefix}-border-clear:before { content: mi-content($mi-var-border-clear); }
.#{$mi-css-prefix}-border-color:before { content: mi-content($mi-var-border-color); }
.#{$mi-css-prefix}-border-horizontal:before { content: mi-content($mi-var-border-horizontal); }
.#{$mi-css-prefix}-border-inner:before { content: mi-content($mi-var-border-inner); }
.#{$mi-css-prefix}-border-left:before { content: mi-content($mi-var-border-left); }
.#{$mi-css-prefix}-border-outer:before { content: mi-content($mi-var-border-outer); }
.#{$mi-css-prefix}-border-right:before { content: mi-content($mi-var-border-right); }
.#{$mi-css-prefix}-border-style:before { content: mi-content($mi-var-border-style); }
.#{$mi-css-prefix}-border-top:before { content: mi-content($mi-var-border-top); }
.#{$mi-css-prefix}-border-vertical:before { content: mi-content($mi-var-border-vertical); }
.#{$mi-css-prefix}-branding-watermark:before { content: mi-content($mi-var-branding-watermark); }
.#{$mi-css-prefix}-brightness-1:before { content: mi-content($mi-var-brightness-1); }
.#{$mi-css-prefix}-brightness-2:before { content: mi-content($mi-var-brightness-2); }
.#{$mi-css-prefix}-brightness-3:before { content: mi-content($mi-var-brightness-3); }
.#{$mi-css-prefix}-brightness-4:before { content: mi-content($mi-var-brightness-4); }
.#{$mi-css-prefix}-brightness-low:before { content: mi-content($mi-var-brightness-low); }
.#{$mi-css-prefix}-brightness-medium:before { content: mi-content($mi-var-brightness-medium); }
.#{$mi-css-prefix}-brightness-high:before { content: mi-content($mi-var-brightness-high); }
.#{$mi-css-prefix}-brightness-auto:before { content: mi-content($mi-var-brightness-auto); }
.#{$mi-css-prefix}-broken-image:before { content: mi-content($mi-var-broken-image); }
.#{$mi-css-prefix}-brush:before { content: mi-content($mi-var-brush); }
.#{$mi-css-prefix}-bubble-chart:before { content: mi-content($mi-var-bubble-chart); }
.#{$mi-css-prefix}-bug-report:before { content: mi-content($mi-var-bug-report); }
.#{$mi-css-prefix}-build:before { content: mi-content($mi-var-build); }
.#{$mi-css-prefix}-burst-mode:before { content: mi-content($mi-var-burst-mode); }
.#{$mi-css-prefix}-domain:before { content: mi-content($mi-var-domain); }
.#{$mi-css-prefix}-business-center:before { content: mi-content($mi-var-business-center); }
.#{$mi-css-prefix}-cached:before { content: mi-content($mi-var-cached); }
.#{$mi-css-prefix}-cake:before { content: mi-content($mi-var-cake); }
.#{$mi-css-prefix}-phone:before { content: mi-content($mi-var-phone); }
.#{$mi-css-prefix}-call-end:before { content: mi-content($mi-var-call-end); }
.#{$mi-css-prefix}-call-made:before { content: mi-content($mi-var-call-made); }
.#{$mi-css-prefix}-merge-type:before { content: mi-content($mi-var-merge-type); }
.#{$mi-css-prefix}-call-missed:before { content: mi-content($mi-var-call-missed); }
.#{$mi-css-prefix}-call-missed-outgoing:before { content: mi-content($mi-var-call-missed-outgoing); }
.#{$mi-css-prefix}-call-received:before { content: mi-content($mi-var-call-received); }
.#{$mi-css-prefix}-call-split:before { content: mi-content($mi-var-call-split); }
.#{$mi-css-prefix}-call-to-action:before { content: mi-content($mi-var-call-to-action); }
.#{$mi-css-prefix}-camera:before { content: mi-content($mi-var-camera); }
.#{$mi-css-prefix}-photo-camera:before { content: mi-content($mi-var-photo-camera); }
.#{$mi-css-prefix}-camera-enhance:before { content: mi-content($mi-var-camera-enhance); }
.#{$mi-css-prefix}-camera-front:before { content: mi-content($mi-var-camera-front); }
.#{$mi-css-prefix}-camera-rear:before { content: mi-content($mi-var-camera-rear); }
.#{$mi-css-prefix}-camera-roll:before { content: mi-content($mi-var-camera-roll); }
.#{$mi-css-prefix}-cancel:before { content: mi-content($mi-var-cancel); }
.#{$mi-css-prefix}-redeem:before { content: mi-content($mi-var-redeem); }
.#{$mi-css-prefix}-card-membership:before { content: mi-content($mi-var-card-membership); }
.#{$mi-css-prefix}-card-travel:before { content: mi-content($mi-var-card-travel); }
.#{$mi-css-prefix}-casino:before { content: mi-content($mi-var-casino); }
.#{$mi-css-prefix}-cast:before { content: mi-content($mi-var-cast); }
.#{$mi-css-prefix}-cast-connected:before { content: mi-content($mi-var-cast-connected); }
.#{$mi-css-prefix}-center-focus-strong:before { content: mi-content($mi-var-center-focus-strong); }
.#{$mi-css-prefix}-center-focus-weak:before { content: mi-content($mi-var-center-focus-weak); }
.#{$mi-css-prefix}-change-history:before { content: mi-content($mi-var-change-history); }
.#{$mi-css-prefix}-chat:before { content: mi-content($mi-var-chat); }
.#{$mi-css-prefix}-chat-bubble:before { content: mi-content($mi-var-chat-bubble); }
.#{$mi-css-prefix}-chat-bubble-outline:before { content: mi-content($mi-var-chat-bubble-outline); }
.#{$mi-css-prefix}-check:before { content: mi-content($mi-var-check); }
.#{$mi-css-prefix}-check-box:before { content: mi-content($mi-var-check-box); }
.#{$mi-css-prefix}-check-box-outline-blank:before { content: mi-content($mi-var-check-box-outline-blank); }
.#{$mi-css-prefix}-check-circle:before { content: mi-content($mi-var-check-circle); }
.#{$mi-css-prefix}-navigate-before:before { content: mi-content($mi-var-navigate-before); }
.#{$mi-css-prefix}-navigate-next:before { content: mi-content($mi-var-navigate-next); }
.#{$mi-css-prefix}-child-care:before { content: mi-content($mi-var-child-care); }
.#{$mi-css-prefix}-child-friendly:before { content: mi-content($mi-var-child-friendly); }
.#{$mi-css-prefix}-chrome-reader-mode:before { content: mi-content($mi-var-chrome-reader-mode); }
.#{$mi-css-prefix}-close:before { content: mi-content($mi-var-close); }
.#{$mi-css-prefix}-clear-all:before { content: mi-content($mi-var-clear-all); }
.#{$mi-css-prefix}-closed-caption:before { content: mi-content($mi-var-closed-caption); }
.#{$mi-css-prefix}-wb-cloudy:before { content: mi-content($mi-var-wb-cloudy); }
.#{$mi-css-prefix}-cloud-circle:before { content: mi-content($mi-var-cloud-circle); }
.#{$mi-css-prefix}-cloud-done:before { content: mi-content($mi-var-cloud-done); }
.#{$mi-css-prefix}-cloud-download:before { content: mi-content($mi-var-cloud-download); }
.#{$mi-css-prefix}-cloud-off:before { content: mi-content($mi-var-cloud-off); }
.#{$mi-css-prefix}-cloud-queue:before { content: mi-content($mi-var-cloud-queue); }
.#{$mi-css-prefix}-code:before { content: mi-content($mi-var-code); }
.#{$mi-css-prefix}-photo-library:before { content: mi-content($mi-var-photo-library); }
.#{$mi-css-prefix}-collections-bookmark:before { content: mi-content($mi-var-collections-bookmark); }
.#{$mi-css-prefix}-palette:before { content: mi-content($mi-var-palette); }
.#{$mi-css-prefix}-colorize:before { content: mi-content($mi-var-colorize); }
.#{$mi-css-prefix}-comment:before { content: mi-content($mi-var-comment); }
.#{$mi-css-prefix}-compare:before { content: mi-content($mi-var-compare); }
.#{$mi-css-prefix}-compare-arrows:before { content: mi-content($mi-var-compare-arrows); }
.#{$mi-css-prefix}-laptop:before { content: mi-content($mi-var-laptop); }
.#{$mi-css-prefix}-confirmation-number:before { content: mi-content($mi-var-confirmation-number); }
.#{$mi-css-prefix}-contact-mail:before { content: mi-content($mi-var-contact-mail); }
.#{$mi-css-prefix}-contact-phone:before { content: mi-content($mi-var-contact-phone); }
.#{$mi-css-prefix}-contacts:before { content: mi-content($mi-var-contacts); }
.#{$mi-css-prefix}-content-copy:before { content: mi-content($mi-var-content-copy); }
.#{$mi-css-prefix}-content-cut:before { content: mi-content($mi-var-content-cut); }
.#{$mi-css-prefix}-content-paste:before { content: mi-content($mi-var-content-paste); }
.#{$mi-css-prefix}-control-point-duplicate:before { content: mi-content($mi-var-control-point-duplicate); }
.#{$mi-css-prefix}-copyright:before { content: mi-content($mi-var-copyright); }
.#{$mi-css-prefix}-mode-edit:before { content: mi-content($mi-var-mode-edit); }
.#{$mi-css-prefix}-create-new-folder:before { content: mi-content($mi-var-create-new-folder); }
.#{$mi-css-prefix}-payment:before { content: mi-content($mi-var-payment); }
.#{$mi-css-prefix}-crop:before { content: mi-content($mi-var-crop); }
.#{$mi-css-prefix}-crop-16-9:before { content: mi-content($mi-var-crop-16-9); }
.#{$mi-css-prefix}-crop-3-2:before { content: mi-content($mi-var-crop-3-2); }
.#{$mi-css-prefix}-crop-landscape:before { content: mi-content($mi-var-crop-landscape); }
.#{$mi-css-prefix}-crop-7-5:before { content: mi-content($mi-var-crop-7-5); }
.#{$mi-css-prefix}-crop-din:before { content: mi-content($mi-var-crop-din); }
.#{$mi-css-prefix}-crop-free:before { content: mi-content($mi-var-crop-free); }
.#{$mi-css-prefix}-crop-original:before { content: mi-content($mi-var-crop-original); }
.#{$mi-css-prefix}-crop-portrait:before { content: mi-content($mi-var-crop-portrait); }
.#{$mi-css-prefix}-crop-rotate:before { content: mi-content($mi-var-crop-rotate); }
.#{$mi-css-prefix}-crop-square:before { content: mi-content($mi-var-crop-square); }
.#{$mi-css-prefix}-dashboard:before { content: mi-content($mi-var-dashboard); }
.#{$mi-css-prefix}-data-usage:before { content: mi-content($mi-var-data-usage); }
.#{$mi-css-prefix}-date-range:before { content: mi-content($mi-var-date-range); }
.#{$mi-css-prefix}-dehaze:before { content: mi-content($mi-var-dehaze); }
.#{$mi-css-prefix}-delete:before { content: mi-content($mi-var-delete); }
.#{$mi-css-prefix}-delete-forever:before { content: mi-content($mi-var-delete-forever); }
.#{$mi-css-prefix}-delete-sweep:before { content: mi-content($mi-var-delete-sweep); }
.#{$mi-css-prefix}-description:before { content: mi-content($mi-var-description); }
.#{$mi-css-prefix}-desktop-mac:before { content: mi-content($mi-var-desktop-mac); }
.#{$mi-css-prefix}-desktop-windows:before { content: mi-content($mi-var-desktop-windows); }
.#{$mi-css-prefix}-details:before { content: mi-content($mi-var-details); }
.#{$mi-css-prefix}-developer-board:before { content: mi-content($mi-var-developer-board); }
.#{$mi-css-prefix}-developer-mode:before { content: mi-content($mi-var-developer-mode); }
.#{$mi-css-prefix}-device-hub:before { content: mi-content($mi-var-device-hub); }
.#{$mi-css-prefix}-phonelink:before { content: mi-content($mi-var-phonelink); }
.#{$mi-css-prefix}-devices-other:before { content: mi-content($mi-var-devices-other); }
.#{$mi-css-prefix}-dialer-sip:before { content: mi-content($mi-var-dialer-sip); }
.#{$mi-css-prefix}-dialpad:before { content: mi-content($mi-var-dialpad); }
.#{$mi-css-prefix}-directions:before { content: mi-content($mi-var-directions); }
.#{$mi-css-prefix}-directions-bike:before { content: mi-content($mi-var-directions-bike); }
.#{$mi-css-prefix}-directions-boat:before { content: mi-content($mi-var-directions-boat); }
.#{$mi-css-prefix}-directions-bus:before { content: mi-content($mi-var-directions-bus); }
.#{$mi-css-prefix}-directions-car:before { content: mi-content($mi-var-directions-car); }
.#{$mi-css-prefix}-directions-railway:before { content: mi-content($mi-var-directions-railway); }
.#{$mi-css-prefix}-directions-run:before { content: mi-content($mi-var-directions-run); }
.#{$mi-css-prefix}-directions-transit:before { content: mi-content($mi-var-directions-transit); }
.#{$mi-css-prefix}-directions-walk:before { content: mi-content($mi-var-directions-walk); }
.#{$mi-css-prefix}-disc-full:before { content: mi-content($mi-var-disc-full); }
.#{$mi-css-prefix}-dns:before { content: mi-content($mi-var-dns); }
.#{$mi-css-prefix}-not-interested:before { content: mi-content($mi-var-not-interested); }
.#{$mi-css-prefix}-do-not-disturb-alt:before { content: mi-content($mi-var-do-not-disturb-alt); }
.#{$mi-css-prefix}-do-not-disturb-off:before { content: mi-content($mi-var-do-not-disturb-off); }
.#{$mi-css-prefix}-remove-circle:before { content: mi-content($mi-var-remove-circle); }
.#{$mi-css-prefix}-dock:before { content: mi-content($mi-var-dock); }
.#{$mi-css-prefix}-done:before { content: mi-content($mi-var-done); }
.#{$mi-css-prefix}-done-all:before { content: mi-content($mi-var-done-all); }
.#{$mi-css-prefix}-donut-large:before { content: mi-content($mi-var-donut-large); }
.#{$mi-css-prefix}-donut-small:before { content: mi-content($mi-var-donut-small); }
.#{$mi-css-prefix}-drafts:before { content: mi-content($mi-var-drafts); }
.#{$mi-css-prefix}-drag-handle:before { content: mi-content($mi-var-drag-handle); }
.#{$mi-css-prefix}-time-to-leave:before { content: mi-content($mi-var-time-to-leave); }
.#{$mi-css-prefix}-dvr:before { content: mi-content($mi-var-dvr); }
.#{$mi-css-prefix}-edit-location:before { content: mi-content($mi-var-edit-location); }
.#{$mi-css-prefix}-eject:before { content: mi-content($mi-var-eject); }
.#{$mi-css-prefix}-markunread:before { content: mi-content($mi-var-markunread); }
.#{$mi-css-prefix}-enhanced-encryption:before { content: mi-content($mi-var-enhanced-encryption); }
.#{$mi-css-prefix}-equalizer:before { content: mi-content($mi-var-equalizer); }
.#{$mi-css-prefix}-error:before { content: mi-content($mi-var-error); }
.#{$mi-css-prefix}-error-outline:before { content: mi-content($mi-var-error-outline); }
.#{$mi-css-prefix}-euro-symbol:before { content: mi-content($mi-var-euro-symbol); }
.#{$mi-css-prefix}-ev-station:before { content: mi-content($mi-var-ev-station); }
.#{$mi-css-prefix}-insert-invitation:before { content: mi-content($mi-var-insert-invitation); }
.#{$mi-css-prefix}-event-available:before { content: mi-content($mi-var-event-available); }
.#{$mi-css-prefix}-event-busy:before { content: mi-content($mi-var-event-busy); }
.#{$mi-css-prefix}-event-note:before { content: mi-content($mi-var-event-note); }
.#{$mi-css-prefix}-event-seat:before { content: mi-content($mi-var-event-seat); }
.#{$mi-css-prefix}-exit-to-app:before { content: mi-content($mi-var-exit-to-app); }
.#{$mi-css-prefix}-expand-less:before { content: mi-content($mi-var-expand-less); }
.#{$mi-css-prefix}-expand-more:before { content: mi-content($mi-var-expand-more); }
.#{$mi-css-prefix}-explicit:before { content: mi-content($mi-var-explicit); }
.#{$mi-css-prefix}-explore:before { content: mi-content($mi-var-explore); }
.#{$mi-css-prefix}-exposure:before { content: mi-content($mi-var-exposure); }
.#{$mi-css-prefix}-exposure-neg-1:before { content: mi-content($mi-var-exposure-neg-1); }
.#{$mi-css-prefix}-exposure-neg-2:before { content: mi-content($mi-var-exposure-neg-2); }
.#{$mi-css-prefix}-exposure-plus-1:before { content: mi-content($mi-var-exposure-plus-1); }
.#{$mi-css-prefix}-exposure-plus-2:before { content: mi-content($mi-var-exposure-plus-2); }
.#{$mi-css-prefix}-exposure-zero:before { content: mi-content($mi-var-exposure-zero); }
.#{$mi-css-prefix}-extension:before { content: mi-content($mi-var-extension); }
.#{$mi-css-prefix}-face:before { content: mi-content($mi-var-face); }
.#{$mi-css-prefix}-fast-forward:before { content: mi-content($mi-var-fast-forward); }
.#{$mi-css-prefix}-fast-rewind:before { content: mi-content($mi-var-fast-rewind); }
.#{$mi-css-prefix}-favorite:before { content: mi-content($mi-var-favorite); }
.#{$mi-css-prefix}-favorite-border:before { content: mi-content($mi-var-favorite-border); }
.#{$mi-css-prefix}-featured-play-list:before { content: mi-content($mi-var-featured-play-list); }
.#{$mi-css-prefix}-featured-video:before { content: mi-content($mi-var-featured-video); }
.#{$mi-css-prefix}-sms-failed:before { content: mi-content($mi-var-sms-failed); }
.#{$mi-css-prefix}-fiber-dvr:before { content: mi-content($mi-var-fiber-dvr); }
.#{$mi-css-prefix}-fiber-manual-record:before { content: mi-content($mi-var-fiber-manual-record); }
.#{$mi-css-prefix}-fiber-new:before { content: mi-content($mi-var-fiber-new); }
.#{$mi-css-prefix}-fiber-pin:before { content: mi-content($mi-var-fiber-pin); }
.#{$mi-css-prefix}-fiber-smart-record:before { content: mi-content($mi-var-fiber-smart-record); }
.#{$mi-css-prefix}-get-app:before { content: mi-content($mi-var-get-app); }
.#{$mi-css-prefix}-file-upload:before { content: mi-content($mi-var-file-upload); }
.#{$mi-css-prefix}-filter:before { content: mi-content($mi-var-filter); }
.#{$mi-css-prefix}-filter-1:before { content: mi-content($mi-var-filter-1); }
.#{$mi-css-prefix}-filter-2:before { content: mi-content($mi-var-filter-2); }
.#{$mi-css-prefix}-filter-3:before { content: mi-content($mi-var-filter-3); }
.#{$mi-css-prefix}-filter-4:before { content: mi-content($mi-var-filter-4); }
.#{$mi-css-prefix}-filter-5:before { content: mi-content($mi-var-filter-5); }
.#{$mi-css-prefix}-filter-6:before { content: mi-content($mi-var-filter-6); }
.#{$mi-css-prefix}-filter-7:before { content: mi-content($mi-var-filter-7); }
.#{$mi-css-prefix}-filter-8:before { content: mi-content($mi-var-filter-8); }
.#{$mi-css-prefix}-filter-9:before { content: mi-content($mi-var-filter-9); }
.#{$mi-css-prefix}-filter-9-plus:before { content: mi-content($mi-var-filter-9-plus); }
.#{$mi-css-prefix}-filter-b-and-w:before { content: mi-content($mi-var-filter-b-and-w); }
.#{$mi-css-prefix}-filter-center-focus:before { content: mi-content($mi-var-filter-center-focus); }
.#{$mi-css-prefix}-filter-drama:before { content: mi-content($mi-var-filter-drama); }
.#{$mi-css-prefix}-filter-frames:before { content: mi-content($mi-var-filter-frames); }
.#{$mi-css-prefix}-terrain:before { content: mi-content($mi-var-terrain); }
.#{$mi-css-prefix}-filter-list:before { content: mi-content($mi-var-filter-list); }
.#{$mi-css-prefix}-filter-none:before { content: mi-content($mi-var-filter-none); }
.#{$mi-css-prefix}-filter-tilt-shift:before { content: mi-content($mi-var-filter-tilt-shift); }
.#{$mi-css-prefix}-filter-vintage:before { content: mi-content($mi-var-filter-vintage); }
.#{$mi-css-prefix}-find-in-page:before { content: mi-content($mi-var-find-in-page); }
.#{$mi-css-prefix}-find-replace:before { content: mi-content($mi-var-find-replace); }
.#{$mi-css-prefix}-fingerprint:before { content: mi-content($mi-var-fingerprint); }
.#{$mi-css-prefix}-first-page:before { content: mi-content($mi-var-first-page); }
.#{$mi-css-prefix}-fitness-center:before { content: mi-content($mi-var-fitness-center); }
.#{$mi-css-prefix}-flare:before { content: mi-content($mi-var-flare); }
.#{$mi-css-prefix}-flash-auto:before { content: mi-content($mi-var-flash-auto); }
.#{$mi-css-prefix}-flash-off:before { content: mi-content($mi-var-flash-off); }
.#{$mi-css-prefix}-flash-on:before { content: mi-content($mi-var-flash-on); }
.#{$mi-css-prefix}-flight-land:before { content: mi-content($mi-var-flight-land); }
.#{$mi-css-prefix}-flight-takeoff:before { content: mi-content($mi-var-flight-takeoff); }
.#{$mi-css-prefix}-flip:before { content: mi-content($mi-var-flip); }
.#{$mi-css-prefix}-flip-to-back:before { content: mi-content($mi-var-flip-to-back); }
.#{$mi-css-prefix}-flip-to-front:before { content: mi-content($mi-var-flip-to-front); }
.#{$mi-css-prefix}-folder:before { content: mi-content($mi-var-folder); }
.#{$mi-css-prefix}-folder-open:before { content: mi-content($mi-var-folder-open); }
.#{$mi-css-prefix}-folder-shared:before { content: mi-content($mi-var-folder-shared); }
.#{$mi-css-prefix}-folder-special:before { content: mi-content($mi-var-folder-special); }
.#{$mi-css-prefix}-font-download:before { content: mi-content($mi-var-font-download); }
.#{$mi-css-prefix}-format-align-center:before { content: mi-content($mi-var-format-align-center); }
.#{$mi-css-prefix}-format-align-justify:before { content: mi-content($mi-var-format-align-justify); }
.#{$mi-css-prefix}-format-align-left:before { content: mi-content($mi-var-format-align-left); }
.#{$mi-css-prefix}-format-align-right:before { content: mi-content($mi-var-format-align-right); }
.#{$mi-css-prefix}-format-bold:before { content: mi-content($mi-var-format-bold); }
.#{$mi-css-prefix}-format-clear:before { content: mi-content($mi-var-format-clear); }
.#{$mi-css-prefix}-format-color-fill:before { content: mi-content($mi-var-format-color-fill); }
.#{$mi-css-prefix}-format-color-reset:before { content: mi-content($mi-var-format-color-reset); }
.#{$mi-css-prefix}-format-color-text:before { content: mi-content($mi-var-format-color-text); }
.#{$mi-css-prefix}-format-indent-decrease:before { content: mi-content($mi-var-format-indent-decrease); }
.#{$mi-css-prefix}-format-indent-increase:before { content: mi-content($mi-var-format-indent-increase); }
.#{$mi-css-prefix}-format-italic:before { content: mi-content($mi-var-format-italic); }
.#{$mi-css-prefix}-format-line-spacing:before { content: mi-content($mi-var-format-line-spacing); }
.#{$mi-css-prefix}-format-list-bulleted:before { content: mi-content($mi-var-format-list-bulleted); }
.#{$mi-css-prefix}-format-list-numbered:before { content: mi-content($mi-var-format-list-numbered); }
.#{$mi-css-prefix}-format-paint:before { content: mi-content($mi-var-format-paint); }
.#{$mi-css-prefix}-format-quote:before { content: mi-content($mi-var-format-quote); }
.#{$mi-css-prefix}-format-shapes:before { content: mi-content($mi-var-format-shapes); }
.#{$mi-css-prefix}-format-size:before { content: mi-content($mi-var-format-size); }
.#{$mi-css-prefix}-format-strikethrough:before { content: mi-content($mi-var-format-strikethrough); }
.#{$mi-css-prefix}-format-textdirection-l-to-r:before { content: mi-content($mi-var-format-textdirection-l-to-r); }
.#{$mi-css-prefix}-format-textdirection-r-to-l:before { content: mi-content($mi-var-format-textdirection-r-to-l); }
.#{$mi-css-prefix}-format-underlined:before { content: mi-content($mi-var-format-underlined); }
.#{$mi-css-prefix}-question-answer:before { content: mi-content($mi-var-question-answer); }
.#{$mi-css-prefix}-forward:before { content: mi-content($mi-var-forward); }
.#{$mi-css-prefix}-forward-10:before { content: mi-content($mi-var-forward-10); }
.#{$mi-css-prefix}-forward-30:before { content: mi-content($mi-var-forward-30); }
.#{$mi-css-prefix}-forward-5:before { content: mi-content($mi-var-forward-5); }
.#{$mi-css-prefix}-free-breakfast:before { content: mi-content($mi-var-free-breakfast); }
.#{$mi-css-prefix}-fullscreen:before { content: mi-content($mi-var-fullscreen); }
.#{$mi-css-prefix}-fullscreen-exit:before { content: mi-content($mi-var-fullscreen-exit); }
.#{$mi-css-prefix}-functions:before { content: mi-content($mi-var-functions); }
.#{$mi-css-prefix}-g-translate:before { content: mi-content($mi-var-g-translate); }
.#{$mi-css-prefix}-games:before { content: mi-content($mi-var-games); }
.#{$mi-css-prefix}-gavel:before { content: mi-content($mi-var-gavel); }
.#{$mi-css-prefix}-gesture:before { content: mi-content($mi-var-gesture); }
.#{$mi-css-prefix}-gif:before { content: mi-content($mi-var-gif); }
.#{$mi-css-prefix}-goat:before { content: mi-content($mi-var-goat); }
.#{$mi-css-prefix}-golf-course:before { content: mi-content($mi-var-golf-course); }
.#{$mi-css-prefix}-my-location:before { content: mi-content($mi-var-my-location); }
.#{$mi-css-prefix}-location-searching:before { content: mi-content($mi-var-location-searching); }
.#{$mi-css-prefix}-location-disabled:before { content: mi-content($mi-var-location-disabled); }
.#{$mi-css-prefix}-star:before { content: mi-content($mi-var-star); }
.#{$mi-css-prefix}-gradient:before { content: mi-content($mi-var-gradient); }
.#{$mi-css-prefix}-grain:before { content: mi-content($mi-var-grain); }
.#{$mi-css-prefix}-graphic-eq:before { content: mi-content($mi-var-graphic-eq); }
.#{$mi-css-prefix}-grid-off:before { content: mi-content($mi-var-grid-off); }
.#{$mi-css-prefix}-grid-on:before { content: mi-content($mi-var-grid-on); }
.#{$mi-css-prefix}-people:before { content: mi-content($mi-var-people); }
.#{$mi-css-prefix}-group-add:before { content: mi-content($mi-var-group-add); }
.#{$mi-css-prefix}-group-work:before { content: mi-content($mi-var-group-work); }
.#{$mi-css-prefix}-hd:before { content: mi-content($mi-var-hd); }
.#{$mi-css-prefix}-hdr-off:before { content: mi-content($mi-var-hdr-off); }
.#{$mi-css-prefix}-hdr-on:before { content: mi-content($mi-var-hdr-on); }
.#{$mi-css-prefix}-hdr-strong:before { content: mi-content($mi-var-hdr-strong); }
.#{$mi-css-prefix}-hdr-weak:before { content: mi-content($mi-var-hdr-weak); }
.#{$mi-css-prefix}-headset:before { content: mi-content($mi-var-headset); }
.#{$mi-css-prefix}-headset-mic:before { content: mi-content($mi-var-headset-mic); }
.#{$mi-css-prefix}-healing:before { content: mi-content($mi-var-healing); }
.#{$mi-css-prefix}-hearing:before { content: mi-content($mi-var-hearing); }
.#{$mi-css-prefix}-help:before { content: mi-content($mi-var-help); }
.#{$mi-css-prefix}-help-outline:before { content: mi-content($mi-var-help-outline); }
.#{$mi-css-prefix}-high-quality:before { content: mi-content($mi-var-high-quality); }
.#{$mi-css-prefix}-highlight:before { content: mi-content($mi-var-highlight); }
.#{$mi-css-prefix}-highlight-off:before { content: mi-content($mi-var-highlight-off); }
.#{$mi-css-prefix}-restore:before { content: mi-content($mi-var-restore); }
.#{$mi-css-prefix}-home:before { content: mi-content($mi-var-home); }
.#{$mi-css-prefix}-hot-tub:before { content: mi-content($mi-var-hot-tub); }
.#{$mi-css-prefix}-local-hotel:before { content: mi-content($mi-var-local-hotel); }
.#{$mi-css-prefix}-hourglass-empty:before { content: mi-content($mi-var-hourglass-empty); }
.#{$mi-css-prefix}-hourglass-full:before { content: mi-content($mi-var-hourglass-full); }
.#{$mi-css-prefix}-http:before { content: mi-content($mi-var-http); }
.#{$mi-css-prefix}-lock:before { content: mi-content($mi-var-lock); }
.#{$mi-css-prefix}-photo:before { content: mi-content($mi-var-photo); }
.#{$mi-css-prefix}-image-aspect-ratio:before { content: mi-content($mi-var-image-aspect-ratio); }
.#{$mi-css-prefix}-import-contacts:before { content: mi-content($mi-var-import-contacts); }
.#{$mi-css-prefix}-import-export:before { content: mi-content($mi-var-import-export); }
.#{$mi-css-prefix}-important-devices:before { content: mi-content($mi-var-important-devices); }
.#{$mi-css-prefix}-inbox:before { content: mi-content($mi-var-inbox); }
.#{$mi-css-prefix}-indeterminate-check-box:before { content: mi-content($mi-var-indeterminate-check-box); }
.#{$mi-css-prefix}-info:before { content: mi-content($mi-var-info); }
.#{$mi-css-prefix}-info-outline:before { content: mi-content($mi-var-info-outline); }
.#{$mi-css-prefix}-input:before { content: mi-content($mi-var-input); }
.#{$mi-css-prefix}-insert-comment:before { content: mi-content($mi-var-insert-comment); }
.#{$mi-css-prefix}-insert-drive-file:before { content: mi-content($mi-var-insert-drive-file); }
.#{$mi-css-prefix}-tag-faces:before { content: mi-content($mi-var-tag-faces); }
.#{$mi-css-prefix}-link:before { content: mi-content($mi-var-link); }
.#{$mi-css-prefix}-invert-colors:before { content: mi-content($mi-var-invert-colors); }
.#{$mi-css-prefix}-invert-colors-off:before { content: mi-content($mi-var-invert-colors-off); }
.#{$mi-css-prefix}-iso:before { content: mi-content($mi-var-iso); }
.#{$mi-css-prefix}-keyboard:before { content: mi-content($mi-var-keyboard); }
.#{$mi-css-prefix}-keyboard-arrow-down:before { content: mi-content($mi-var-keyboard-arrow-down); }
.#{$mi-css-prefix}-keyboard-arrow-left:before { content: mi-content($mi-var-keyboard-arrow-left); }
.#{$mi-css-prefix}-keyboard-arrow-right:before { content: mi-content($mi-var-keyboard-arrow-right); }
.#{$mi-css-prefix}-keyboard-arrow-up:before { content: mi-content($mi-var-keyboard-arrow-up); }
.#{$mi-css-prefix}-keyboard-backspace:before { content: mi-content($mi-var-keyboard-backspace); }
.#{$mi-css-prefix}-keyboard-capslock:before { content: mi-content($mi-var-keyboard-capslock); }
.#{$mi-css-prefix}-keyboard-hide:before { content: mi-content($mi-var-keyboard-hide); }
.#{$mi-css-prefix}-keyboard-return:before { content: mi-content($mi-var-keyboard-return); }
.#{$mi-css-prefix}-keyboard-tab:before { content: mi-content($mi-var-keyboard-tab); }
.#{$mi-css-prefix}-keyboard-voice:before { content: mi-content($mi-var-keyboard-voice); }
.#{$mi-css-prefix}-kitchen:before { content: mi-content($mi-var-kitchen); }
.#{$mi-css-prefix}-label:before { content: mi-content($mi-var-label); }
.#{$mi-css-prefix}-label-outline:before { content: mi-content($mi-var-label-outline); }
.#{$mi-css-prefix}-language:before { content: mi-content($mi-var-language); }
.#{$mi-css-prefix}-laptop-chromebook:before { content: mi-content($mi-var-laptop-chromebook); }
.#{$mi-css-prefix}-laptop-mac:before { content: mi-content($mi-var-laptop-mac); }
.#{$mi-css-prefix}-laptop-windows:before { content: mi-content($mi-var-laptop-windows); }
.#{$mi-css-prefix}-last-page:before { content: mi-content($mi-var-last-page); }
.#{$mi-css-prefix}-open-in-new:before { content: mi-content($mi-var-open-in-new); }
.#{$mi-css-prefix}-layers:before { content: mi-content($mi-var-layers); }
.#{$mi-css-prefix}-layers-clear:before { content: mi-content($mi-var-layers-clear); }
.#{$mi-css-prefix}-leak-add:before { content: mi-content($mi-var-leak-add); }
.#{$mi-css-prefix}-leak-remove:before { content: mi-content($mi-var-leak-remove); }
.#{$mi-css-prefix}-lens:before { content: mi-content($mi-var-lens); }
.#{$mi-css-prefix}-library-books:before { content: mi-content($mi-var-library-books); }
.#{$mi-css-prefix}-library-music:before { content: mi-content($mi-var-library-music); }
.#{$mi-css-prefix}-lightbulb-outline:before { content: mi-content($mi-var-lightbulb-outline); }
.#{$mi-css-prefix}-line-style:before { content: mi-content($mi-var-line-style); }
.#{$mi-css-prefix}-line-weight:before { content: mi-content($mi-var-line-weight); }
.#{$mi-css-prefix}-linear-scale:before { content: mi-content($mi-var-linear-scale); }
.#{$mi-css-prefix}-linked-camera:before { content: mi-content($mi-var-linked-camera); }
.#{$mi-css-prefix}-list:before { content: mi-content($mi-var-list); }
.#{$mi-css-prefix}-live-help:before { content: mi-content($mi-var-live-help); }
.#{$mi-css-prefix}-live-tv:before { content: mi-content($mi-var-live-tv); }
.#{$mi-css-prefix}-local-play:before { content: mi-content($mi-var-local-play); }
.#{$mi-css-prefix}-local-airport:before { content: mi-content($mi-var-local-airport); }
.#{$mi-css-prefix}-local-atm:before { content: mi-content($mi-var-local-atm); }
.#{$mi-css-prefix}-local-bar:before { content: mi-content($mi-var-local-bar); }
.#{$mi-css-prefix}-local-cafe:before { content: mi-content($mi-var-local-cafe); }
.#{$mi-css-prefix}-local-car-wash:before { content: mi-content($mi-var-local-car-wash); }
.#{$mi-css-prefix}-local-convenience-store:before { content: mi-content($mi-var-local-convenience-store); }
.#{$mi-css-prefix}-restaurant-menu:before { content: mi-content($mi-var-restaurant-menu); }
.#{$mi-css-prefix}-local-drink:before { content: mi-content($mi-var-local-drink); }
.#{$mi-css-prefix}-local-florist:before { content: mi-content($mi-var-local-florist); }
.#{$mi-css-prefix}-local-gas-station:before { content: mi-content($mi-var-local-gas-station); }
.#{$mi-css-prefix}-shopping-cart:before { content: mi-content($mi-var-shopping-cart); }
.#{$mi-css-prefix}-local-hospital:before { content: mi-content($mi-var-local-hospital); }
.#{$mi-css-prefix}-local-laundry-service:before { content: mi-content($mi-var-local-laundry-service); }
.#{$mi-css-prefix}-local-library:before { content: mi-content($mi-var-local-library); }
.#{$mi-css-prefix}-local-mall:before { content: mi-content($mi-var-local-mall); }
.#{$mi-css-prefix}-theaters:before { content: mi-content($mi-var-theaters); }
.#{$mi-css-prefix}-local-offer:before { content: mi-content($mi-var-local-offer); }
.#{$mi-css-prefix}-local-parking:before { content: mi-content($mi-var-local-parking); }
.#{$mi-css-prefix}-local-pharmacy:before { content: mi-content($mi-var-local-pharmacy); }
.#{$mi-css-prefix}-local-pizza:before { content: mi-content($mi-var-local-pizza); }
.#{$mi-css-prefix}-print:before { content: mi-content($mi-var-print); }
.#{$mi-css-prefix}-local-shipping:before { content: mi-content($mi-var-local-shipping); }
.#{$mi-css-prefix}-local-taxi:before { content: mi-content($mi-var-local-taxi); }
.#{$mi-css-prefix}-location-city:before { content: mi-content($mi-var-location-city); }
.#{$mi-css-prefix}-location-off:before { content: mi-content($mi-var-location-off); }
.#{$mi-css-prefix}-room:before { content: mi-content($mi-var-room); }
.#{$mi-css-prefix}-lock-open:before { content: mi-content($mi-var-lock-open); }
.#{$mi-css-prefix}-lock-outline:before { content: mi-content($mi-var-lock-outline); }
.#{$mi-css-prefix}-looks:before { content: mi-content($mi-var-looks); }
.#{$mi-css-prefix}-looks-3:before { content: mi-content($mi-var-looks-3); }
.#{$mi-css-prefix}-looks-4:before { content: mi-content($mi-var-looks-4); }
.#{$mi-css-prefix}-looks-5:before { content: mi-content($mi-var-looks-5); }
.#{$mi-css-prefix}-looks-6:before { content: mi-content($mi-var-looks-6); }
.#{$mi-css-prefix}-looks-one:before { content: mi-content($mi-var-looks-one); }
.#{$mi-css-prefix}-looks-two:before { content: mi-content($mi-var-looks-two); }
.#{$mi-css-prefix}-sync:before { content: mi-content($mi-var-sync); }
.#{$mi-css-prefix}-loupe:before { content: mi-content($mi-var-loupe); }
.#{$mi-css-prefix}-low-priority:before { content: mi-content($mi-var-low-priority); }
.#{$mi-css-prefix}-loyalty:before { content: mi-content($mi-var-loyalty); }
.#{$mi-css-prefix}-mail-outline:before { content: mi-content($mi-var-mail-outline); }
.#{$mi-css-prefix}-map:before { content: mi-content($mi-var-map); }
.#{$mi-css-prefix}-markunread-mailbox:before { content: mi-content($mi-var-markunread-mailbox); }
.#{$mi-css-prefix}-memory:before { content: mi-content($mi-var-memory); }
.#{$mi-css-prefix}-menu:before { content: mi-content($mi-var-menu); }
.#{$mi-css-prefix}-message:before { content: mi-content($mi-var-message); }
.#{$mi-css-prefix}-mic:before { content: mi-content($mi-var-mic); }
.#{$mi-css-prefix}-mic-none:before { content: mi-content($mi-var-mic-none); }
.#{$mi-css-prefix}-mic-off:before { content: mi-content($mi-var-mic-off); }
.#{$mi-css-prefix}-mms:before { content: mi-content($mi-var-mms); }
.#{$mi-css-prefix}-mode-comment:before { content: mi-content($mi-var-mode-comment); }
.#{$mi-css-prefix}-monetization-on:before { content: mi-content($mi-var-monetization-on); }
.#{$mi-css-prefix}-money-off:before { content: mi-content($mi-var-money-off); }
.#{$mi-css-prefix}-monochrome-photos:before { content: mi-content($mi-var-monochrome-photos); }
.#{$mi-css-prefix}-mood-bad:before { content: mi-content($mi-var-mood-bad); }
.#{$mi-css-prefix}-more:before { content: mi-content($mi-var-more); }
.#{$mi-css-prefix}-more-horiz:before { content: mi-content($mi-var-more-horiz); }
.#{$mi-css-prefix}-more-vert:before { content: mi-content($mi-var-more-vert); }
.#{$mi-css-prefix}-motorcycle:before { content: mi-content($mi-var-motorcycle); }
.#{$mi-css-prefix}-mouse:before { content: mi-content($mi-var-mouse); }
.#{$mi-css-prefix}-move-to-inbox:before { content: mi-content($mi-var-move-to-inbox); }
.#{$mi-css-prefix}-movie-creation:before { content: mi-content($mi-var-movie-creation); }
.#{$mi-css-prefix}-movie-filter:before { content: mi-content($mi-var-movie-filter); }
.#{$mi-css-prefix}-multiline-chart:before { content: mi-content($mi-var-multiline-chart); }
.#{$mi-css-prefix}-music-note:before { content: mi-content($mi-var-music-note); }
.#{$mi-css-prefix}-music-video:before { content: mi-content($mi-var-music-video); }
.#{$mi-css-prefix}-nature:before { content: mi-content($mi-var-nature); }
.#{$mi-css-prefix}-nature-people:before { content: mi-content($mi-var-nature-people); }
.#{$mi-css-prefix}-navigation:before { content: mi-content($mi-var-navigation); }
.#{$mi-css-prefix}-near-me:before { content: mi-content($mi-var-near-me); }
.#{$mi-css-prefix}-network-cell:before { content: mi-content($mi-var-network-cell); }
.#{$mi-css-prefix}-network-check:before { content: mi-content($mi-var-network-check); }
.#{$mi-css-prefix}-network-locked:before { content: mi-content($mi-var-network-locked); }
.#{$mi-css-prefix}-network-wifi:before { content: mi-content($mi-var-network-wifi); }
.#{$mi-css-prefix}-new-releases:before { content: mi-content($mi-var-new-releases); }
.#{$mi-css-prefix}-next-week:before { content: mi-content($mi-var-next-week); }
.#{$mi-css-prefix}-nfc:before { content: mi-content($mi-var-nfc); }
.#{$mi-css-prefix}-no-encryption:before { content: mi-content($mi-var-no-encryption); }
.#{$mi-css-prefix}-signal-cellular-no-sim:before { content: mi-content($mi-var-signal-cellular-no-sim); }
.#{$mi-css-prefix}-note:before { content: mi-content($mi-var-note); }
.#{$mi-css-prefix}-note-add:before { content: mi-content($mi-var-note-add); }
.#{$mi-css-prefix}-notifications:before { content: mi-content($mi-var-notifications); }
.#{$mi-css-prefix}-notifications-active:before { content: mi-content($mi-var-notifications-active); }
.#{$mi-css-prefix}-notifications-none:before { content: mi-content($mi-var-notifications-none); }
.#{$mi-css-prefix}-notifications-off:before { content: mi-content($mi-var-notifications-off); }
.#{$mi-css-prefix}-notifications-paused:before { content: mi-content($mi-var-notifications-paused); }
.#{$mi-css-prefix}-offline-pin:before { content: mi-content($mi-var-offline-pin); }
.#{$mi-css-prefix}-ondemand-video:before { content: mi-content($mi-var-ondemand-video); }
.#{$mi-css-prefix}-opacity:before { content: mi-content($mi-var-opacity); }
.#{$mi-css-prefix}-open-in-browser:before { content: mi-content($mi-var-open-in-browser); }
.#{$mi-css-prefix}-open-with:before { content: mi-content($mi-var-open-with); }
.#{$mi-css-prefix}-pages:before { content: mi-content($mi-var-pages); }
.#{$mi-css-prefix}-pageview:before { content: mi-content($mi-var-pageview); }
.#{$mi-css-prefix}-pan-tool:before { content: mi-content($mi-var-pan-tool); }
.#{$mi-css-prefix}-panorama:before { content: mi-content($mi-var-panorama); }
.#{$mi-css-prefix}-radio-button-unchecked:before { content: mi-content($mi-var-radio-button-unchecked); }
.#{$mi-css-prefix}-panorama-horizontal:before { content: mi-content($mi-var-panorama-horizontal); }
.#{$mi-css-prefix}-panorama-vertical:before { content: mi-content($mi-var-panorama-vertical); }
.#{$mi-css-prefix}-panorama-wide-angle:before { content: mi-content($mi-var-panorama-wide-angle); }
.#{$mi-css-prefix}-party-mode:before { content: mi-content($mi-var-party-mode); }
.#{$mi-css-prefix}-pause:before { content: mi-content($mi-var-pause); }
.#{$mi-css-prefix}-pause-circle-filled:before { content: mi-content($mi-var-pause-circle-filled); }
.#{$mi-css-prefix}-pause-circle-outline:before { content: mi-content($mi-var-pause-circle-outline); }
.#{$mi-css-prefix}-people-outline:before { content: mi-content($mi-var-people-outline); }
.#{$mi-css-prefix}-perm-camera-mic:before { content: mi-content($mi-var-perm-camera-mic); }
.#{$mi-css-prefix}-perm-contact-calendar:before { content: mi-content($mi-var-perm-contact-calendar); }
.#{$mi-css-prefix}-perm-data-setting:before { content: mi-content($mi-var-perm-data-setting); }
.#{$mi-css-prefix}-perm-device-information:before { content: mi-content($mi-var-perm-device-information); }
.#{$mi-css-prefix}-person-outline:before { content: mi-content($mi-var-person-outline); }
.#{$mi-css-prefix}-perm-media:before { content: mi-content($mi-var-perm-media); }
.#{$mi-css-prefix}-perm-phone-msg:before { content: mi-content($mi-var-perm-phone-msg); }
.#{$mi-css-prefix}-perm-scan-wifi:before { content: mi-content($mi-var-perm-scan-wifi); }
.#{$mi-css-prefix}-person:before { content: mi-content($mi-var-person); }
.#{$mi-css-prefix}-person-add:before { content: mi-content($mi-var-person-add); }
.#{$mi-css-prefix}-person-pin:before { content: mi-content($mi-var-person-pin); }
.#{$mi-css-prefix}-person-pin-circle:before { content: mi-content($mi-var-person-pin-circle); }
.#{$mi-css-prefix}-personal-video:before { content: mi-content($mi-var-personal-video); }
.#{$mi-css-prefix}-pets:before { content: mi-content($mi-var-pets); }
.#{$mi-css-prefix}-phone-android:before { content: mi-content($mi-var-phone-android); }
.#{$mi-css-prefix}-phone-bluetooth-speaker:before { content: mi-content($mi-var-phone-bluetooth-speaker); }
.#{$mi-css-prefix}-phone-forwarded:before { content: mi-content($mi-var-phone-forwarded); }
.#{$mi-css-prefix}-phone-in-talk:before { content: mi-content($mi-var-phone-in-talk); }
.#{$mi-css-prefix}-phone-iphone:before { content: mi-content($mi-var-phone-iphone); }
.#{$mi-css-prefix}-phone-locked:before { content: mi-content($mi-var-phone-locked); }
.#{$mi-css-prefix}-phone-missed:before { content: mi-content($mi-var-phone-missed); }
.#{$mi-css-prefix}-phone-paused:before { content: mi-content($mi-var-phone-paused); }
.#{$mi-css-prefix}-phonelink-erase:before { content: mi-content($mi-var-phonelink-erase); }
.#{$mi-css-prefix}-phonelink-lock:before { content: mi-content($mi-var-phonelink-lock); }
.#{$mi-css-prefix}-phonelink-off:before { content: mi-content($mi-var-phonelink-off); }
.#{$mi-css-prefix}-phonelink-ring:before { content: mi-content($mi-var-phonelink-ring); }
.#{$mi-css-prefix}-phonelink-setup:before { content: mi-content($mi-var-phonelink-setup); }
.#{$mi-css-prefix}-photo-album:before { content: mi-content($mi-var-photo-album); }
.#{$mi-css-prefix}-photo-filter:before { content: mi-content($mi-var-photo-filter); }
.#{$mi-css-prefix}-photo-size-select-actual:before { content: mi-content($mi-var-photo-size-select-actual); }
.#{$mi-css-prefix}-photo-size-select-large:before { content: mi-content($mi-var-photo-size-select-large); }
.#{$mi-css-prefix}-photo-size-select-small:before { content: mi-content($mi-var-photo-size-select-small); }
.#{$mi-css-prefix}-picture-as-pdf:before { content: mi-content($mi-var-picture-as-pdf); }
.#{$mi-css-prefix}-picture-in-picture:before { content: mi-content($mi-var-picture-in-picture); }
.#{$mi-css-prefix}-picture-in-picture-alt:before { content: mi-content($mi-var-picture-in-picture-alt); }
.#{$mi-css-prefix}-pie-chart:before { content: mi-content($mi-var-pie-chart); }
.#{$mi-css-prefix}-pie-chart-outlined:before { content: mi-content($mi-var-pie-chart-outlined); }
.#{$mi-css-prefix}-pin-drop:before { content: mi-content($mi-var-pin-drop); }
.#{$mi-css-prefix}-play-arrow:before { content: mi-content($mi-var-play-arrow); }
.#{$mi-css-prefix}-play-circle-filled:before { content: mi-content($mi-var-play-circle-filled); }
.#{$mi-css-prefix}-play-circle-outline:before { content: mi-content($mi-var-play-circle-outline); }
.#{$mi-css-prefix}-play-for-work:before { content: mi-content($mi-var-play-for-work); }
.#{$mi-css-prefix}-playlist-add:before { content: mi-content($mi-var-playlist-add); }
.#{$mi-css-prefix}-playlist-add-check:before { content: mi-content($mi-var-playlist-add-check); }
.#{$mi-css-prefix}-playlist-play:before { content: mi-content($mi-var-playlist-play); }
.#{$mi-css-prefix}-plus-one:before { content: mi-content($mi-var-plus-one); }
.#{$mi-css-prefix}-polymer:before { content: mi-content($mi-var-polymer); }
.#{$mi-css-prefix}-pool:before { content: mi-content($mi-var-pool); }
.#{$mi-css-prefix}-portable-wifi-off:before { content: mi-content($mi-var-portable-wifi-off); }
.#{$mi-css-prefix}-portrait:before { content: mi-content($mi-var-portrait); }
.#{$mi-css-prefix}-power:before { content: mi-content($mi-var-power); }
.#{$mi-css-prefix}-power-input:before { content: mi-content($mi-var-power-input); }
.#{$mi-css-prefix}-power-settings-new:before { content: mi-content($mi-var-power-settings-new); }
.#{$mi-css-prefix}-pregnant-woman:before { content: mi-content($mi-var-pregnant-woman); }
.#{$mi-css-prefix}-present-to-all:before { content: mi-content($mi-var-present-to-all); }
.#{$mi-css-prefix}-priority-high:before { content: mi-content($mi-var-priority-high); }
.#{$mi-css-prefix}-public:before { content: mi-content($mi-var-public); }
.#{$mi-css-prefix}-publish:before { content: mi-content($mi-var-publish); }
.#{$mi-css-prefix}-queue-music:before { content: mi-content($mi-var-queue-music); }
.#{$mi-css-prefix}-queue-play-next:before { content: mi-content($mi-var-queue-play-next); }
.#{$mi-css-prefix}-radio:before { content: mi-content($mi-var-radio); }
.#{$mi-css-prefix}-radio-button-checked:before { content: mi-content($mi-var-radio-button-checked); }
.#{$mi-css-prefix}-rate-review:before { content: mi-content($mi-var-rate-review); }
.#{$mi-css-prefix}-receipt:before { content: mi-content($mi-var-receipt); }
.#{$mi-css-prefix}-recent-actors:before { content: mi-content($mi-var-recent-actors); }
.#{$mi-css-prefix}-record-voice-over:before { content: mi-content($mi-var-record-voice-over); }
.#{$mi-css-prefix}-redo:before { content: mi-content($mi-var-redo); }
.#{$mi-css-prefix}-refresh:before { content: mi-content($mi-var-refresh); }
.#{$mi-css-prefix}-remove:before { content: mi-content($mi-var-remove); }
.#{$mi-css-prefix}-remove-circle-outline:before { content: mi-content($mi-var-remove-circle-outline); }
.#{$mi-css-prefix}-remove-from-queue:before { content: mi-content($mi-var-remove-from-queue); }
.#{$mi-css-prefix}-visibility:before { content: mi-content($mi-var-visibility); }
.#{$mi-css-prefix}-remove-shopping-cart:before { content: mi-content($mi-var-remove-shopping-cart); }
.#{$mi-css-prefix}-reorder:before { content: mi-content($mi-var-reorder); }
.#{$mi-css-prefix}-repeat:before { content: mi-content($mi-var-repeat); }
.#{$mi-css-prefix}-repeat-one:before { content: mi-content($mi-var-repeat-one); }
.#{$mi-css-prefix}-replay:before { content: mi-content($mi-var-replay); }
.#{$mi-css-prefix}-replay-10:before { content: mi-content($mi-var-replay-10); }
.#{$mi-css-prefix}-replay-30:before { content: mi-content($mi-var-replay-30); }
.#{$mi-css-prefix}-replay-5:before { content: mi-content($mi-var-replay-5); }
.#{$mi-css-prefix}-reply:before { content: mi-content($mi-var-reply); }
.#{$mi-css-prefix}-reply-all:before { content: mi-content($mi-var-reply-all); }
.#{$mi-css-prefix}-report:before { content: mi-content($mi-var-report); }
.#{$mi-css-prefix}-warning:before { content: mi-content($mi-var-warning); }
.#{$mi-css-prefix}-restaurant:before { content: mi-content($mi-var-restaurant); }
.#{$mi-css-prefix}-restore-page:before { content: mi-content($mi-var-restore-page); }
.#{$mi-css-prefix}-ring-volume:before { content: mi-content($mi-var-ring-volume); }
.#{$mi-css-prefix}-room-service:before { content: mi-content($mi-var-room-service); }
.#{$mi-css-prefix}-rotate-90-degrees-ccw:before { content: mi-content($mi-var-rotate-90-degrees-ccw); }
.#{$mi-css-prefix}-rotate-left:before { content: mi-content($mi-var-rotate-left); }
.#{$mi-css-prefix}-rotate-right:before { content: mi-content($mi-var-rotate-right); }
.#{$mi-css-prefix}-rounded-corner:before { content: mi-content($mi-var-rounded-corner); }
.#{$mi-css-prefix}-router:before { content: mi-content($mi-var-router); }
.#{$mi-css-prefix}-rowing:before { content: mi-content($mi-var-rowing); }
.#{$mi-css-prefix}-rss-feed:before { content: mi-content($mi-var-rss-feed); }
.#{$mi-css-prefix}-rv-hookup:before { content: mi-content($mi-var-rv-hookup); }
.#{$mi-css-prefix}-satellite:before { content: mi-content($mi-var-satellite); }
.#{$mi-css-prefix}-save:before { content: mi-content($mi-var-save); }
.#{$mi-css-prefix}-scanner:before { content: mi-content($mi-var-scanner); }
.#{$mi-css-prefix}-school:before { content: mi-content($mi-var-school); }
.#{$mi-css-prefix}-screen-lock-landscape:before { content: mi-content($mi-var-screen-lock-landscape); }
.#{$mi-css-prefix}-screen-lock-portrait:before { content: mi-content($mi-var-screen-lock-portrait); }
.#{$mi-css-prefix}-screen-lock-rotation:before { content: mi-content($mi-var-screen-lock-rotation); }
.#{$mi-css-prefix}-screen-rotation:before { content: mi-content($mi-var-screen-rotation); }
.#{$mi-css-prefix}-screen-share:before { content: mi-content($mi-var-screen-share); }
.#{$mi-css-prefix}-sd-storage:before { content: mi-content($mi-var-sd-storage); }
.#{$mi-css-prefix}-search:before { content: mi-content($mi-var-search); }
.#{$mi-css-prefix}-security:before { content: mi-content($mi-var-security); }
.#{$mi-css-prefix}-select-all:before { content: mi-content($mi-var-select-all); }
.#{$mi-css-prefix}-send:before { content: mi-content($mi-var-send); }
.#{$mi-css-prefix}-sentiment-dissatisfied:before { content: mi-content($mi-var-sentiment-dissatisfied); }
.#{$mi-css-prefix}-sentiment-neutral:before { content: mi-content($mi-var-sentiment-neutral); }
.#{$mi-css-prefix}-sentiment-satisfied:before { content: mi-content($mi-var-sentiment-satisfied); }
.#{$mi-css-prefix}-sentiment-very-dissatisfied:before { content: mi-content($mi-var-sentiment-very-dissatisfied); }
.#{$mi-css-prefix}-sentiment-very-satisfied:before { content: mi-content($mi-var-sentiment-very-satisfied); }
.#{$mi-css-prefix}-settings:before { content: mi-content($mi-var-settings); }
.#{$mi-css-prefix}-settings-applications:before { content: mi-content($mi-var-settings-applications); }
.#{$mi-css-prefix}-settings-backup-restore:before { content: mi-content($mi-var-settings-backup-restore); }
.#{$mi-css-prefix}-settings-bluetooth:before { content: mi-content($mi-var-settings-bluetooth); }
.#{$mi-css-prefix}-settings-brightness:before { content: mi-content($mi-var-settings-brightness); }
.#{$mi-css-prefix}-settings-cell:before { content: mi-content($mi-var-settings-cell); }
.#{$mi-css-prefix}-settings-ethernet:before { content: mi-content($mi-var-settings-ethernet); }
.#{$mi-css-prefix}-settings-input-antenna:before { content: mi-content($mi-var-settings-input-antenna); }
.#{$mi-css-prefix}-settings-input-composite:before { content: mi-content($mi-var-settings-input-composite); }
.#{$mi-css-prefix}-settings-input-hdmi:before { content: mi-content($mi-var-settings-input-hdmi); }
.#{$mi-css-prefix}-settings-input-svideo:before { content: mi-content($mi-var-settings-input-svideo); }
.#{$mi-css-prefix}-settings-overscan:before { content: mi-content($mi-var-settings-overscan); }
.#{$mi-css-prefix}-settings-phone:before { content: mi-content($mi-var-settings-phone); }
.#{$mi-css-prefix}-settings-power:before { content: mi-content($mi-var-settings-power); }
.#{$mi-css-prefix}-settings-remote:before { content: mi-content($mi-var-settings-remote); }
.#{$mi-css-prefix}-settings-system-daydream:before { content: mi-content($mi-var-settings-system-daydream); }
.#{$mi-css-prefix}-settings-voice:before { content: mi-content($mi-var-settings-voice); }
.#{$mi-css-prefix}-share:before { content: mi-content($mi-var-share); }
.#{$mi-css-prefix}-shop:before { content: mi-content($mi-var-shop); }
.#{$mi-css-prefix}-shop-two:before { content: mi-content($mi-var-shop-two); }
.#{$mi-css-prefix}-shopping-basket:before { content: mi-content($mi-var-shopping-basket); }
.#{$mi-css-prefix}-short-text:before { content: mi-content($mi-var-short-text); }
.#{$mi-css-prefix}-show-chart:before { content: mi-content($mi-var-show-chart); }
.#{$mi-css-prefix}-shuffle:before { content: mi-content($mi-var-shuffle); }
.#{$mi-css-prefix}-signal-cellular-4-bar:before { content: mi-content($mi-var-signal-cellular-4-bar); }
.#{$mi-css-prefix}-signal-cellular-connected-no-internet-4-bar:before { content: mi-content($mi-var-signal-cellular-connected-no-internet-4-bar); }
.#{$mi-css-prefix}-signal-cellular-null:before { content: mi-content($mi-var-signal-cellular-null); }
.#{$mi-css-prefix}-signal-cellular-off:before { content: mi-content($mi-var-signal-cellular-off); }
.#{$mi-css-prefix}-signal-wifi-4-bar:before { content: mi-content($mi-var-signal-wifi-4-bar); }
.#{$mi-css-prefix}-signal-wifi-4-bar-lock:before { content: mi-content($mi-var-signal-wifi-4-bar-lock); }
.#{$mi-css-prefix}-signal-wifi-off:before { content: mi-content($mi-var-signal-wifi-off); }
.#{$mi-css-prefix}-sim-card:before { content: mi-content($mi-var-sim-card); }
.#{$mi-css-prefix}-sim-card-alert:before { content: mi-content($mi-var-sim-card-alert); }
.#{$mi-css-prefix}-skip-next:before { content: mi-content($mi-var-skip-next); }
.#{$mi-css-prefix}-skip-previous:before { content: mi-content($mi-var-skip-previous); }
.#{$mi-css-prefix}-slideshow:before { content: mi-content($mi-var-slideshow); }
.#{$mi-css-prefix}-slow-motion-video:before { content: mi-content($mi-var-slow-motion-video); }
.#{$mi-css-prefix}-stay-primary-portrait:before { content: mi-content($mi-var-stay-primary-portrait); }
.#{$mi-css-prefix}-smoke-free:before { content: mi-content($mi-var-smoke-free); }
.#{$mi-css-prefix}-smoking-rooms:before { content: mi-content($mi-var-smoking-rooms); }
.#{$mi-css-prefix}-textsms:before { content: mi-content($mi-var-textsms); }
.#{$mi-css-prefix}-snooze:before { content: mi-content($mi-var-snooze); }
.#{$mi-css-prefix}-sort:before { content: mi-content($mi-var-sort); }
.#{$mi-css-prefix}-sort-by-alpha:before { content: mi-content($mi-var-sort-by-alpha); }
.#{$mi-css-prefix}-spa:before { content: mi-content($mi-var-spa); }
.#{$mi-css-prefix}-space-bar:before { content: mi-content($mi-var-space-bar); }
.#{$mi-css-prefix}-speaker:before { content: mi-content($mi-var-speaker); }
.#{$mi-css-prefix}-speaker-group:before { content: mi-content($mi-var-speaker-group); }
.#{$mi-css-prefix}-speaker-notes:before { content: mi-content($mi-var-speaker-notes); }
.#{$mi-css-prefix}-speaker-notes-off:before { content: mi-content($mi-var-speaker-notes-off); }
.#{$mi-css-prefix}-speaker-phone:before { content: mi-content($mi-var-speaker-phone); }
.#{$mi-css-prefix}-spellcheck:before { content: mi-content($mi-var-spellcheck); }
.#{$mi-css-prefix}-star-border:before { content: mi-content($mi-var-star-border); }
.#{$mi-css-prefix}-star-half:before { content: mi-content($mi-var-star-half); }
.#{$mi-css-prefix}-stars:before { content: mi-content($mi-var-stars); }
.#{$mi-css-prefix}-stay-primary-landscape:before { content: mi-content($mi-var-stay-primary-landscape); }
.#{$mi-css-prefix}-stop:before { content: mi-content($mi-var-stop); }
.#{$mi-css-prefix}-stop-screen-share:before { content: mi-content($mi-var-stop-screen-share); }
.#{$mi-css-prefix}-storage:before { content: mi-content($mi-var-storage); }
.#{$mi-css-prefix}-store-mall-directory:before { content: mi-content($mi-var-store-mall-directory); }
.#{$mi-css-prefix}-straighten:before { content: mi-content($mi-var-straighten); }
.#{$mi-css-prefix}-streetview:before { content: mi-content($mi-var-streetview); }
.#{$mi-css-prefix}-strikethrough-s:before { content: mi-content($mi-var-strikethrough-s); }
.#{$mi-css-prefix}-style:before { content: mi-content($mi-var-style); }
.#{$mi-css-prefix}-subdirectory-arrow-left:before { content: mi-content($mi-var-subdirectory-arrow-left); }
.#{$mi-css-prefix}-subdirectory-arrow-right:before { content: mi-content($mi-var-subdirectory-arrow-right); }
.#{$mi-css-prefix}-subject:before { content: mi-content($mi-var-subject); }
.#{$mi-css-prefix}-subscriptions:before { content: mi-content($mi-var-subscriptions); }
.#{$mi-css-prefix}-subtitles:before { content: mi-content($mi-var-subtitles); }
.#{$mi-css-prefix}-subway:before { content: mi-content($mi-var-subway); }
.#{$mi-css-prefix}-supervisor-account:before { content: mi-content($mi-var-supervisor-account); }
.#{$mi-css-prefix}-surround-sound:before { content: mi-content($mi-var-surround-sound); }
.#{$mi-css-prefix}-swap-calls:before { content: mi-content($mi-var-swap-calls); }
.#{$mi-css-prefix}-swap-horiz:before { content: mi-content($mi-var-swap-horiz); }
.#{$mi-css-prefix}-swap-vert:before { content: mi-content($mi-var-swap-vert); }
.#{$mi-css-prefix}-swap-vertical-circle:before { content: mi-content($mi-var-swap-vertical-circle); }
.#{$mi-css-prefix}-switch-camera:before { content: mi-content($mi-var-switch-camera); }
.#{$mi-css-prefix}-switch-video:before { content: mi-content($mi-var-switch-video); }
.#{$mi-css-prefix}-sync-disabled:before { content: mi-content($mi-var-sync-disabled); }
.#{$mi-css-prefix}-sync-problem:before { content: mi-content($mi-var-sync-problem); }
.#{$mi-css-prefix}-system-update:before { content: mi-content($mi-var-system-update); }
.#{$mi-css-prefix}-system-update-alt:before { content: mi-content($mi-var-system-update-alt); }
.#{$mi-css-prefix}-tab:before { content: mi-content($mi-var-tab); }
.#{$mi-css-prefix}-tab-unselected:before { content: mi-content($mi-var-tab-unselected); }
.#{$mi-css-prefix}-tablet:before { content: mi-content($mi-var-tablet); }
.#{$mi-css-prefix}-tablet-android:before { content: mi-content($mi-var-tablet-android); }
.#{$mi-css-prefix}-tablet-mac:before { content: mi-content($mi-var-tablet-mac); }
.#{$mi-css-prefix}-tap-and-play:before { content: mi-content($mi-var-tap-and-play); }
.#{$mi-css-prefix}-text-fields:before { content: mi-content($mi-var-text-fields); }
.#{$mi-css-prefix}-text-format:before { content: mi-content($mi-var-text-format); }
.#{$mi-css-prefix}-texture:before { content: mi-content($mi-var-texture); }
.#{$mi-css-prefix}-thumb-down:before { content: mi-content($mi-var-thumb-down); }
.#{$mi-css-prefix}-thumb-up:before { content: mi-content($mi-var-thumb-up); }
.#{$mi-css-prefix}-thumbs-up-down:before { content: mi-content($mi-var-thumbs-up-down); }
.#{$mi-css-prefix}-timelapse:before { content: mi-content($mi-var-timelapse); }
.#{$mi-css-prefix}-timeline:before { content: mi-content($mi-var-timeline); }
.#{$mi-css-prefix}-timer:before { content: mi-content($mi-var-timer); }
.#{$mi-css-prefix}-timer-10:before { content: mi-content($mi-var-timer-10); }
.#{$mi-css-prefix}-timer-3:before { content: mi-content($mi-var-timer-3); }
.#{$mi-css-prefix}-timer-off:before { content: mi-content($mi-var-timer-off); }
.#{$mi-css-prefix}-title:before { content: mi-content($mi-var-title); }
.#{$mi-css-prefix}-toc:before { content: mi-content($mi-var-toc); }
.#{$mi-css-prefix}-today:before { content: mi-content($mi-var-today); }
.#{$mi-css-prefix}-toll:before { content: mi-content($mi-var-toll); }
.#{$mi-css-prefix}-tonality:before { content: mi-content($mi-var-tonality); }
.#{$mi-css-prefix}-touch-app:before { content: mi-content($mi-var-touch-app); }
.#{$mi-css-prefix}-toys:before { content: mi-content($mi-var-toys); }
.#{$mi-css-prefix}-track-changes:before { content: mi-content($mi-var-track-changes); }
.#{$mi-css-prefix}-traffic:before { content: mi-content($mi-var-traffic); }
.#{$mi-css-prefix}-train:before { content: mi-content($mi-var-train); }
.#{$mi-css-prefix}-tram:before { content: mi-content($mi-var-tram); }
.#{$mi-css-prefix}-transfer-within-a-station:before { content: mi-content($mi-var-transfer-within-a-station); }
.#{$mi-css-prefix}-transform:before { content: mi-content($mi-var-transform); }
.#{$mi-css-prefix}-translate:before { content: mi-content($mi-var-translate); }
.#{$mi-css-prefix}-trending-down:before { content: mi-content($mi-var-trending-down); }
.#{$mi-css-prefix}-trending-flat:before { content: mi-content($mi-var-trending-flat); }
.#{$mi-css-prefix}-trending-up:before { content: mi-content($mi-var-trending-up); }
.#{$mi-css-prefix}-tune:before { content: mi-content($mi-var-tune); }
.#{$mi-css-prefix}-tv:before { content: mi-content($mi-var-tv); }
.#{$mi-css-prefix}-unarchive:before { content: mi-content($mi-var-unarchive); }
.#{$mi-css-prefix}-undo:before { content: mi-content($mi-var-undo); }
.#{$mi-css-prefix}-unfold-less:before { content: mi-content($mi-var-unfold-less); }
.#{$mi-css-prefix}-unfold-more:before { content: mi-content($mi-var-unfold-more); }
.#{$mi-css-prefix}-update:before { content: mi-content($mi-var-update); }
.#{$mi-css-prefix}-usb:before { content: mi-content($mi-var-usb); }
.#{$mi-css-prefix}-verified-user:before { content: mi-content($mi-var-verified-user); }
.#{$mi-css-prefix}-vertical-align-bottom:before { content: mi-content($mi-var-vertical-align-bottom); }
.#{$mi-css-prefix}-vertical-align-center:before { content: mi-content($mi-var-vertical-align-center); }
.#{$mi-css-prefix}-vertical-align-top:before { content: mi-content($mi-var-vertical-align-top); }
.#{$mi-css-prefix}-vibration:before { content: mi-content($mi-var-vibration); }
.#{$mi-css-prefix}-video-call:before { content: mi-content($mi-var-video-call); }
.#{$mi-css-prefix}-video-label:before { content: mi-content($mi-var-video-label); }
.#{$mi-css-prefix}-video-library:before { content: mi-content($mi-var-video-library); }
.#{$mi-css-prefix}-videocam:before { content: mi-content($mi-var-videocam); }
.#{$mi-css-prefix}-videocam-off:before { content: mi-content($mi-var-videocam-off); }
.#{$mi-css-prefix}-videogame-asset:before { content: mi-content($mi-var-videogame-asset); }
.#{$mi-css-prefix}-view-agenda:before { content: mi-content($mi-var-view-agenda); }
.#{$mi-css-prefix}-view-array:before { content: mi-content($mi-var-view-array); }
.#{$mi-css-prefix}-view-carousel:before { content: mi-content($mi-var-view-carousel); }
.#{$mi-css-prefix}-view-column:before { content: mi-content($mi-var-view-column); }
.#{$mi-css-prefix}-view-comfy:before { content: mi-content($mi-var-view-comfy); }
.#{$mi-css-prefix}-view-compact:before { content: mi-content($mi-var-view-compact); }
.#{$mi-css-prefix}-view-day:before { content: mi-content($mi-var-view-day); }
.#{$mi-css-prefix}-view-headline:before { content: mi-content($mi-var-view-headline); }
.#{$mi-css-prefix}-view-list:before { content: mi-content($mi-var-view-list); }
.#{$mi-css-prefix}-view-module:before { content: mi-content($mi-var-view-module); }
.#{$mi-css-prefix}-view-quilt:before { content: mi-content($mi-var-view-quilt); }
.#{$mi-css-prefix}-view-stream:before { content: mi-content($mi-var-view-stream); }
.#{$mi-css-prefix}-view-week:before { content: mi-content($mi-var-view-week); }
.#{$mi-css-prefix}-vignette:before { content: mi-content($mi-var-vignette); }
.#{$mi-css-prefix}-visibility-off:before { content: mi-content($mi-var-visibility-off); }
.#{$mi-css-prefix}-voice-chat:before { content: mi-content($mi-var-voice-chat); }
.#{$mi-css-prefix}-voicemail:before { content: mi-content($mi-var-voicemail); }
.#{$mi-css-prefix}-volume-down:before { content: mi-content($mi-var-volume-down); }
.#{$mi-css-prefix}-volume-mute:before { content: mi-content($mi-var-volume-mute); }
.#{$mi-css-prefix}-volume-off:before { content: mi-content($mi-var-volume-off); }
.#{$mi-css-prefix}-volume-up:before { content: mi-content($mi-var-volume-up); }
.#{$mi-css-prefix}-vpn-key:before { content: mi-content($mi-var-vpn-key); }
.#{$mi-css-prefix}-vpn-lock:before { content: mi-content($mi-var-vpn-lock); }
.#{$mi-css-prefix}-wallpaper:before { content: mi-content($mi-var-wallpaper); }
.#{$mi-css-prefix}-watch:before { content: mi-content($mi-var-watch); }
.#{$mi-css-prefix}-watch-later:before { content: mi-content($mi-var-watch-later); }
.#{$mi-css-prefix}-wb-auto:before { content: mi-content($mi-var-wb-auto); }
.#{$mi-css-prefix}-wb-incandescent:before { content: mi-content($mi-var-wb-incandescent); }
.#{$mi-css-prefix}-wb-iridescent:before { content: mi-content($mi-var-wb-iridescent); }
.#{$mi-css-prefix}-wb-sunny:before { content: mi-content($mi-var-wb-sunny); }
.#{$mi-css-prefix}-wc:before { content: mi-content($mi-var-wc); }
.#{$mi-css-prefix}-web:before { content: mi-content($mi-var-web); }
.#{$mi-css-prefix}-web-asset:before { content: mi-content($mi-var-web-asset); }
.#{$mi-css-prefix}-weekend:before { content: mi-content($mi-var-weekend); }
.#{$mi-css-prefix}-whatshot:before { content: mi-content($mi-var-whatshot); }
.#{$mi-css-prefix}-widgets:before { content: mi-content($mi-var-widgets); }
.#{$mi-css-prefix}-wifi:before { content: mi-content($mi-var-wifi); }
.#{$mi-css-prefix}-wifi-lock:before { content: mi-content($mi-var-wifi-lock); }
.#{$mi-css-prefix}-wifi-tethering:before { content: mi-content($mi-var-wifi-tethering); }
.#{$mi-css-prefix}-work:before { content: mi-content($mi-var-work); }
.#{$mi-css-prefix}-wrap-text:before { content: mi-content($mi-var-wrap-text); }
.#{$mi-css-prefix}-youtube-searched-for:before { content: mi-content($mi-var-youtube-searched-for); }
.#{$mi-css-prefix}-zoom-in:before { content: mi-content($mi-var-zoom-in); }
.#{$mi-css-prefix}-zoom-out:before { content: mi-content($mi-var-zoom-out); }
.#{$mi-css-prefix}-zoom-out-map:before { content: mi-content($mi-var-zoom-out-map); }
