/* ------------------------------------------------------------------------------
 *
 *  # Material set mixins
 *
 *  Custom mixins for Material icon set only.
 *
 * ---------------------------------------------------------------------------- */


// Rotate icon
@mixin mi-icon-rotate($degrees, $rotation) {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation})";
    transform: rotate($degrees);
}

// Flip icon
@mixin mi-icon-flip($horiz, $vert, $rotation) {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation}, mirror=1)";
    transform: scale($horiz, $vert);
}
