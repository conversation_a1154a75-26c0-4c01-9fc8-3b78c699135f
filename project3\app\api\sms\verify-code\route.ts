import { NextRequest, NextResponse } from 'next/server';
import { detectBot, validateOrigin, checkSuspiciousIP } from '@/lib/security-utils';
import { formatPhoneNumber, isValidPhoneNumber } from '@/lib/phone-utils';
import { getVerificationCode, deleteVerificationCode, trackVerificationAttempt, storeVerificationCode } from '@/lib/verification-store';

export async function POST(request: NextRequest) {
  try {
    // Security checks
    const headers = request.headers;
    const ip = headers.get('x-forwarded-for') || headers.get('x-real-ip') || 'unknown';

    // 1. Bot detection
    const botCheck = detectBot(headers);
    if (botCheck.isBot) {
      console.warn(`Bot detected in verification: ${botCheck.reason} from IP: ${ip}`);
      return NextResponse.json(
        { error: 'Request blocked for security reasons' },
        { status: 403 }
      );
    }

    // 2. Origin validation
    const originCheck = validateOrigin(headers);
    if (!originCheck.valid) {
      console.warn(`Invalid origin in verification: ${originCheck.reason} from IP: ${ip}`);
      return NextResponse.json(
        { error: 'Request blocked for security reasons' },
        { status: 403 }
      );
    }

    // 3. IP address check
    const ipCheck = checkSuspiciousIP(ip);
    if (ipCheck.suspicious) {
      console.warn(`Suspicious IP in verification: ${ipCheck.reason} - ${ip}`);
      return NextResponse.json(
        { error: 'Request blocked for security reasons' },
        { status: 403 }
      );
    }

    const { phoneNumber: rawPhoneNumber, code } = await request.json();

    if (!rawPhoneNumber || !code) {
      return NextResponse.json(
        { error: 'Phone number and verification code are required' },
        { status: 400 }
      );
    }

    // Format phone number consistently
    const phoneNumber = formatPhoneNumber(rawPhoneNumber);

    // Validate phone number format
    if (!isValidPhoneNumber(phoneNumber)) {
      return NextResponse.json(
        { error: 'Invalid phone number format' },
        { status: 400 }
      );
    }

    // Track verification attempt and check rate limits
    const attemptData = await trackVerificationAttempt(phoneNumber);
    if (attemptData.count > 5) {
      return NextResponse.json(
        { 
          error: 'Too many verification attempts. Please try again later.',
          resetTime: attemptData.resetTime
        },
        { status: 429 }
      );
    }

    // Call .NET API to verify the code
    const rawBase = process.env.ADMIN_BASE_URL || process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com';
    const base = rawBase.endsWith('/') ? rawBase.slice(0, -1) : rawBase;
    const requestBody = JSON.stringify({
      PhoneNumber: phoneNumber,        // Capital P to match backend
      VerificationCode: code          // Capital V and C to match backend
    });
    const verifyResponse = await fetch(`${base}/api/v1/verification/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': requestBody.length.toString(),
        'User-Agent': 'NextJS-Frontend/1.0',
        'Accept': 'application/json',
      },
      body: requestBody
    });

    if (!verifyResponse.ok) {
      throw new Error('Failed to verify code with backend');
    }

    const verifyResult = await verifyResponse.json();

    if (verifyResult.success && verifyResult.isValid) {
      console.log(`Phone number verified successfully: ${phoneNumber.slice(0, 5)}*****`);
      
      return NextResponse.json({
        success: true,
        message: 'Phone number verified successfully',
      });
    } else {
      console.warn(`Verification failed for: ${phoneNumber.slice(0, 5)}***** - ${verifyResult.message}`);
      
      return NextResponse.json(
        { 
          error: verifyResult.message || 'Invalid verification code',
          remainingAttempts: verifyResult.attemptCount ? (3 - verifyResult.attemptCount) : 0
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Code verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

