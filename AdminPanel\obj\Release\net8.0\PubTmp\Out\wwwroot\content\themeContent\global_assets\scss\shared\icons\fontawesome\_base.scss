/* ------------------------------------------------------------------------------
 *
 *  # Font Awesome icons base
 *
 *  Base styles for Font Awesome. Include font import, class definition and other options
 *
 * ---------------------------------------------------------------------------- */


// Font definition
// -------------------------

// Solid icons
@font-face {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 900;
    src: url('#{$fa-font-path}/fa-solid-900.eot');
    src: url('#{$fa-font-path}/fa-solid-900.eot?#iefix') format('embedded-opentype'),
    url('#{$fa-font-path}/fa-solid-900.woff2') format('woff2'),
    url('#{$fa-font-path}/fa-solid-900.woff') format('woff'),
    url('#{$fa-font-path}/fa-solid-900.ttf') format('truetype'),
    url('#{$fa-font-path}/fa-solid-900.svg#fontawesome') format('svg');
}

// Normal icons
@font-face {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 400;
    src: url('#{$fa-font-path}/fa-regular-400.eot');
    src: url('#{$fa-font-path}/fa-regular-400.eot?#iefix') format('embedded-opentype'),
    url('#{$fa-font-path}/fa-regular-400.woff2') format('woff2'),
    url('#{$fa-font-path}/fa-regular-400.woff') format('woff'),
    url('#{$fa-font-path}/fa-regular-400.ttf') format('truetype'),
    url('#{$fa-font-path}/fa-regular-400.svg#fontawesome') format('svg');
}

// Brand icons
@font-face {
    font-family: 'Font Awesome 5 Brands';
    font-style: normal;
    font-weight: normal;
    src: url('#{$fa-font-path}/fa-brands-400.eot');
    src: url('#{$fa-font-path}/fa-brands-400.eot?#iefix') format('embedded-opentype'),
    url('#{$fa-font-path}/fa-brands-400.woff2') format('woff2'),
    url('#{$fa-font-path}/fa-brands-400.woff') format('woff'),
    url('#{$fa-font-path}/fa-brands-400.ttf') format('truetype'),
    url('#{$fa-font-path}/fa-brands-400.svg#fontawesome') format('svg');
}


// Base Class Definition
// -------------------------

// Base
.#{$fa-css-prefix},
.fas,
.far,
.fal,
.fab {
    display: inline-block;
    font-size: $icon-font-size;
    font-style: normal;
    font-variant: normal;
    min-width: 1em;
    vertical-align: middle;
    position: relative;
    top: -1px;
    text-rendering: auto;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

// Solid icons
.fa,
.fas {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

// Regular icons
.far {
    font-family: 'Font Awesome 5 Free';
    font-weight: 400;
}

// Brand icons
.fab {
    font-family: 'Font Awesome 5 Brands';
}


// Icon Sizes
// -------------------------

// 4 additional sizes: .fa-2x -> .fa-5x
@for $i from 1 through 5 {
    .#{$fa-css-prefix}-#{$i}x {
        font-size: $i * $icon-font-size;
    }
}


// Rotated & Flipped Icons
// -------------------------

// Rotation
.#{$fa-css-prefix}-rotate-90  { @include fa-icon-rotate(90deg, 1);  }
.#{$fa-css-prefix}-rotate-180 { @include fa-icon-rotate(180deg, 2); }
.#{$fa-css-prefix}-rotate-270 { @include fa-icon-rotate(270deg, 3); }

// Flip
.#{$fa-css-prefix}-flip-horizontal { @include fa-icon-flip(-1, 1, 0); }
.#{$fa-css-prefix}-flip-vertical   { @include fa-icon-flip(1, -1, 2); }
.#{$fa-css-prefix}-flip-horizontal.#{$fa-css-prefix}-flip-vertical { @include fa-icon-flip(-1, -1, 2); }
