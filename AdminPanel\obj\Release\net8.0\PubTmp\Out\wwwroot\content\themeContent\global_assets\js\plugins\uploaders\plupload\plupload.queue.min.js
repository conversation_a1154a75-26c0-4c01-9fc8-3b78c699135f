!function(e,t){function i(e){return t.translate(e)||e}function s(t,s){s.contents().each(function(t,i){i=e(i),i.is(".plupload")||i.remove()}),s.prepend('<div class="plupload_wrapper plupload_scroll"><div id="'+t+'_container" class="plupload_container">'+'<div class="plupload">'+'<div class="plupload_header">'+'<div class="plupload_header_content">'+'<div class="plupload_header_title">'+i("Select files")+"</div>"+'<div class="plupload_header_text">'+i("Add files to the upload queue and click the start button.")+"</div>"+"</div>"+"</div>"+'<div class="plupload_content">'+'<div class="plupload_filelist_header">'+'<div class="plupload_file_name">'+i("Filename")+"</div>"+'<div class="plupload_file_action">&nbsp;</div>'+'<div class="plupload_file_status"><span>'+i("Status")+"</span></div>"+'<div class="plupload_file_size">'+i("Size")+"</div>"+'<div class="plupload_clearer">&nbsp;</div>'+"</div>"+'<ul id="'+t+'_filelist" class="plupload_filelist"></ul>'+'<div class="plupload_filelist_footer">'+'<div class="plupload_file_name">'+'<div class="plupload_buttons">'+'<a href="#" class="plupload_button plupload_add" id="'+t+'_browse">'+i("Add Files")+"</a>"+'<a href="#" class="plupload_button plupload_start">'+i("Start Upload")+"</a>"+"</div>"+'<span class="plupload_upload_status"></span>'+"</div>"+'<div class="plupload_file_action"></div>'+'<div class="plupload_file_status"><span class="plupload_total_status">0%</span></div>'+'<div class="plupload_file_size"><span class="plupload_total_file_size">0 b</span></div>'+'<div class="plupload_progress">'+'<div class="plupload_progress_container">'+'<div class="plupload_progress_bar"></div>'+"</div>"+"</div>"+'<div class="plupload_clearer">&nbsp;</div>'+"</div>"+"</div>"+"</div>"+"</div>"+'<input type="hidden" id="'+t+'_count" name="'+t+'_count" value="0" />'+"</div>")}var l={};e.fn.pluploadQueue=function(a){return a?(this.each(function(){function n(i){var s;i.status==t.DONE&&(s="plupload_done"),i.status==t.FAILED&&(s="plupload_failed"),i.status==t.QUEUED&&(s="plupload_delete"),i.status==t.UPLOADING&&(s="plupload_uploading");var l=e("#"+i.id).attr("class",s).find("a").css("display","block");i.hint&&l.attr("title",i.hint)}function o(){e("span.plupload_total_status",p).html(u.total.percent+"%"),e("div.plupload_progress_bar",p).css("width",u.total.percent+"%"),e("span.plupload_upload_status",p).html(t.sprintf(i("Uploaded %d/%d files"),u.total.uploaded,u.files.length))}function r(){var s,l=e("ul.plupload_filelist",p).html(""),a=0;e.each(u.files,function(i,o){s="",o.status==t.DONE&&(o.target_name&&(s+='<input type="hidden" name="'+c+"_"+a+'_tmpname" value="'+t.xmlEncode(o.target_name)+'" />'),s+='<input type="hidden" name="'+c+"_"+a+'_name" value="'+t.xmlEncode(o.name)+'" />',s+='<input type="hidden" name="'+c+"_"+a+'_status" value="'+(o.status==t.DONE?"done":"failed")+'" />',a++,e("#"+c+"_count").val(a)),l.append('<li id="'+o.id+'">'+'<div class="plupload_file_name"><span>'+o.name+"</span></div>"+'<div class="plupload_file_action"><a href="#"></a></div>'+'<div class="plupload_file_status">'+o.percent+"%</div>"+'<div class="plupload_file_size">'+t.formatSize(o.size)+"</div>"+'<div class="plupload_clearer">&nbsp;</div>'+s+"</li>"),n(o),e("#"+o.id+".plupload_delete a").click(function(t){e("#"+o.id).remove(),u.removeFile(o),t.preventDefault()})}),e("span.plupload_total_file_size",p).html(t.formatSize(u.total.size)),0===u.total.queued?e("span.plupload_add_text",p).html(i("Add Files")):e("span.plupload_add_text",p).html(t.sprintf(i("%d files queued"),u.total.queued)),e("a.plupload_start",p).toggleClass("plupload_disabled",u.files.length==u.total.uploaded+u.total.failed),l[0].scrollTop=l[0].scrollHeight,o(),!u.files.length&&u.features.dragdrop&&u.settings.dragdrop&&e("#"+c+"_filelist").append('<li class="plupload_droptext">'+i("Drag files here.")+"</li>")}function d(){delete l[c],u.destroy(),p.html(_),u=p=_=null}var u,p,c,_;p=e(this),c=p.attr("id"),c||(c=t.guid(),p.attr("id",c)),_=p.html(),s(c,p),a=e.extend({dragdrop:!0,browse_button:c+"_browse",container:c},a),a.dragdrop&&(a.drop_element=c+"_filelist"),u=new t.Uploader(a),l[c]=u,u.bind("UploadFile",function(t,i){e("#"+i.id).addClass("plupload_current_file")}),u.bind("Init",function(t,i){!a.unique_names&&a.rename&&p.on("click","#"+c+"_filelist div.plupload_file_name span",function(i){var s,l,a,n=e(i.target),o="",r=n.closest("li");r.hasClass("plupload_delete")&&(s=t.getFile(n.parents("li")[0].id),a=s.name,l=/^(.+)(\.[^.]+)$/.exec(a),l&&(a=l[1],o=l[2]),n.hide().after('<input type="text" />'),n.next().val(a).focus().blur(function(){n.show().next().remove()}).keydown(function(t){var i=e(this);13==t.keyCode&&(t.preventDefault(),s.name=i.val()+o,n.html(s.name),i.blur())}))}),e("#"+c+"_container").attr("title","Using runtime: "+i.runtime),e("a.plupload_start",p).click(function(t){e(this).hasClass("plupload_disabled")||u.start(),t.preventDefault()}),e("a.plupload_stop",p).click(function(e){e.preventDefault(),u.stop()}),e("a.plupload_start",p).addClass("plupload_disabled")}),u.bind("Error",function(s,l){var a,n=l.file;n&&(a=l.message,l.details&&(a+=" ("+l.details+")"),l.code==t.FILE_SIZE_ERROR&&alert(i("Error: File too large:")+" "+n.name),l.code==t.FILE_EXTENSION_ERROR&&alert(i("Error: Invalid file extension:")+" "+n.name),n.hint=a,e("#"+n.id).attr("class","plupload_failed").find("a").css("display","block").attr("title",a)),l.code===t.INIT_ERROR&&setTimeout(function(){d()},1)}),u.bind("PostInit",function(t){t.settings.dragdrop&&t.features.dragdrop&&e("#"+c+"_filelist").append('<li class="plupload_droptext">'+i("Drag files here.")+"</li>")}),u.init(),u.bind("StateChanged",function(){u.state===t.STARTED?(e("li.plupload_delete a,div.plupload_buttons",p).hide(),u.disableBrowse(!0),e("span.plupload_upload_status,div.plupload_progress,a.plupload_stop",p).css("display","block"),e("span.plupload_upload_status",p).html("Uploaded "+u.total.uploaded+"/"+u.files.length+" files"),a.multiple_queues&&e("span.plupload_total_status,span.plupload_total_file_size",p).show()):(r(),e("a.plupload_stop,div.plupload_progress",p).hide(),e("a.plupload_delete",p).css("display","block"),a.multiple_queues&&u.total.uploaded+u.total.failed==u.files.length&&(e(".plupload_buttons,.plupload_upload_status",p).css("display","inline"),u.disableBrowse(!1),e(".plupload_start",p).addClass("plupload_disabled"),e("span.plupload_total_status,span.plupload_total_file_size",p).hide()))}),u.bind("FilesAdded",r),u.bind("FilesRemoved",function(){var t=e("#"+c+"_filelist").scrollTop();r(),e("#"+c+"_filelist").scrollTop(t)}),u.bind("FileUploaded",function(e,t){n(t)}),u.bind("UploadProgress",function(t,i){e("#"+i.id+" div.plupload_file_status",p).html(i.percent+"%"),n(i),o()}),a.setup&&a.setup(u)}),this):l[e(this[0]).attr("id")]}}(jQuery,plupload);