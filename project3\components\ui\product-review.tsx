'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { Star, Send } from 'lucide-react';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { useUser } from '@/contexts/user-context';
import Swal from 'sweetalert2';

interface ProductReviewProps {
  productId: number;
  productName: string;
  onReviewSubmitted?: () => void;
}

export default function ProductReview({ productId, productName, onReviewSubmitted }: ProductReviewProps) {
  const { user, token } = useUser();
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [title, setTitle] = useState('');
  const [body, setBody] = useState('');
  const [loading, setLoading] = useState(false);

  const handleStarClick = (starRating: number) => {
    setRating(starRating);
  };

  const handleStarHover = (starRating: number) => {
    setHoveredRating(starRating);
  };

  const handleStarLeave = () => {
    setHoveredRating(0);
  };

  const handleSubmitReview = async () => {
    if (!rating || !title.trim() || !body.trim()) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Information',
        text: 'Please provide a rating, title, and review text.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    if (!user?.UserID && !user?.UserId) {
      Swal.fire({
        icon: 'error',
        title: 'Authentication Required',
        text: 'Please log in to submit a review.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    if (!token) {
      Swal.fire({
        icon: 'error',
        title: 'Authentication Required',
        text: 'Please log in to submit a review.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    setLoading(true);

    try {
      const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
      };

      const param = {
        requestParameters: {
          ProductID: productId,
          Title: title.trim(),
          Body: body.trim(),
          Rating: rating,
          ReviewerName: `${user.FirstName || ''} ${user.LastName || ''}`.trim() || user.Email || 'Anonymous',
          ReviewerEmail: user.Email || user.EmailAddress || '',
          // UserID removed - will be automatically extracted from JWT token
        }
      };

      console.log('🔍 Review submission: Sending review data:', param);
      console.log('🔍 Review submission: User data:', {
        userId: user.UserID || user.UserId,
        email: user.Email || user.EmailAddress,
        name: `${user.FirstName || ''} ${user.LastName || ''}`.trim()
      });

      // Use the new API route for better JWT token handling
      const response = await fetch('/api/reviews/insert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}` // Get token from user context
        },
        body: JSON.stringify(param)
      });

      const responseData = await response.json();

      console.log('🔍 Review submission: API response status:', response.status);
      console.log('🔍 Review submission: Response data:', responseData);

      if (response.ok && responseData && !responseData.errorMessage) {
        await Swal.fire({
          icon: 'success',
          title: 'Review Submitted!',
          text: 'Thank you for your review. It will be visible after approval.',
          confirmButtonColor: '#10b981'
        });

        // Reset form
        setRating(0);
        setTitle('');
        setBody('');

        // Call callback if provided
        if (onReviewSubmitted) {
          onReviewSubmitted();
        }
      } else {
        throw new Error(responseData?.errorMessage || responseData?.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      Swal.fire({
        icon: 'error',
        title: 'Submission Failed',
        text: error instanceof Error ? error.message : 'Failed to submit review. Please try again.',
        confirmButtonColor: '#ef4444'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="p-6">
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">Write a Review</h3>
          <p className="text-sm text-muted-foreground">
            Share your experience with {productName}
          </p>
        </div>

        {/* Rating Stars */}
        <div>
          <label className="block text-sm font-medium mb-2">Rating *</label>
          <div className="flex items-center gap-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => handleStarClick(star)}
                onMouseEnter={() => handleStarHover(star)}
                onMouseLeave={handleStarLeave}
                className="p-1 transition-colors"
                disabled={loading}
              >
                <Star
                  className={`h-6 w-6 ${
                    star <= (hoveredRating || rating)
                      ? 'fill-yellow-400 text-yellow-400'
                      : 'text-gray-300'
                  }`}
                />
              </button>
            ))}
            <span className="ml-2 text-sm text-muted-foreground">
              {rating > 0 && `${rating} star${rating !== 1 ? 's' : ''}`}
            </span>
          </div>
        </div>

        {/* Review Title */}
        <div>
          <label className="block text-sm font-medium mb-2">Review Title *</label>
          <Input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Summarize your experience"
            disabled={loading}
            maxLength={100}
          />
          <p className="text-xs text-muted-foreground mt-1">
            {title.length}/100 characters
          </p>
        </div>

        {/* Review Body */}
        <div>
          <label className="block text-sm font-medium mb-2">Your Review *</label>
          <Textarea
            value={body}
            onChange={(e) => setBody(e.target.value)}
            placeholder="Tell others about your experience with this product..."
            disabled={loading}
            rows={4}
            maxLength={500}
          />
          <p className="text-xs text-muted-foreground mt-1">
            {body.length}/500 characters
          </p>
        </div>

        {/* Submit Button */}
        <Button
          onClick={handleSubmitReview}
          disabled={loading || !rating || !title.trim() || !body.trim()}
          className="w-full"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Submitting...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Submit Review
            </>
          )}
        </Button>

        <p className="text-xs text-muted-foreground text-center">
          Reviews are moderated and will be published after approval.
        </p>
      </div>
    </Card>
  );
}