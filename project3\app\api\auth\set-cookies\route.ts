import { NextRequest, NextResponse } from 'next/server';
import CryptoJS from 'crypto-js';

// Server-only secret (do NOT expose NEXT_PUBLIC here)
const SECRET_KEY = process.env.ENCRYPTION_KEY || process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'change-me';

function encryptJson(data: any) {
  const plaintext = JSON.stringify(data);
  const encrypted = CryptoJS.AES.encrypt(plaintext, SECRET_KEY).toString();
  return encrypted;
}

function pickPublicUser(user: any) {
  if (!user) return null;
  return {
    UserId: user.UserId || user.UserID,
    UserName: user.UserName || user.Username || user.Name,
    Email: user.Email || user.EmailAddress,
    FirstName: user.FirstName,
    LastName: user.LastName,
    PhoneNumber: user.PhoneNumber || user.PhoneNo || user.MobileNo,
    AvatarUrl: user.AvatarUrl || user.ImageUrl || user.ProfileImageUrl || null,
    UserTypeID: user.UserTypeID || user.UserTypeId || user.UserType || null,
    // Include gender and category information for account page
    Gender: user.Gender || user.gender || "",
    CategoryID: user.CategoryID || user.CategoryId || user.category_id || user.categoryId || "",
    CategoryId: user.CategoryId || user.CategoryID || user.category_id || user.categoryId || "",
    SpecialistId: user.SpecialistId || user.specialist_id || user.CategoryId || user.CategoryID || "",
    CatID: user.CatID || user.CategoryId || user.CategoryID || "",
    Pointno: user.Pointno || user.Points || 0,
  };
}

export async function POST(request: NextRequest) {
  try {
    const { user, token } = await request.json();

    const response = NextResponse.json({ success: true });

    const publicUser = pickPublicUser(user);
    const domain = process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DOMAIN ? process.env.NEXT_PUBLIC_DOMAIN : undefined;

    // Client-visible minimal cookie
    response.cookies.set('auth_user_public', JSON.stringify(publicUser), {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
      maxAge: 7 * 24 * 60 * 60,
      domain,
    });

    // HttpOnly encrypted full profile
    response.cookies.set('auth_user', encryptJson(user), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 7 * 24 * 60 * 60,
      domain,
    });

    // Set token cookie (HttpOnly for security)
    if (token) {
      response.cookies.set('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge: 7 * 24 * 60 * 60,
        domain,
      });

      response.cookies.set('auth_token_client', token, {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge: 7 * 24 * 60 * 60,
        domain,
      });
    }

    console.log('✅ Cookies set successfully:', {
      user: user?.Email || user?.EmailAddress,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'none'
    });

    return response;
  } catch (error) {
    console.error('Error setting cookies:', error);
    return NextResponse.json(
      { error: 'Failed to set cookies' },
      { status: 500 }
    );
  }
}