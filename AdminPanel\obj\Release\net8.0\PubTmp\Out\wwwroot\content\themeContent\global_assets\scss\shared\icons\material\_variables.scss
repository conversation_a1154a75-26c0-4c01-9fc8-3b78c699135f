/* ------------------------------------------------------------------------------
 *
 *  # Material set variables
 *
 *  Includes some set-specific functions and variables (base and icons)
 *
 * ---------------------------------------------------------------------------- */


// Base
$mi-font-path:                './fonts' !default;
$mi-css-prefix:               'mi' !default;

// Convenience function used to set content property
@function mi-content($mi-var) {
	@return unquote("\"#{ $mi-var }\"");
}

// Icons
$mi-var-3d-rotation: \e900;
$mi-var-ac-unit: \e901;
$mi-var-alarm: \e902;
$mi-var-access-alarms: \e903;
$mi-var-schedule: \e904;
$mi-var-accessibility: \e905;
$mi-var-accessible: \e906;
$mi-var-account-balance: \e907;
$mi-var-account-balance-wallet: \e908;
$mi-var-account-box: \e909;
$mi-var-account-circle: \e90a;
$mi-var-adb: \e90b;
$mi-var-add: \e90c;
$mi-var-add-a-photo: \e90d;
$mi-var-alarm-add: \e90e;
$mi-var-add-alert: \e90f;
$mi-var-add-box: \e910;
$mi-var-add-circle: \e911;
$mi-var-control-point: \e912;
$mi-var-add-location: \e913;
$mi-var-add-shopping-cart: \e914;
$mi-var-queue: \e915;
$mi-var-add-to-queue: \e916;
$mi-var-adjust: \e917;
$mi-var-airline-seat-flat: \e918;
$mi-var-airline-seat-flat-angled: \e919;
$mi-var-airline-seat-individual-suite: \e91a;
$mi-var-airline-seat-legroom-extra: \e91b;
$mi-var-airline-seat-legroom-normal: \e91c;
$mi-var-airline-seat-legroom-reduced: \e91d;
$mi-var-airline-seat-recline-extra: \e91e;
$mi-var-airline-seat-recline-normal: \e91f;
$mi-var-flight: \e920;
$mi-var-airplanemode-inactive: \e921;
$mi-var-airplay: \e922;
$mi-var-airport-shuttle: \e923;
$mi-var-alarm-off: \e924;
$mi-var-alarm-on: \e925;
$mi-var-album: \e926;
$mi-var-all-inclusive: \e927;
$mi-var-all-out: \e928;
$mi-var-android: \e929;
$mi-var-announcement: \e92a;
$mi-var-apps: \e92b;
$mi-var-archive: \e92c;
$mi-var-arrow-back: \e92d;
$mi-var-arrow-downward: \e92e;
$mi-var-arrow-drop-down: \e92f;
$mi-var-arrow-drop-down-circle: \e930;
$mi-var-arrow-drop-up: \e931;
$mi-var-arrow-forward: \e932;
$mi-var-arrow-upward: \e933;
$mi-var-art-track: \e934;
$mi-var-aspect-ratio: \e935;
$mi-var-poll: \e936;
$mi-var-assignment: \e937;
$mi-var-assignment-ind: \e938;
$mi-var-assignment-late: \e939;
$mi-var-assignment-return: \e93a;
$mi-var-assignment-returned: \e93b;
$mi-var-assignment-turned-in: \e93c;
$mi-var-assistant: \e93d;
$mi-var-flag: \e93e;
$mi-var-attach-file: \e93f;
$mi-var-attach-money: \e940;
$mi-var-attachment: \e941;
$mi-var-audiotrack: \e942;
$mi-var-autorenew: \e943;
$mi-var-av-timer: \e944;
$mi-var-backspace: \e945;
$mi-var-cloud-upload: \e946;
$mi-var-battery-alert: \e947;
$mi-var-battery-charging-full: \e948;
$mi-var-battery-std: \e949;
$mi-var-battery-unknown: \e94a;
$mi-var-beach-access: \e94b;
$mi-var-beenhere: \e94c;
$mi-var-block: \e94d;
$mi-var-bluetooth: \e94e;
$mi-var-bluetooth-searching: \e94f;
$mi-var-bluetooth-connected: \e950;
$mi-var-bluetooth-disabled: \e951;
$mi-var-blur-circular: \e952;
$mi-var-blur-linear: \e953;
$mi-var-blur-off: \e954;
$mi-var-blur-on: \e955;
$mi-var-class: \e956;
$mi-var-turned-in: \e957;
$mi-var-turned-in-not: \e958;
$mi-var-border-all: \e959;
$mi-var-border-bottom: \e95a;
$mi-var-border-clear: \e95b;
$mi-var-border-color: \e95c;
$mi-var-border-horizontal: \e95d;
$mi-var-border-inner: \e95e;
$mi-var-border-left: \e95f;
$mi-var-border-outer: \e960;
$mi-var-border-right: \e961;
$mi-var-border-style: \e962;
$mi-var-border-top: \e963;
$mi-var-border-vertical: \e964;
$mi-var-branding-watermark: \e965;
$mi-var-brightness-1: \e966;
$mi-var-brightness-2: \e967;
$mi-var-brightness-3: \e968;
$mi-var-brightness-4: \e969;
$mi-var-brightness-low: \e96a;
$mi-var-brightness-medium: \e96b;
$mi-var-brightness-high: \e96c;
$mi-var-brightness-auto: \e96d;
$mi-var-broken-image: \e96e;
$mi-var-brush: \e96f;
$mi-var-bubble-chart: \e970;
$mi-var-bug-report: \e971;
$mi-var-build: \e972;
$mi-var-burst-mode: \e973;
$mi-var-domain: \e974;
$mi-var-business-center: \e975;
$mi-var-cached: \e976;
$mi-var-cake: \e977;
$mi-var-phone: \e978;
$mi-var-call-end: \e979;
$mi-var-call-made: \e97a;
$mi-var-merge-type: \e97b;
$mi-var-call-missed: \e97c;
$mi-var-call-missed-outgoing: \e97d;
$mi-var-call-received: \e97e;
$mi-var-call-split: \e97f;
$mi-var-call-to-action: \e980;
$mi-var-camera: \e981;
$mi-var-photo-camera: \e982;
$mi-var-camera-enhance: \e983;
$mi-var-camera-front: \e984;
$mi-var-camera-rear: \e985;
$mi-var-camera-roll: \e986;
$mi-var-cancel: \e987;
$mi-var-redeem: \e988;
$mi-var-card-membership: \e989;
$mi-var-card-travel: \e98a;
$mi-var-casino: \e98b;
$mi-var-cast: \e98c;
$mi-var-cast-connected: \e98d;
$mi-var-center-focus-strong: \e98e;
$mi-var-center-focus-weak: \e98f;
$mi-var-change-history: \e990;
$mi-var-chat: \e991;
$mi-var-chat-bubble: \e992;
$mi-var-chat-bubble-outline: \e993;
$mi-var-check: \e994;
$mi-var-check-box: \e995;
$mi-var-check-box-outline-blank: \e996;
$mi-var-check-circle: \e997;
$mi-var-navigate-before: \e998;
$mi-var-navigate-next: \e999;
$mi-var-child-care: \e99a;
$mi-var-child-friendly: \e99b;
$mi-var-chrome-reader-mode: \e99c;
$mi-var-close: \e99d;
$mi-var-clear-all: \e99e;
$mi-var-closed-caption: \e99f;
$mi-var-wb-cloudy: \e9a0;
$mi-var-cloud-circle: \e9a1;
$mi-var-cloud-done: \e9a2;
$mi-var-cloud-download: \e9a3;
$mi-var-cloud-off: \e9a4;
$mi-var-cloud-queue: \e9a5;
$mi-var-code: \e9a6;
$mi-var-photo-library: \e9a7;
$mi-var-collections-bookmark: \e9a8;
$mi-var-palette: \e9a9;
$mi-var-colorize: \e9aa;
$mi-var-comment: \e9ab;
$mi-var-compare: \e9ac;
$mi-var-compare-arrows: \e9ad;
$mi-var-laptop: \e9ae;
$mi-var-confirmation-number: \e9af;
$mi-var-contact-mail: \e9b0;
$mi-var-contact-phone: \e9b1;
$mi-var-contacts: \e9b2;
$mi-var-content-copy: \e9b3;
$mi-var-content-cut: \e9b4;
$mi-var-content-paste: \e9b5;
$mi-var-control-point-duplicate: \e9b6;
$mi-var-copyright: \e9b7;
$mi-var-mode-edit: \e9b8;
$mi-var-create-new-folder: \e9b9;
$mi-var-payment: \e9ba;
$mi-var-crop: \e9bb;
$mi-var-crop-16-9: \e9bc;
$mi-var-crop-3-2: \e9bd;
$mi-var-crop-landscape: \e9be;
$mi-var-crop-7-5: \e9bf;
$mi-var-crop-din: \e9c0;
$mi-var-crop-free: \e9c1;
$mi-var-crop-original: \e9c2;
$mi-var-crop-portrait: \e9c3;
$mi-var-crop-rotate: \e9c4;
$mi-var-crop-square: \e9c5;
$mi-var-dashboard: \e9c6;
$mi-var-data-usage: \e9c7;
$mi-var-date-range: \e9c8;
$mi-var-dehaze: \e9c9;
$mi-var-delete: \e9ca;
$mi-var-delete-forever: \e9cb;
$mi-var-delete-sweep: \e9cc;
$mi-var-description: \e9cd;
$mi-var-desktop-mac: \e9ce;
$mi-var-desktop-windows: \e9cf;
$mi-var-details: \e9d0;
$mi-var-developer-board: \e9d1;
$mi-var-developer-mode: \e9d2;
$mi-var-device-hub: \e9d3;
$mi-var-phonelink: \e9d4;
$mi-var-devices-other: \e9d5;
$mi-var-dialer-sip: \e9d6;
$mi-var-dialpad: \e9d7;
$mi-var-directions: \e9d8;
$mi-var-directions-bike: \e9d9;
$mi-var-directions-boat: \e9da;
$mi-var-directions-bus: \e9db;
$mi-var-directions-car: \e9dc;
$mi-var-directions-railway: \e9dd;
$mi-var-directions-run: \e9de;
$mi-var-directions-transit: \e9df;
$mi-var-directions-walk: \e9e0;
$mi-var-disc-full: \e9e1;
$mi-var-dns: \e9e2;
$mi-var-not-interested: \e9e3;
$mi-var-do-not-disturb-alt: \e9e4;
$mi-var-do-not-disturb-off: \e9e5;
$mi-var-remove-circle: \e9e6;
$mi-var-dock: \e9e7;
$mi-var-done: \e9e8;
$mi-var-done-all: \e9e9;
$mi-var-donut-large: \e9ea;
$mi-var-donut-small: \e9eb;
$mi-var-drafts: \e9ec;
$mi-var-drag-handle: \e9ed;
$mi-var-time-to-leave: \e9ee;
$mi-var-dvr: \e9ef;
$mi-var-edit-location: \e9f0;
$mi-var-eject: \e9f1;
$mi-var-markunread: \e9f2;
$mi-var-enhanced-encryption: \e9f3;
$mi-var-equalizer: \e9f4;
$mi-var-error: \e9f5;
$mi-var-error-outline: \e9f6;
$mi-var-euro-symbol: \e9f7;
$mi-var-ev-station: \e9f8;
$mi-var-insert-invitation: \e9f9;
$mi-var-event-available: \e9fa;
$mi-var-event-busy: \e9fb;
$mi-var-event-note: \e9fc;
$mi-var-event-seat: \e9fd;
$mi-var-exit-to-app: \e9fe;
$mi-var-expand-less: \e9ff;
$mi-var-expand-more: \ea00;
$mi-var-explicit: \ea01;
$mi-var-explore: \ea02;
$mi-var-exposure: \ea03;
$mi-var-exposure-neg-1: \ea04;
$mi-var-exposure-neg-2: \ea05;
$mi-var-exposure-plus-1: \ea06;
$mi-var-exposure-plus-2: \ea07;
$mi-var-exposure-zero: \ea08;
$mi-var-extension: \ea09;
$mi-var-face: \ea0a;
$mi-var-fast-forward: \ea0b;
$mi-var-fast-rewind: \ea0c;
$mi-var-favorite: \ea0d;
$mi-var-favorite-border: \ea0e;
$mi-var-featured-play-list: \ea0f;
$mi-var-featured-video: \ea10;
$mi-var-sms-failed: \ea11;
$mi-var-fiber-dvr: \ea12;
$mi-var-fiber-manual-record: \ea13;
$mi-var-fiber-new: \ea14;
$mi-var-fiber-pin: \ea15;
$mi-var-fiber-smart-record: \ea16;
$mi-var-get-app: \ea17;
$mi-var-file-upload: \ea18;
$mi-var-filter: \ea19;
$mi-var-filter-1: \ea1a;
$mi-var-filter-2: \ea1b;
$mi-var-filter-3: \ea1c;
$mi-var-filter-4: \ea1d;
$mi-var-filter-5: \ea1e;
$mi-var-filter-6: \ea1f;
$mi-var-filter-7: \ea20;
$mi-var-filter-8: \ea21;
$mi-var-filter-9: \ea22;
$mi-var-filter-9-plus: \ea23;
$mi-var-filter-b-and-w: \ea24;
$mi-var-filter-center-focus: \ea25;
$mi-var-filter-drama: \ea26;
$mi-var-filter-frames: \ea27;
$mi-var-terrain: \ea28;
$mi-var-filter-list: \ea29;
$mi-var-filter-none: \ea2a;
$mi-var-filter-tilt-shift: \ea2b;
$mi-var-filter-vintage: \ea2c;
$mi-var-find-in-page: \ea2d;
$mi-var-find-replace: \ea2e;
$mi-var-fingerprint: \ea2f;
$mi-var-first-page: \ea30;
$mi-var-fitness-center: \ea31;
$mi-var-flare: \ea32;
$mi-var-flash-auto: \ea33;
$mi-var-flash-off: \ea34;
$mi-var-flash-on: \ea35;
$mi-var-flight-land: \ea36;
$mi-var-flight-takeoff: \ea37;
$mi-var-flip: \ea38;
$mi-var-flip-to-back: \ea39;
$mi-var-flip-to-front: \ea3a;
$mi-var-folder: \ea3b;
$mi-var-folder-open: \ea3c;
$mi-var-folder-shared: \ea3d;
$mi-var-folder-special: \ea3e;
$mi-var-font-download: \ea3f;
$mi-var-format-align-center: \ea40;
$mi-var-format-align-justify: \ea41;
$mi-var-format-align-left: \ea42;
$mi-var-format-align-right: \ea43;
$mi-var-format-bold: \ea44;
$mi-var-format-clear: \ea45;
$mi-var-format-color-fill: \ea46;
$mi-var-format-color-reset: \ea47;
$mi-var-format-color-text: \ea48;
$mi-var-format-indent-decrease: \ea49;
$mi-var-format-indent-increase: \ea4a;
$mi-var-format-italic: \ea4b;
$mi-var-format-line-spacing: \ea4c;
$mi-var-format-list-bulleted: \ea4d;
$mi-var-format-list-numbered: \ea4e;
$mi-var-format-paint: \ea4f;
$mi-var-format-quote: \ea50;
$mi-var-format-shapes: \ea51;
$mi-var-format-size: \ea52;
$mi-var-format-strikethrough: \ea53;
$mi-var-format-textdirection-l-to-r: \ea54;
$mi-var-format-textdirection-r-to-l: \ea55;
$mi-var-format-underlined: \ea56;
$mi-var-question-answer: \ea57;
$mi-var-forward: \ea58;
$mi-var-forward-10: \ea59;
$mi-var-forward-30: \ea5a;
$mi-var-forward-5: \ea5b;
$mi-var-free-breakfast: \ea5c;
$mi-var-fullscreen: \ea5d;
$mi-var-fullscreen-exit: \ea5e;
$mi-var-functions: \ea5f;
$mi-var-g-translate: \ea60;
$mi-var-games: \ea61;
$mi-var-gavel: \ea62;
$mi-var-gesture: \ea63;
$mi-var-gif: \ea64;
$mi-var-goat: \ea65;
$mi-var-golf-course: \ea66;
$mi-var-my-location: \ea67;
$mi-var-location-searching: \ea68;
$mi-var-location-disabled: \ea69;
$mi-var-star: \ea6a;
$mi-var-gradient: \ea6b;
$mi-var-grain: \ea6c;
$mi-var-graphic-eq: \ea6d;
$mi-var-grid-off: \ea6e;
$mi-var-grid-on: \ea6f;
$mi-var-people: \ea70;
$mi-var-group-add: \ea71;
$mi-var-group-work: \ea72;
$mi-var-hd: \ea73;
$mi-var-hdr-off: \ea74;
$mi-var-hdr-on: \ea75;
$mi-var-hdr-strong: \ea76;
$mi-var-hdr-weak: \ea77;
$mi-var-headset: \ea78;
$mi-var-headset-mic: \ea79;
$mi-var-healing: \ea7a;
$mi-var-hearing: \ea7b;
$mi-var-help: \ea7c;
$mi-var-help-outline: \ea7d;
$mi-var-high-quality: \ea7e;
$mi-var-highlight: \ea7f;
$mi-var-highlight-off: \ea80;
$mi-var-restore: \ea81;
$mi-var-home: \ea82;
$mi-var-hot-tub: \ea83;
$mi-var-local-hotel: \ea84;
$mi-var-hourglass-empty: \ea85;
$mi-var-hourglass-full: \ea86;
$mi-var-http: \ea87;
$mi-var-lock: \ea88;
$mi-var-photo: \ea89;
$mi-var-image-aspect-ratio: \ea8a;
$mi-var-import-contacts: \ea8b;
$mi-var-import-export: \ea8c;
$mi-var-important-devices: \ea8d;
$mi-var-inbox: \ea8e;
$mi-var-indeterminate-check-box: \ea8f;
$mi-var-info: \ea90;
$mi-var-info-outline: \ea91;
$mi-var-input: \ea92;
$mi-var-insert-comment: \ea93;
$mi-var-insert-drive-file: \ea94;
$mi-var-tag-faces: \ea95;
$mi-var-link: \ea96;
$mi-var-invert-colors: \ea97;
$mi-var-invert-colors-off: \ea98;
$mi-var-iso: \ea99;
$mi-var-keyboard: \ea9a;
$mi-var-keyboard-arrow-down: \ea9b;
$mi-var-keyboard-arrow-left: \ea9c;
$mi-var-keyboard-arrow-right: \ea9d;
$mi-var-keyboard-arrow-up: \ea9e;
$mi-var-keyboard-backspace: \ea9f;
$mi-var-keyboard-capslock: \eaa0;
$mi-var-keyboard-hide: \eaa1;
$mi-var-keyboard-return: \eaa2;
$mi-var-keyboard-tab: \eaa3;
$mi-var-keyboard-voice: \eaa4;
$mi-var-kitchen: \eaa5;
$mi-var-label: \eaa6;
$mi-var-label-outline: \eaa7;
$mi-var-language: \eaa8;
$mi-var-laptop-chromebook: \eaa9;
$mi-var-laptop-mac: \eaaa;
$mi-var-laptop-windows: \eaab;
$mi-var-last-page: \eaac;
$mi-var-open-in-new: \eaad;
$mi-var-layers: \eaae;
$mi-var-layers-clear: \eaaf;
$mi-var-leak-add: \eab0;
$mi-var-leak-remove: \eab1;
$mi-var-lens: \eab2;
$mi-var-library-books: \eab3;
$mi-var-library-music: \eab4;
$mi-var-lightbulb-outline: \eab5;
$mi-var-line-style: \eab6;
$mi-var-line-weight: \eab7;
$mi-var-linear-scale: \eab8;
$mi-var-linked-camera: \eab9;
$mi-var-list: \eaba;
$mi-var-live-help: \eabb;
$mi-var-live-tv: \eabc;
$mi-var-local-play: \eabd;
$mi-var-local-airport: \eabe;
$mi-var-local-atm: \eabf;
$mi-var-local-bar: \eac0;
$mi-var-local-cafe: \eac1;
$mi-var-local-car-wash: \eac2;
$mi-var-local-convenience-store: \eac3;
$mi-var-restaurant-menu: \eac4;
$mi-var-local-drink: \eac5;
$mi-var-local-florist: \eac6;
$mi-var-local-gas-station: \eac7;
$mi-var-shopping-cart: \eac8;
$mi-var-local-hospital: \eac9;
$mi-var-local-laundry-service: \eaca;
$mi-var-local-library: \eacb;
$mi-var-local-mall: \eacc;
$mi-var-theaters: \eacd;
$mi-var-local-offer: \eace;
$mi-var-local-parking: \eacf;
$mi-var-local-pharmacy: \ead0;
$mi-var-local-pizza: \ead1;
$mi-var-print: \ead2;
$mi-var-local-shipping: \ead3;
$mi-var-local-taxi: \ead4;
$mi-var-location-city: \ead5;
$mi-var-location-off: \ead6;
$mi-var-room: \ead7;
$mi-var-lock-open: \ead8;
$mi-var-lock-outline: \ead9;
$mi-var-looks: \eada;
$mi-var-looks-3: \eadb;
$mi-var-looks-4: \eadc;
$mi-var-looks-5: \eadd;
$mi-var-looks-6: \eade;
$mi-var-looks-one: \eadf;
$mi-var-looks-two: \eae0;
$mi-var-sync: \eae1;
$mi-var-loupe: \eae2;
$mi-var-low-priority: \eae3;
$mi-var-loyalty: \eae4;
$mi-var-mail-outline: \eae5;
$mi-var-map: \eae6;
$mi-var-markunread-mailbox: \eae7;
$mi-var-memory: \eae8;
$mi-var-menu: \eae9;
$mi-var-message: \eaea;
$mi-var-mic: \eaeb;
$mi-var-mic-none: \eaec;
$mi-var-mic-off: \eaed;
$mi-var-mms: \eaee;
$mi-var-mode-comment: \eaef;
$mi-var-monetization-on: \eaf0;
$mi-var-money-off: \eaf1;
$mi-var-monochrome-photos: \eaf2;
$mi-var-mood-bad: \eaf3;
$mi-var-more: \eaf4;
$mi-var-more-horiz: \eaf5;
$mi-var-more-vert: \eaf6;
$mi-var-motorcycle: \eaf7;
$mi-var-mouse: \eaf8;
$mi-var-move-to-inbox: \eaf9;
$mi-var-movie-creation: \eafa;
$mi-var-movie-filter: \eafb;
$mi-var-multiline-chart: \eafc;
$mi-var-music-note: \eafd;
$mi-var-music-video: \eafe;
$mi-var-nature: \eaff;
$mi-var-nature-people: \eb00;
$mi-var-navigation: \eb01;
$mi-var-near-me: \eb02;
$mi-var-network-cell: \eb03;
$mi-var-network-check: \eb04;
$mi-var-network-locked: \eb05;
$mi-var-network-wifi: \eb06;
$mi-var-new-releases: \eb07;
$mi-var-next-week: \eb08;
$mi-var-nfc: \eb09;
$mi-var-no-encryption: \eb0a;
$mi-var-signal-cellular-no-sim: \eb0b;
$mi-var-note: \eb0c;
$mi-var-note-add: \eb0d;
$mi-var-notifications: \eb0e;
$mi-var-notifications-active: \eb0f;
$mi-var-notifications-none: \eb10;
$mi-var-notifications-off: \eb11;
$mi-var-notifications-paused: \eb12;
$mi-var-offline-pin: \eb13;
$mi-var-ondemand-video: \eb14;
$mi-var-opacity: \eb15;
$mi-var-open-in-browser: \eb16;
$mi-var-open-with: \eb17;
$mi-var-pages: \eb18;
$mi-var-pageview: \eb19;
$mi-var-pan-tool: \eb1a;
$mi-var-panorama: \eb1b;
$mi-var-radio-button-unchecked: \eb1c;
$mi-var-panorama-horizontal: \eb1d;
$mi-var-panorama-vertical: \eb1e;
$mi-var-panorama-wide-angle: \eb1f;
$mi-var-party-mode: \eb20;
$mi-var-pause: \eb21;
$mi-var-pause-circle-filled: \eb22;
$mi-var-pause-circle-outline: \eb23;
$mi-var-people-outline: \eb24;
$mi-var-perm-camera-mic: \eb25;
$mi-var-perm-contact-calendar: \eb26;
$mi-var-perm-data-setting: \eb27;
$mi-var-perm-device-information: \eb28;
$mi-var-person-outline: \eb29;
$mi-var-perm-media: \eb2a;
$mi-var-perm-phone-msg: \eb2b;
$mi-var-perm-scan-wifi: \eb2c;
$mi-var-person: \eb2d;
$mi-var-person-add: \eb2e;
$mi-var-person-pin: \eb2f;
$mi-var-person-pin-circle: \eb30;
$mi-var-personal-video: \eb31;
$mi-var-pets: \eb32;
$mi-var-phone-android: \eb33;
$mi-var-phone-bluetooth-speaker: \eb34;
$mi-var-phone-forwarded: \eb35;
$mi-var-phone-in-talk: \eb36;
$mi-var-phone-iphone: \eb37;
$mi-var-phone-locked: \eb38;
$mi-var-phone-missed: \eb39;
$mi-var-phone-paused: \eb3a;
$mi-var-phonelink-erase: \eb3b;
$mi-var-phonelink-lock: \eb3c;
$mi-var-phonelink-off: \eb3d;
$mi-var-phonelink-ring: \eb3e;
$mi-var-phonelink-setup: \eb3f;
$mi-var-photo-album: \eb40;
$mi-var-photo-filter: \eb41;
$mi-var-photo-size-select-actual: \eb42;
$mi-var-photo-size-select-large: \eb43;
$mi-var-photo-size-select-small: \eb44;
$mi-var-picture-as-pdf: \eb45;
$mi-var-picture-in-picture: \eb46;
$mi-var-picture-in-picture-alt: \eb47;
$mi-var-pie-chart: \eb48;
$mi-var-pie-chart-outlined: \eb49;
$mi-var-pin-drop: \eb4a;
$mi-var-play-arrow: \eb4b;
$mi-var-play-circle-filled: \eb4c;
$mi-var-play-circle-outline: \eb4d;
$mi-var-play-for-work: \eb4e;
$mi-var-playlist-add: \eb4f;
$mi-var-playlist-add-check: \eb50;
$mi-var-playlist-play: \eb51;
$mi-var-plus-one: \eb52;
$mi-var-polymer: \eb53;
$mi-var-pool: \eb54;
$mi-var-portable-wifi-off: \eb55;
$mi-var-portrait: \eb56;
$mi-var-power: \eb57;
$mi-var-power-input: \eb58;
$mi-var-power-settings-new: \eb59;
$mi-var-pregnant-woman: \eb5a;
$mi-var-present-to-all: \eb5b;
$mi-var-priority-high: \eb5c;
$mi-var-public: \eb5d;
$mi-var-publish: \eb5e;
$mi-var-queue-music: \eb5f;
$mi-var-queue-play-next: \eb60;
$mi-var-radio: \eb61;
$mi-var-radio-button-checked: \eb62;
$mi-var-rate-review: \eb63;
$mi-var-receipt: \eb64;
$mi-var-recent-actors: \eb65;
$mi-var-record-voice-over: \eb66;
$mi-var-redo: \eb67;
$mi-var-refresh: \eb68;
$mi-var-remove: \eb69;
$mi-var-remove-circle-outline: \eb6a;
$mi-var-remove-from-queue: \eb6b;
$mi-var-visibility: \eb6c;
$mi-var-remove-shopping-cart: \eb6d;
$mi-var-reorder: \eb6e;
$mi-var-repeat: \eb6f;
$mi-var-repeat-one: \eb70;
$mi-var-replay: \eb71;
$mi-var-replay-10: \eb72;
$mi-var-replay-30: \eb73;
$mi-var-replay-5: \eb74;
$mi-var-reply: \eb75;
$mi-var-reply-all: \eb76;
$mi-var-report: \eb77;
$mi-var-warning: \eb78;
$mi-var-restaurant: \eb79;
$mi-var-restore-page: \eb7a;
$mi-var-ring-volume: \eb7b;
$mi-var-room-service: \eb7c;
$mi-var-rotate-90-degrees-ccw: \eb7d;
$mi-var-rotate-left: \eb7e;
$mi-var-rotate-right: \eb7f;
$mi-var-rounded-corner: \eb80;
$mi-var-router: \eb81;
$mi-var-rowing: \eb82;
$mi-var-rss-feed: \eb83;
$mi-var-rv-hookup: \eb84;
$mi-var-satellite: \eb85;
$mi-var-save: \eb86;
$mi-var-scanner: \eb87;
$mi-var-school: \eb88;
$mi-var-screen-lock-landscape: \eb89;
$mi-var-screen-lock-portrait: \eb8a;
$mi-var-screen-lock-rotation: \eb8b;
$mi-var-screen-rotation: \eb8c;
$mi-var-screen-share: \eb8d;
$mi-var-sd-storage: \eb8e;
$mi-var-search: \eb8f;
$mi-var-security: \eb90;
$mi-var-select-all: \eb91;
$mi-var-send: \eb92;
$mi-var-sentiment-dissatisfied: \eb93;
$mi-var-sentiment-neutral: \eb94;
$mi-var-sentiment-satisfied: \eb95;
$mi-var-sentiment-very-dissatisfied: \eb96;
$mi-var-sentiment-very-satisfied: \eb97;
$mi-var-settings: \eb98;
$mi-var-settings-applications: \eb99;
$mi-var-settings-backup-restore: \eb9a;
$mi-var-settings-bluetooth: \eb9b;
$mi-var-settings-brightness: \eb9c;
$mi-var-settings-cell: \eb9d;
$mi-var-settings-ethernet: \eb9e;
$mi-var-settings-input-antenna: \eb9f;
$mi-var-settings-input-composite: \eba0;
$mi-var-settings-input-hdmi: \eba1;
$mi-var-settings-input-svideo: \eba2;
$mi-var-settings-overscan: \eba3;
$mi-var-settings-phone: \eba4;
$mi-var-settings-power: \eba5;
$mi-var-settings-remote: \eba6;
$mi-var-settings-system-daydream: \eba7;
$mi-var-settings-voice: \eba8;
$mi-var-share: \eba9;
$mi-var-shop: \ebaa;
$mi-var-shop-two: \ebab;
$mi-var-shopping-basket: \ebac;
$mi-var-short-text: \ebad;
$mi-var-show-chart: \ebae;
$mi-var-shuffle: \ebaf;
$mi-var-signal-cellular-4-bar: \ebb0;
$mi-var-signal-cellular-connected-no-internet-4-bar: \ebb1;
$mi-var-signal-cellular-null: \ebb2;
$mi-var-signal-cellular-off: \ebb3;
$mi-var-signal-wifi-4-bar: \ebb4;
$mi-var-signal-wifi-4-bar-lock: \ebb5;
$mi-var-signal-wifi-off: \ebb6;
$mi-var-sim-card: \ebb7;
$mi-var-sim-card-alert: \ebb8;
$mi-var-skip-next: \ebb9;
$mi-var-skip-previous: \ebba;
$mi-var-slideshow: \ebbb;
$mi-var-slow-motion-video: \ebbc;
$mi-var-stay-primary-portrait: \ebbd;
$mi-var-smoke-free: \ebbe;
$mi-var-smoking-rooms: \ebbf;
$mi-var-textsms: \ebc0;
$mi-var-snooze: \ebc1;
$mi-var-sort: \ebc2;
$mi-var-sort-by-alpha: \ebc3;
$mi-var-spa: \ebc4;
$mi-var-space-bar: \ebc5;
$mi-var-speaker: \ebc6;
$mi-var-speaker-group: \ebc7;
$mi-var-speaker-notes: \ebc8;
$mi-var-speaker-notes-off: \ebc9;
$mi-var-speaker-phone: \ebca;
$mi-var-spellcheck: \ebcb;
$mi-var-star-border: \ebcc;
$mi-var-star-half: \ebcd;
$mi-var-stars: \ebce;
$mi-var-stay-primary-landscape: \ebcf;
$mi-var-stop: \ebd0;
$mi-var-stop-screen-share: \ebd1;
$mi-var-storage: \ebd2;
$mi-var-store-mall-directory: \ebd3;
$mi-var-straighten: \ebd4;
$mi-var-streetview: \ebd5;
$mi-var-strikethrough-s: \ebd6;
$mi-var-style: \ebd7;
$mi-var-subdirectory-arrow-left: \ebd8;
$mi-var-subdirectory-arrow-right: \ebd9;
$mi-var-subject: \ebda;
$mi-var-subscriptions: \ebdb;
$mi-var-subtitles: \ebdc;
$mi-var-subway: \ebdd;
$mi-var-supervisor-account: \ebde;
$mi-var-surround-sound: \ebdf;
$mi-var-swap-calls: \ebe0;
$mi-var-swap-horiz: \ebe1;
$mi-var-swap-vert: \ebe2;
$mi-var-swap-vertical-circle: \ebe3;
$mi-var-switch-camera: \ebe4;
$mi-var-switch-video: \ebe5;
$mi-var-sync-disabled: \ebe6;
$mi-var-sync-problem: \ebe7;
$mi-var-system-update: \ebe8;
$mi-var-system-update-alt: \ebe9;
$mi-var-tab: \ebea;
$mi-var-tab-unselected: \ebeb;
$mi-var-tablet: \ebec;
$mi-var-tablet-android: \ebed;
$mi-var-tablet-mac: \ebee;
$mi-var-tap-and-play: \ebef;
$mi-var-text-fields: \ebf0;
$mi-var-text-format: \ebf1;
$mi-var-texture: \ebf2;
$mi-var-thumb-down: \ebf3;
$mi-var-thumb-up: \ebf4;
$mi-var-thumbs-up-down: \ebf5;
$mi-var-timelapse: \ebf6;
$mi-var-timeline: \ebf7;
$mi-var-timer: \ebf8;
$mi-var-timer-10: \ebf9;
$mi-var-timer-3: \ebfa;
$mi-var-timer-off: \ebfb;
$mi-var-title: \ebfc;
$mi-var-toc: \ebfd;
$mi-var-today: \ebfe;
$mi-var-toll: \ebff;
$mi-var-tonality: \ec00;
$mi-var-touch-app: \ec01;
$mi-var-toys: \ec02;
$mi-var-track-changes: \ec03;
$mi-var-traffic: \ec04;
$mi-var-train: \ec05;
$mi-var-tram: \ec06;
$mi-var-transfer-within-a-station: \ec07;
$mi-var-transform: \ec08;
$mi-var-translate: \ec09;
$mi-var-trending-down: \ec0a;
$mi-var-trending-flat: \ec0b;
$mi-var-trending-up: \ec0c;
$mi-var-tune: \ec0d;
$mi-var-tv: \ec0e;
$mi-var-unarchive: \ec0f;
$mi-var-undo: \ec10;
$mi-var-unfold-less: \ec11;
$mi-var-unfold-more: \ec12;
$mi-var-update: \ec13;
$mi-var-usb: \ec14;
$mi-var-verified-user: \ec15;
$mi-var-vertical-align-bottom: \ec16;
$mi-var-vertical-align-center: \ec17;
$mi-var-vertical-align-top: \ec18;
$mi-var-vibration: \ec19;
$mi-var-video-call: \ec1a;
$mi-var-video-label: \ec1b;
$mi-var-video-library: \ec1c;
$mi-var-videocam: \ec1d;
$mi-var-videocam-off: \ec1e;
$mi-var-videogame-asset: \ec1f;
$mi-var-view-agenda: \ec20;
$mi-var-view-array: \ec21;
$mi-var-view-carousel: \ec22;
$mi-var-view-column: \ec23;
$mi-var-view-comfy: \ec24;
$mi-var-view-compact: \ec25;
$mi-var-view-day: \ec26;
$mi-var-view-headline: \ec27;
$mi-var-view-list: \ec28;
$mi-var-view-module: \ec29;
$mi-var-view-quilt: \ec2a;
$mi-var-view-stream: \ec2b;
$mi-var-view-week: \ec2c;
$mi-var-vignette: \ec2d;
$mi-var-visibility-off: \ec2e;
$mi-var-voice-chat: \ec2f;
$mi-var-voicemail: \ec30;
$mi-var-volume-down: \ec31;
$mi-var-volume-mute: \ec32;
$mi-var-volume-off: \ec33;
$mi-var-volume-up: \ec34;
$mi-var-vpn-key: \ec35;
$mi-var-vpn-lock: \ec36;
$mi-var-wallpaper: \ec37;
$mi-var-watch: \ec38;
$mi-var-watch-later: \ec39;
$mi-var-wb-auto: \ec3a;
$mi-var-wb-incandescent: \ec3b;
$mi-var-wb-iridescent: \ec3c;
$mi-var-wb-sunny: \ec3d;
$mi-var-wc: \ec3e;
$mi-var-web: \ec3f;
$mi-var-web-asset: \ec40;
$mi-var-weekend: \ec41;
$mi-var-whatshot: \ec42;
$mi-var-widgets: \ec43;
$mi-var-wifi: \ec44;
$mi-var-wifi-lock: \ec45;
$mi-var-wifi-tethering: \ec46;
$mi-var-work: \ec47;
$mi-var-wrap-text: \ec48;
$mi-var-youtube-searched-for: \ec49;
$mi-var-zoom-in: \ec4a;
$mi-var-zoom-out: \ec4b;
$mi-var-zoom-out-map: \ec4c;
