// Japanese (ja)
plupload.addI18n({"Stop Upload":"アップロード停止","Upload URL might be wrong or doesn't exist.":"アップロード先の URL が存在しません","tb":"TB","Size":"サイズ","Close":"閉じる","You must specify either browse_button or drop_element.":"ブラウザのボタンで または ファイルをドロップするか いずれかの方法で指定する必要があります。","Init error.":"イニシャライズエラー","Add files to the upload queue and click the start button.":"ファイルをアップロードキューに追加してスタートボタンをクリックしてください","List":"リスト","Filename":"ファイル名","%s specified, but cannot be found.":"指定された %s は見つかりません。","Image format either wrong or not supported.":"画像形式が間違っているかサポートされていません","Status":"ステータス","HTTP Error.":"HTTP エラー","Start Upload":"アップロード開始","Error: File too large:":"エラー: ファイルが大きすぎます:","kb":"KB","Duplicate file error.":"重複ファイルエラー","File size error.":"ファイルサイズエラー","N/A":"N/A","gb":"GB","Error: Invalid file extension:":"エラー: ファイルの拡張子が無効です：","Select files":"ファイル選択","%s already present in the queue.":"%s 既にキューに存在しています","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"解像度がしきい値を超えています! ランタイム <b>%s</b> は縦 %h px 横 %w px までをサポートします","File: %s":"ファイル: %s","b":"B","Uploaded %d/%d files":"アップロード中 %d/%d ファイル","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"アップロード可能なファイル数は %d です 余分なファイルは削除されました","%d files queued":"%d ファイルが追加されました","File: %s, size: %d, max file size: %d":"ファイル: %s, サイズ: %d, 最大ファイルサイズ: %d","Thumbnails":"サムネイル","Drag files here.":"ここにファイルをドラッグ","Runtime ran out of available memory.":"ランタイムが使用するメモリが不足しました","File count error.":"ファイル数エラー","File extension error.":"ファイル拡張子エラー","mb":"MB","Add Files":"ファイルを追加"});