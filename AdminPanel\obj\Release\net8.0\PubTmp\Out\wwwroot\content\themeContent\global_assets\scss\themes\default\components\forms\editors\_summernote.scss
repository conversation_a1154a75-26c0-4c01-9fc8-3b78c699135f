/* ------------------------------------------------------------------------------
*
*  # Summernote editor
*
*  Styles for summernote.min.js - super simple WYSIWYG Editor for Bootstrap framework
*
* ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-summernote {


    // Summernote uses its own icons
    // ----------------------------------------

    // Load font
    @font-face {
        font-family: 'summernote';
        font-style: normal;
        font-weight: normal;
        src: url("#{$summernote-font-path}/summernote.eot?0d0d5fac99cc8774d89eb08b1a8323c4");
        src: url("#{$summernote-font-path}/summernote.eot?#iefix") format("embedded-opentype"),
             url("#{$summernote-font-path}/summernote.woff?0d0d5fac99cc8774d89eb08b1a8323c4") format("woff"),
             url("#{$summernote-font-path}/summernote.ttf?0d0d5fac99cc8774d89eb08b1a8323c4") format("truetype");
    }

    // Icon styles
    [class^="note-icon-"]:before,
    [class*=" note-icon-"]:before {
        display: inline-block;
        font-family: 'summernote';
        font-style: normal;
        font-size: inherit;
        text-decoration: inherit;
        vertical-align: middle;
        line-height: normal;
        @include ll-font-smoothing();
    }

    // Icons
    .note-icon-align-center:before{content:"\f101";}
    .note-icon-align-indent:before{content:"\f102";}
    .note-icon-align-justify:before{content:"\f103";}
    .note-icon-align-left:before{content:"\f104";}
    .note-icon-align-outdent:before{content:"\f105";}
    .note-icon-align-right:before{content:"\f106";}
    .note-icon-align:before{content:"\f107";}
    .note-icon-arrow-circle-down:before{content:"\f108";}
    .note-icon-arrow-circle-left:before{content:"\f109";}
    .note-icon-arrow-circle-right:before{content:"\f10a";}
    .note-icon-arrow-circle-up:before{content:"\f10b";}
    .note-icon-arrows-alt:before{content:"\f10c";}
    .note-icon-arrows-h:before{content:"\f10d";}
    .note-icon-arrows-v:before{content:"\f10e";}
    .note-icon-bold:before{content:"\f10f";}
    .note-icon-caret:before{content:"\f110";}
    .note-icon-chain-broken:before{content:"\f111";}
    .note-icon-circle:before{content:"\f112";}
    .note-icon-close:before{content:"\f113";}
    .note-icon-code:before{content:"\f114";}
    .note-icon-col-after:before{content:"\f115";}
    .note-icon-col-before:before{content:"\f116";}
    .note-icon-col-remove:before{content:"\f117";}
    .note-icon-eraser:before{content:"\f118";}
    .note-icon-font:before{content:"\f119";}
    .note-icon-frame:before{content:"\f11a";}
    .note-icon-italic:before{content:"\f11b";}
    .note-icon-link:before{content:"\f11c";}
    .note-icon-magic:before{content:"\f11d";}
    .note-icon-menu-check:before{content:"\f11e";}
    .note-icon-minus:before{content:"\f11f";}
    .note-icon-orderedlist:before{content:"\f120";}
    .note-icon-pencil:before{content:"\f121";}
    .note-icon-picture:before{content:"\f122";}
    .note-icon-question:before{content:"\f123";}
    .note-icon-redo:before{content:"\f124";}
    .note-icon-row-above:before{content:"\f125";}
    .note-icon-row-below:before{content:"\f126";}
    .note-icon-row-remove:before{content:"\f127";}
    .note-icon-special-character:before{content:"\f128";}
    .note-icon-square:before{content:"\f129";}
    .note-icon-strikethrough:before{content:"\f12a";}
    .note-icon-subscript:before{content:"\f12b";}
    .note-icon-summernote:before{content:"\f12c";}
    .note-icon-superscript:before{content:"\f12d";}
    .note-icon-table:before{content:"\f12e";}
    .note-icon-text-height:before{content:"\f12f";}
    .note-icon-trash:before{content:"\f130";}
    .note-icon-underline:before{content:"\f131";}
    .note-icon-undo:before{content:"\f132";}
    .note-icon-unorderedlist:before{content:"\f133";}
    .note-icon-video:before{content:"\f134";}


    // Core
    // ------------------------------

    // Editor
    .note-editor {
        position: relative;

        // Drop container
        .note-dropzone {
            position: absolute;
            z-index: 100;
            display: none;
            color: theme-color("primary");
            background-color: $card-bg;
            pointer-events: none;
            border: ($summernote-border-width * 2) dashed theme-color("primary");
            opacity: .95;

            // Hover state
            &.hover {
                color: theme-color("primary");
                border: ($summernote-border-width * 2) dashed theme-color("primary");
            }

            // Messages
            .note-dropzone-message {
                display: table-cell;
                font-size: $h5-font-size;
                font-weight: $font-weight-semibold;
                text-align: center;
                vertical-align: middle;
            }
        }
        .card & {
            border-color: $summernote-border-color;
            @include box-shadow(none);
        }
        &.dragover .note-dropzone {
            display: table;
        }

        // Remove bottom margin from last rows
        .note-form-group {
            &:last-child {
                margin-bottom: 0;
            }
        }
        .note-group-image-url {
            &:last-child {
                margin-bottom: 0;
            }
        }

        // Modal title
        .modal-title {
            font-size: $h5-font-size;
        }

        // Style dropdown menu
        .dropdown-style {
            pre {
                background-color: $dropdown-bg;
                padding: $dropdown-item-padding-y $dropdown-item-padding-x;
            }
        }

        // Add .summernote-borderless class to original container
        // to remove all styles related to .card
        .summernote-borderless ~ & {
            border: 0;
            margin: 0;
            @include border-radius(0);
        }
    }

    // Air mode
    .note-air-editor {
        outline: 0;
    }

    // Some fix for FF
    @-moz-document url-prefix() {
        .note-image-input {
            height: auto;
        }
    }

    // Placeholder text
    .note-placeholder {
        position: absolute;
        display: none;
        color: $gray-600;
    }

    // Toolbar
    .note-toolbar:not([class*=bg-]):not([class*=alpha-]) {
        background-color: $card-cap-bg;
        border-bottom: $card-border-width solid $card-border-color;
        padding: $card-spacer-y $card-spacer-x;
        padding-top: 0;
        margin: 0;
    }

    // Menus
    .dropdown-fontname,
    .dropdown-style {
        max-height: 20rem;
        overflow: auto;
    }

    // Remove margin from paragraphs in menus
    .dropdown-style {
        a > p {
            margin-bottom: 0;
        }
    }


    //
    // Editor
    //

    // Editing area
    .note-editing-area {
        position: relative;

        // Editable container
        .note-editable {
            outline: 0;

            sup {
                vertical-align: super;
            }
            sub {
                vertical-align: sub;
            }
        }

        // Images
        img {
            &.note-float-left {
                margin-right: $element-spacer-x;
            }
            &.note-float-right {
                margin-left: $element-spacer-x;
            }
        }
    }

    // Frame
    .note-frame {

        // Code view
        &.codeview {
            .note-editing-area {
                .note-editable {
                    display: none;
                }
                .note-codable {
                    display: block;
                }
            }
        }

        // Editing area
        .note-editing-area {
            overflow: hidden;

            // Edit view
            .note-editable {
                padding: $card-spacer-x;
                overflow: auto;
                color: $body-color;
                background-color: $card-bg;
                min-height: $summernote-min-height;

                &[contenteditable="false"] {
                    background-color: $gray-300;
                }
            }

            // Code view
            .note-codable {
                display: none;
                width: 100%;
                padding: $card-spacer-x;
                margin-bottom: 0;
                font-family: $font-family-monospace;
                color: $white;
                background-color: $gray-900;
                border: 0;
                resize: none;
                @include box-shadow(none);
                @include border-radius(0);
            }
        }

        // Fullscreen mode
        &.fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            z-index: $zindex-tooltip + 10;
            width: 100%!important;
            border: 0;
            @include border-radius(0);

            // Hide resize bar
            .note-resizebar {
                display: none;
            }
        }

        // Placeholder
        .note-placeholder {
            position: absolute;
            display: none;
            color: $gray-600;
        }
    }

    // Status bar
    .note-statusbar {
        background-color: $card-cap-bg;

        // Resize handle
        .note-resizebar {
            width: 100%;
            padding: ($summernote-resizer-height * 2) 0;
            cursor: ns-resize;
            border-top: $summernote-border-width solid $summernote-border-color;

            // Icon bar
            .note-icon-bar {
                width: $summernote-resizer-width;
                margin: $summernote-resizer-height auto;
                border-top: $summernote-resizer-height solid $summernote-resizer-color;
            }
        }
    }


    //
    // Popover
    //

    // Base
    .note-popover {

        // Override default popover styles
        &.popover {
            display: none;
            max-width: none;

            // Links
            .popover-content a {
                display: inline-block;
                max-width: $popover-max-width;
                vertical-align: middle;
                @include text-truncate();
            }

            // Arrow
            .arrow {
                left: $card-spacer-x !important;
            }
        }

        // Content
        .popover-content {
            display: inline-flex;
            padding: $popover-body-padding-y 0 0 $popover-body-padding-y;
            align-items: center;
            flex-wrap: wrap;

            > span {
                margin-right: $popover-body-padding-x;
                margin-bottom: $popover-body-padding-x;
            }

            > .btn-group {
                margin-right: $popover-body-padding-x;
                margin-bottom: $popover-body-padding-x;
            }
        }
    }

    // Remove margins from headings
    .note-style {
        h1, h2, h3, h4, h5, h6, blockquote {
            margin: 0;
        }
    }

    // Hint
    .note-hint-popover {
        min-width: $dropdown-min-width;
        padding: $dropdown-padding-y 0;

        .popover-content {
            max-height: 10rem;
            overflow: auto;

            .note-hint-group {
                .note-hint-item {
                    display: block!important;
                    padding: $dropdown-item-padding-y $dropdown-item-padding-x;
                    color: $dropdown-link-color;
                    clear: both;

                    // Hover state
                    @include hover-focus {
                        color: $dropdown-link-hover-color;
                        background-color: $dropdown-link-hover-bg;
                        text-decoration: none;
                    }

                    // Active state
                    &.active {
                        color: $dropdown-link-active-color;
                        text-decoration: none;
                        background-color: $dropdown-link-active-bg;
                    }
                }
            }
        }
    }


    // Toolbar
    .card-header.note-toolbar {

        // Closest button group
        > .btn-group {
            margin-top: $card-spacer-y;
            margin-right: $card-spacer-y;

            &:last-child {
                margin-right: 0;
            }
        }

        // Dropdown menu
        .dropdown-menu {

            // Links
            &.note-check a {
                i {
                    display: none;
                }

                // Active state
                &.checked {
                    color: $dropdown-link-active-color;
                    background-color: $dropdown-link-active-bg;
                }
            }
        }

        // Color palette
        .note-color-palette {
            line-height: 1;

            div {
                .note-color-btn {
                    padding: 0;
                    margin: 0;
                    border: 1px solid $dropdown-bg;
                    @include size(1.25rem);

                    @include hover {
                        border-color: $gray-900;
                    }
                }
            }
        }
    }

    // All button groups
    .note-popover,
    .note-toolbar {
        .btn-group {
            .note-table {
                min-width: 0;
                padding: $dropdown-padding-y;

                // Table cell picker
                .note-dimension-picker {
                    font-size: 1.125rem;

                    .note-dimension-picker-mousecatcher {
                        position: absolute!important;
                        z-index: 3;
                        width: 10em;
                        height: 10em;
                        cursor: pointer
                    }

                    .note-dimension-picker-unhighlighted {
                        position: relative!important;
                        z-index: 1;
                        width: 5em;
                        height: 5em;
                        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC') repeat;
                    }

                    .note-dimension-picker-highlighted {
                        position: absolute!important;
                        z-index: 2;
                        width: 1em;
                        height: 1em;
                        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC') repeat;
                    }
                }

                // Selected cells callback label
                .note-dimension-display {
                    text-align: center;
                    margin-top: $dropdown-padding-y;
                }
            }
        }
    }

    // Colors
    .note-color {

        // Row
        .note-color-row {
            font-size: 0;
        }

        // Dropdown menu
        .dropdown-menu {

            // Make sure 2 palettes fit in 1 row
            &.show {
                display: flex;
            }

            // Color palette
            .note-palette {
                display: inline-block;
                width: (1.25rem * 8);
                margin: $btn-padding-y $btn-padding-x;

                // Title
                .note-palette-title {
                    margin-bottom: ($spacer / 2);
                    font-weight: $font-weight-semibold;
                }

                // Reset
                .note-color-reset {
                    width: 100%;
                    padding: $btn-padding-y-sm $btn-padding-x-sm;
                    margin-bottom: $btn-padding-x;
                }
            }
        }
    }

    // Paragraphs
    .note-para {

        // Dropdown menu
        .dropdown-menu {
            padding: $dropdown-item-padding-y;
            white-space: nowrap;

            > div:first-child {
                margin-right: $element-spacer-x;
            }
        }
    }

    // Image resize handle
    .note-handle {
        position: relative;

        // Selection
        .note-control-selection {
            position: absolute;
            display: none;
            border: $border-width solid $gray-900;
            z-index: 10;

            > div {
                position: absolute;
            }

            // Background
            .note-control-selection-bg {
                background-color: $gray-900;
                opacity: 0.3;
                @include size(100%);
            }

            // Control handle
            .note-control-handle,
            .note-control-holder,
            .note-control-sizing {
                border: $border-width solid $gray-900;
                @include size(0.5rem);
            }
            .note-control-sizing {
                background-color: $white;
            }

            // Control direction
            .note-control-nw {
                top: rem-calc(-0.3125rem);
                left: rem-calc(-0.3125rem);
                border-right: 0;
                border-bottom: 0;
            }
            .note-control-ne {
                top: rem-calc(-0.3125rem);
                right: rem-calc(-0.3125rem);
                border-bottom: 0;
                border-left: 0;
            }
            .note-control-sw {
                bottom: rem-calc(-0.3125rem);
                left: rem-calc(-0.3125rem);
                border-top: 0;
                border-right: 0;
            }
            .note-control-se {
                right: rem-calc(-0.3125rem);
                bottom: rem-calc(-0.3125rem);
                cursor: se-resize;
            }

            // Selection info
            .note-control-selection-info {
                right: 0;
                bottom: 0;
                padding: 0.3125rem;
                margin: 0.3125rem;
                color: $white;
                background-color: $gray-900;
                opacity: 0.7;
                @include border-radius($border-radius);
            }
        }
    }
}
