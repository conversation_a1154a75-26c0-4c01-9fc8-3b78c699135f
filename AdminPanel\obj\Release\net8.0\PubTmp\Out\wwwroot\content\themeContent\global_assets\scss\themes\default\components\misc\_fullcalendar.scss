/* ------------------------------------------------------------------------------
 *
 *  # FullCalendar
 *
 *  Styles for fullcalendar JS files - JavaScript event calendar
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-fullcalendar {


	// Layout
	// ------------------------------

	// Container
	.fc {
		direction: ltr;
		text-align: left;
	}

	// Events container
	.fc-events-container {
		background-color: $gray-100;
		border: $border-width solid $border-color;
		padding: map-get($spacers, 2);
		@include border-radius($border-radius);
	}

	// Event
	.fc-event {
		padding: $fullcalendar-event-padding-y $fullcalendar-event-padding-x;

		+ .fc-event {
			margin-top: ($spacer / 2);
		}
	}


	// Colors
	// ------------------------------

	// Default theme
	.fc-unthemed {
		th,
		td,
		thead,
		tbody,
		.fc-divider,
		.fc-row,
		.fc-content,
		.fc-popover,
		.fc-list-view,
		.fc-list-heading td {
			border-color: $table-border-color;
		}

		// Change bottom border color
		thead,
		thead td,
		thead th {
			border-bottom-color: $table-head-border-color;
		}

		// Popover
		.fc-popover {
			background-color: $popover-bg;
			@include border-radius($border-radius);
		}

		// Highlight today's date
		td.fc-today {
			background-color: $fullcalendar-today-bg;
		}
	}

	// When user is selecting cells
	.fc-highlight { 
		background-color: $fullcalendar-highlight-bg;
	}

	// Default look for background events
	.fc-bgevent {
		background-color: $fullcalendar-bgevent-bg;
		opacity: 0.3;
	}

	// Default look for non-business-hours areas
	// Will inherit .fc-bgevent's styles
	.fc-nonbusiness {
		background-color: $fullcalendar-nonbusiness-bg;
	}


	// Icons
	// ------------------------------

	// Icon base
	.fc-icon {
		display: block;
		user-select: none;
		@include transition(all ease-in-out $component-transition-timer);

		// Icon base
		&:after {
			display: inline-block;
			font-family: $icon-font-family;
			line-height: 1;
			font-size: $icon-font-size;
			vertical-align: middle;
			position: relative;
			top: -1px;
			@include ll-font-smoothing();
		}
	}

	// Arrow icons
	.fc-icon-chevron-left:after {
	    @if $direction == 'LTR' {
	        content: $icon-nav-calendar-prev;
	    }
	    @else {
	        content: $icon-nav-calendar-next;
	    }
	    .fc-rtl & {
	    	content: $icon-nav-calendar-prev;
	    }
	}
	.fc-icon-chevron-right:after {
	    @if $direction == 'LTR' {
	        content: $icon-nav-calendar-next;
	    }
	    @else {
	        content: $icon-nav-calendar-prev;
	    }
	    .fc-rtl & {
	    	content: $icon-nav-calendar-next;
	    }
	}
	.fc-icon-chevrons-left:after {
	    @if $direction == 'LTR' {
	        content: '\000AB';
	    }
	    @else {
	        content: '\000BB';
	    }
	    .fc-rtl & {
	    	content: '\000AB';
	    }
	}
	.fc-icon-chevrons-right:after {
	    @if $direction == 'LTR' {
	        content: '\000BB';
	    }
	    @else {
	        content: '\000AB';
	    }
	    .fc-rtl & {
	    	content: '\000BB';
	    }
	}

	// Close popover icon
	.fc-icon-x:after {
		content: $icon-action-cross;
		font-size: $font-size-base;
		display: block;
		margin-top: ($line-height-computed - $font-size-base) / 2;
		top: 0;
	}


	// Buttons
	// ------------------------------

	// Base, identical to .btn-light
	.fc-button {
		margin: 0;
		padding: $btn-padding-y $btn-padding-x;
		white-space: nowrap;
		cursor: pointer;
	    color: $btn-light-color;
	    background-color: $btn-light-bg;
	    border: $btn-border-width solid $btn-light-border-color;
	    outline: 0;
	    @include border-radius($btn-border-radius);
		@include transition(all ease-in-out $component-transition-timer);

		// Hover state
	    &:not(.fc-button-disabled):not(.fc-button-active):hover,
	    &:not(.fc-button-disabled):not(.fc-button-active):focus {
		    color: $btn-light-hover-color;
		    background-color: $btn-light-hover-bg;
		    border-color: $btn-light-hover-border-color;
	    }

	    // Remove outline
		&:focus {
			outline: 0;
		}

		// Firefox has an annoying inner border
		&::-moz-focus-inner {
			margin: 0;
			padding: 0;
		}
	}

	// Disabled state
	.fc-button-disabled {
		cursor: $cursor-disabled;
		opacity: $btn-disabled-opacity;
	}

	// Active state
	.fc-button-active {
		background-image: none;
	    color: $btn-light-active-color;
	    background-color: $btn-light-active-bg;
	    border-color: $btn-light-active-border-color;
	}


	//
	// Button groups
	//

	.fc-button-group {
		display: inline-block;

		// Border radius
		.fc-button {
			@include border-radius(0);

			&:first-child {
				@include border-left-radius($btn-border-radius);
			}
			&:last-child {
				@include border-right-radius($btn-border-radius);
			}
		}

		.fc & {
			> * {
				float: left;
				margin: 0;
				margin-left: -($btn-border-width);
			}

			> :first-child {
				margin-left: 0;
			}
		}
	}


	// Popover
	// ------------------------------

	.fc-popover {
		position: absolute;
		border: $popover-border-width solid $popover-border-color;
		z-index: $zindex-popover;
		@include box-shadow($popover-box-shadow);

		// Header
		.fc-header {
			padding: $popover-header-padding-y $popover-header-padding-x;

			// Close button
			.fc-close {
				cursor: pointer;
				float: right;
				margin-left: $element-spacer-x;
			}

			// Title
			.fc-title {
				font-weight: $font-weight-semibold;
			}
		}

		// Unthemed
		.fc-unthemed & {
			.fc-header .fc-close {
				opacity: 0.6;
				@include transition(opacity ease-in-out $component-transition-timer);

				// Hover state
				@include hover {
					opacity: 1;
				}
			}
		}
	}


	// Misc reusable components
	// ------------------------------

	// Divider
	.fc-divider {
		border-style: solid;
		border-width: $table-border-width;
	}
	hr.fc-divider {
		height: 0;
		margin: 0;
		padding: 0;
		border-width: $table-border-width 0;
	}


	// Clear helper
	.fc-clear {
		clear: both;
	}

	// Element positioning
	.fc-bg,
	.fc-bgevent-skeleton,
	.fc-highlight-skeleton,
	.fc-helper-skeleton {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
	}

	// General background
	.fc-bg {
		bottom: 0;

		table {
			height: 100%;
		}
	}


	// Tables
	// ------------------------------

	.fc {
		table {
			width: 100%;
			table-layout: fixed;
			border-collapse: collapse;
			border-spacing: 0;
			box-sizing: border-box;
		}

		th,
		td {
			border-style: solid;
			border-width: $table-border-width;
			padding: 0;
			vertical-align: top;
		}

		th {
			padding: $table-cell-padding;
			padding-left: 0;
			padding-right: 0;
			font-weight: $font-weight-semibold;
			text-align: center;
		}

		td.fc-today {
			border-style: double;
		}
	}


	// Internal Nav Links
	// ------------------------------

	a[data-goto] {
		cursor: pointer;
		color: $body-color;
	}


	// Fake table rows
	// ------------------------------

	.fc-row {
		border-style: solid;
		border-width: 0;
		position: relative;

		table {
			border-left: 0;
			border-right: 0;
			border-bottom: 0; 
		}

		&:first-child table {
			border-top: 0;
		}
	}


	// Day row (used within the header and the DayGrid)
	// ------------------------------

	.fc-row {

		// Main background
		.fc-bg {
			z-index: 1;
		}

		// Highlighting cells & background event skeleton
		.fc-bgevent-skeleton,
		.fc-highlight-skeleton {
			bottom: 0;

			table {
				height: 100%;
			}

			td {
				border-color: transparent;
				border: 0;
			}
		}
		.fc-bgevent-skeleton {
			z-index: 2;
		}
		.fc-highlight-skeleton {
			z-index: 3;
		}

		// Row content
		.fc-content-skeleton,
		.fc-helper-skeleton {
			td {
				background: none;
				border-bottom: 0;
			}

			// Cells with events inside (so NOT the day number cell)
			tbody td {
				border-top: 0;
			}
		}
		.fc-content-skeleton {
			position: relative;
			z-index: 4;
		}
		.fc-helper-skeleton {
			z-index: 5;
		}
	}


	// Scrolling container
	// ------------------------------

	.fc-scroller {
		-webkit-overflow-scrolling: touch;

		// Timing grids
		> .fc-day-grid,
		> .fc-time-grid {
			position: relative;
			width: 100%;
		}
	}


	// Global event styles
	// ------------------------------

	.fc-event {
		position: relative;
		display: block;
		border: $fullcalendar-event-border-width solid $fullcalendar-event-border-color;
		color: $fullcalendar-event-color!important;
		@include border-radius($border-radius);

		// Give events with links and draggable events a hand mouse pointer
		&[href],
		&.fc-draggable {
			cursor: pointer;
		}

		// Event background
		.fc-bg {
			z-index: 1;
		}

		// Event content
		.fc-content {
			position: relative;
			z-index: 2;
		}


		//
		// Resizer
		//

		// Base
		.fc-resizer {
			position: absolute;
			z-index: 4;
			display: none;
		}

		// Show resizer
		&.fc-allow-mouse-resize .fc-resizer,
		&.fc-selected .fc-resizer {
			display: block;
		}

		// Hit area
		&.fc-selected .fc-resizer:before {
			content: "";
			position: absolute;
			z-index: $zindex-tooltip;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			@include size(($fullcalendar-event-padding-y * 2) + $line-height-computed);
		}


		//
		// Event selection (only for touch devices)
		//

		.fc-event {
			&.fc-selected {
				z-index: $zindex-tooltip !important;
			}
		}
	}

	// Backgrounds
	.fc-event,
	.fc-event-dot {
		background-color: $fullcalendar-event-bg;
	}

	// Override an event's custom cursor
	.fc-not-allowed {
		&,
		.fc-event {
			cursor: $cursor-disabled;
		}
	}


	// Horizontal events
	// ------------------------------

	.fc-h-event {


		//
		// Resizer
		//

		// End
		.fc-end-resizer {
			cursor: e-resize;
			right: -($fullcalendar-event-border-width);
		}

		// In selected event
		&.fc-selected {
			.fc-end-resizer {
				margin-right: calc(-#{$fullcalendar-event-resizer-size-h / 2} - #{$fullcalendar-event-border-width});
			}
		}

		// Resizer (mouse devices)
		&.fc-allow-mouse-resize .fc-resizer {
			width: $fullcalendar-event-padding-x;
			top: -($fullcalendar-event-border-width);
			bottom: -($fullcalendar-event-border-width);
		}

		// Resizer (touch devices)
		&.fc-selected .fc-resizer {
			border-width: $fullcalendar-event-border-width;
			border-style: solid;
			border-color: inherit;
			background-color: $fullcalendar-event-color;
			top: 50%;
			transform: translateY(-50%);
			@include border-radius($border-radius-circle);
			@include size($fullcalendar-event-resizer-size-h);
		}
	}


	// DayGrid events
	// ------------------------------

	.fc-day-grid-event {
		margin: $fullcalendar-event-spacer;
		margin-top: 0;
		padding: $fullcalendar-event-padding-y $fullcalendar-event-padding-x;

		// Darken selected event
		&.fc-selected:after {
			content: "";
			position: absolute;
			z-index: 1;
			top: -($fullcalendar-event-border-width);
			right: -($fullcalendar-event-border-width);
			bottom: -($fullcalendar-event-border-width);
			left: -($fullcalendar-event-border-width);
			background-color: rgba($black, 0.15);
			@include border-radius($border-radius);
		}

		// Force events to be one-line tall
		> .fc-content {
			@include text-truncate();
		}

		// Make the time semibold
		.fc-time {
			font-weight: $font-weight-semibold;
		}

		// Resizer
		&.fc-allow-mouse-resize {
			.fc-end-resizer {
				margin-right: -($fullcalendar-event-border-width);
			}
		}

		// Desktop view
		@include media-breakpoint-up(md) {

			// Time
			.fc-time {
				float: right;
				padding-left: $element-spacer-x;
				display: inline-block;
				z-index: 2;
				position: relative;
				background-color: inherit;
			}

			// Title
			.fc-title {
				z-index: 1;
				position: relative;
			}
		}
	}


	// Event limiting
	// ------------------------------

	// "More" link
	a.fc-more {
		margin: 0 $fullcalendar-event-spacer;
		background-color: $gray-200;
		display: block;
		padding: $fullcalendar-event-padding-y $fullcalendar-event-padding-x;
		text-align: center;
		font-size: $font-size-sm;
		line-height: $line-height-sm;
		cursor: pointer;
		@include border-radius($border-radius);
		@include transition(all ease-in-out $component-transition-timer);

		@include hover {
			background-color: $gray-300;
		}
	}

	// Rows and cells that are hidden because of a "more" link
	.fc-limited {
		display: none;
	}

	// Popover that appears when "more" link is clicked
	.fc-day-grid .fc-row {
		z-index: 1;
	}
	.fc-more-popover {
		z-index: 2;
		width: 15rem;

		.fc-event-container {
			padding: $popover-body-padding-y $popover-body-padding-x;
			padding-top: 0;
		}

		.fc-day-grid-event {
			margin: 0;

			+ .fc-day-grid-event {
				margin-top: $fullcalendar-event-spacer;
			}
		}
	}


	// Utilities
	// ------------------------------

	// Now indicator
	.fc-now-indicator {
		position: absolute;
	}

	// Not selectable
	.fc-unselectable {
		-webkit-touch-callout: none;
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
		user-select: none;
	}


	// Toolbar
	// ------------------------------

	.fc-toolbar {
		text-align: center;

		// Header toolbar
		&.fc-header-toolbar {
			margin-bottom: $spacer;
		}

		// Footer toolbar
		&.fc-footer-toolbar {
			margin-top: $spacer;
		}

		// Title text
		h2 {
			margin: ((($btn-padding-y * 2) + $line-height-computed + rem-calc($btn-border-width * 2) - ($h5-font-size * $headings-line-height)) / 2) 0;
			font-size: $h5-font-size;
		}

		// Left
		.fc-left {
			float: left;
			width: 33%;
		}

		// Right
		.fc-right {
			float: right;
			width: 33%;

			.fc-button-group {
				float: right;
			}
		}

		// Left/right buttons
		.fc-left,
		.fc-right {
			> .fc-button {
				margin-left: $element-spacer-x;
			}
		}

		// Center
		.fc-center {
			display: inline-block;
			vertical-align: middle;
		}

		// The things within each left/right/center section
		> * {
			> * {
				float: left;
			}

			// The first thing within each left/center/right section
			> :first-child {
				margin-left: 0;
			}
		}

		// Button layering (for border precedence)
		button {
			position: relative;

			&:focus {
				z-index: 5;
			}
		}

		// States
		.fc-button:hover {
			z-index: 2;
		}
		.fc-button:focus {
			z-index: 3;
		}
		.fc-button-active {
			z-index: 4;
		}
	}


	// View structure
	// ------------------------------

	.fc-view-container {
		border: $card-border-width solid $card-border-color;
		@include border-radius($card-border-radius);

		// Remove borders from table cells
		.fc-view > table {

			// Right border
			th,
			td {
				border-right-width: 0;
			}

			// Left border from first cell
			tr:first-child {
				th:first-child,
				td:first-child {
					border-left-width: 0;
				}
			}

			// Bottom border from last cell
			tr:last-child {
				td {
					border-bottom-width: 0;
				}
			}

			.fc-head {
				tr:first-child {
					> th,
					> td {
						border-top: 0;
					}
				}
			}

			.fc-head-container {
				border-top: 0;
				border-bottom: 0;

				.fc-widget-header {
					border-top: 0;
				}
			}

			.fc-body {
				> tr:first-child {
					> th,
					> td {
						border-top: 0;
					}
				}
				> tr:last-child {
					> th,
					> td {
						border-bottom: 0;
					}
				}

				.fc-row:first-child {
					tr:first-child {
						td,
						th {
							border-top: 0;
						}
					}
				}
			}
		}

		// Change box sizing
		* {
			&,
			&:before,
			&:after {
				box-sizing: content-box;
			}
		}
	}

	// Scope positioning and z-index's
	.fc-view {
		&,
		> table {
			position: relative;
			z-index: 1;
		}
	} 


	// Basic view
	// ------------------------------

	// Day row structure
	.fc-dayGridWeek-view,
	.fc-dayGridDay-view {
		.fc-content-skeleton {
			padding-top: $fullcalendar-event-spacer;
			padding-bottom: $fullcalendar-event-spacer;
		}
	}

	// Rows
	.fc-dayGrid-view tbody .fc-row {
		min-height: 6em;
	}

	// A "rigid" row will take up a constant amount of height
	// because content-skeleton is absolute
	.fc-row.fc-rigid {
		overflow: hidden;

		.fc-content-skeleton {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
		}
	}

	// Week and day number styling
	.fc-dayGrid-view {

		// Week and day
		.fc-day-number {
			padding: $fullcalendar-event-spacer;
		}

		.fc-week-number {
			color: $fullcalendar-weekday-color;
		}

		td.fc-week-number,
		th.fc-week-number {
			padding: $table-cell-padding;
			background-color: $fullcalendar-weekday-bg;
		}

		// Week number only
		td.fc-week-number {
			font-size: $font-size-sm;
			padding-top: $fullcalendar-event-spacer;
			padding-bottom: $fullcalendar-event-spacer;
		}

		span.fc-week-number {
			display: inline-block;
			font-size: $font-size-sm;
			padding: $fullcalendar-event-spacer;
		}

		// Day number only
		.fc-day-number {
			text-align: right;
		}

		// Top date
		.fc-day-top {

			// Day number
			.fc-day-number {
				float: right;
			}
		}
	}

	// Other month
	.fc-other-month {
		&.fc-day-top,
		&.fc-day-number {
			opacity: 0.3;
		}
	}


	// AgendaView all-day area
	// ------------------------------

	.fc-timeGrid-view {
		.fc-day-grid {
			position: relative;
			z-index: 2;

			.fc-row {
				min-height: 3em;

				.fc-content-skeleton {
					padding-top: $fullcalendar-event-spacer;
					padding-bottom: 0;
				}
			}
		}
	}


	// TimeGrid axis
	// ------------------------------

	.fc-axis {
		text-align: right;

		.fc & {
			vertical-align: middle;
			padding: 0 $fullcalendar-event-padding-x;
			height: (($fullcalendar-event-padding-x * 2) + $line-height-computed);
			white-space: nowrap;
		}
	}


	// TimeGrid structure
	// ------------------------------

	// Base
	.fc-time-grid-container,
	.fc-time-grid {
		position: relative;
		z-index: 1;
	}

	// Grid
	.fc-time-grid {
		min-height: 100%;

		table {
			border: 0 hidden transparent;
		}

		> .fc-bg {
			z-index: 1;
		}

		.fc-slats,
		> hr {
			position: relative;
			z-index: 2;
		}

		.fc-content-col {
			position: relative;
		}

		// Containers
		.fc-content-skeleton {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			z-index: 3;
		}
		.fc-business-container {
			position: relative;
			z-index: 1;
		}
		.fc-bgevent-container {
			position: relative;
			z-index: 2;
		}
		.fc-highlight-container {
			position: relative;
			z-index: 3;
		}
		.fc-event-container {
			position: relative;
			z-index: 4;
		}
		.fc-now-indicator-line {
			z-index: 5;
		}
		.fc-helper-container {
			position: relative;
			z-index: 6;
		}


		//
		// TimeGrid slats
		//

		.fc-slats {
			td {
				border-bottom: 0;
			}

			.fc-minor td {
				border-top-style: dotted;
			}
		}


		//
		// TimeGrid highlighting slots
		//

		// Container
		.fc-highlight-container {
			position: relative;
		}

		// Highlight
		.fc-highlight {
			position: absolute;
			left: 0;
			right: 0;
		}


		//
		// TimeGrid event containment
		//

		// Containers
		.fc-event-container,
		.fc-bgevent-container {
			position: relative;
		}

		// Blue event container
		.fc-event {
			box-shadow: 0 0 0 $fullcalendar-event-border-width $white;
		}

		// Events
		.fc-event,
		.fc-bgevent {
			position: absolute;
			z-index: 1;
			padding: $fullcalendar-event-padding-x;
		}

		// Background event
		.fc-bgevent {
			left: 0;
			right: 0;
		}
	}


	// Generic Vertical Event
	// ------------------------------

	.fc-v-event {

		// Events that are continuing from another day
		&.fc-not-start {
			@include border-top-radius(0);
		}

		// Replace space made by the top border with padding
		&.fc-not-end {
			@include border-bottom-radius(0);
		}
	}


	// TimeGrid event styling
	// ------------------------------

	.fc-time-grid-event {
		overflow: hidden;

		// Need to allow touch resizers to extend outside event's bounding box
		&.fc-selected {
			overflow: visible;

			// Hide semi-white background, to appear darker
			.fc-bg {
				display: none;
			}
		}

		// Contains the time and title, but no bg and resizer
		.fc-content {
			position: relative;
			overflow: hidden;
		}

		// Time font weight
		.fc-time {
			font-weight: $font-weight-semibold;
		}

		// Short mode, where time and title are on the same line
		&.fc-short {

			// Content
			.fc-content {
				white-space: nowrap;
			}

			// Time and title
			.fc-time,
			.fc-title {
				display: inline-block;
				vertical-align: top;
			}
			.fc-time {
				&:before {
					content: attr(data-start);
				}
				&:after {
					content: "\000A0-\000A0";
				}

				span {
					display: none;
				}
			}
			.fc-title {
				font-size: $font-size-sm;
				padding: 0;
			}
		}


		//
		// Resizer
		//

		.fc-resizer {

		}

		// Cursor device
		&.fc-allow-mouse-resize {
			.fc-resizer {
				z-index: 3;
				left: 0;
				right: 0;
				bottom: 0;
				overflow: hidden;
				line-height: 1;
				text-align: center;
				cursor: s-resize;
				background-color: rgba($black, 0.1);

				// Small icon
				&:after {
					content: "=";
					display: inline-block;
					vertical-align: middle;
				}
			}
		}

		// Touch devide
		&.fc-selected .fc-resizer {
			border-width: $fullcalendar-event-border-width;
			border-style: solid;
			border-color: inherit;
			background-color: $fullcalendar-event-color;
			left: 50%;
			bottom: 0;
			margin-bottom: calc(-#{$fullcalendar-event-resizer-size-h / 2} - #{$fullcalendar-event-border-width});
			transform: translateX(-50%);
			@include size($fullcalendar-event-resizer-size-v);
			@include border-radius($border-radius-circle);
		}
	}


	// Now indicator
	// ------------------------------

	.fc-time-grid {

		// Line
		.fc-now-indicator-line {
			border-top-width: $table-border-width;
			left: 0;
			right: 0;
		}

		// Arrow
		.fc-now-indicator-arrow {
			left: 0;
			margin-top: -$tooltip-arrow-width;
			border-width: $tooltip-arrow-width 0 $tooltip-arrow-width $tooltip-arrow-width;
			border-top-color: transparent;
			border-bottom-color: transparent;
		}
	}


	// List view
	// ------------------------------

	// Event dot
	.fc-event-dot {
		display: inline-block;
		@include size($badge-mark-size);
		@include border-radius($border-radius-circle);
	}

	// List view base
	.fc-list-view {
		border-width: 0;
		border-style: solid;
		direction: ltr;
	}


	//
	// Table resets
	//

	.fc-list-table {
		.fc & {
			table-layout: auto;
		}

		td {
			border-width: $table-border-width 0 0;
			padding: $table-cell-padding;
		}

		tr:first-child td {
			border-top-width: 0;
		}
	}


	//
	// Day headings with the list
	//

	.fc-list-heading {
		border-bottom-width: $table-border-width;

		td {
			font-weight: $font-weight-semibold;
		}
	}
	.fc-list-heading-main {
		float: left;
	}
	.fc-list-heading-alt {
		float: right;
	}


	//
	// Event list items
	//

	// Item
	.fc-list-item {
		td {
			@include transition(all ease-in-out $component-transition-timer);
		}

		&.fc-has-url {
			cursor: pointer;
		}

		// Change bg on hover
		@include hover {
			td {
				background-color: $table-hover-bg;
			}
		}
	}

	// Markers
	.fc-list-item-marker,
	.fc-list-item-time {
		white-space: nowrap;
		width: 1px;
	}

	// Make the dot closer to the event title
	.fc-list-item-marker {
		padding-right: 0;
	}

	// Title
	.fc-list-item-title a {
		text-decoration: none;
		color: inherit;
	}


	//
	// Message when no events
	//

	.fc-list-empty-wrap2 {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}
	.fc-list-empty-wrap1 {
		display: table;
		@include size(100%);
	}
	.fc-list-empty {
		display: table-cell;
		vertical-align: middle;
		text-align: center;
	}
	.fc-unthemed .fc-list-empty { /* theme will provide own background */
		background-color: $fullcalendar-nonbusiness-bg;
	}


	// Responsive stuff
	// ------------------------------

	@include media-breakpoint-down(sm) {
		.fc-toolbar {
			.fc-left,
			.fc-left > .fc-button,
			.fc-right,
			.fc-right > .fc-button,
			.fc-left .fc-button-group,
			.fc-right .fc-button-group {
				float: none;
				width: auto;
				vertical-align: top;
			}

			.fc-left,
			.fc-right {
				display: block;
				margin-bottom: ($spacer / 2);
			}
		}
	}
}
