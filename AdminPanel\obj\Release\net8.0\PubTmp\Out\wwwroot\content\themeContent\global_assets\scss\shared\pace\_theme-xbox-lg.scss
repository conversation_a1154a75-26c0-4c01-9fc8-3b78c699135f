/* ------------------------------------------------------------------------------
 *
 *  # Pace. Xbox theme
 *
 *  Xbox css spinner theme for Pace. Default size
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-pace {

    // Define variables
    $pace-show-text: true;
    $pace-overlay-color: $color-slate-900;
    $pace-loader-color: $white;
    $pace-loader-size: 2.75rem;
    $pace-loader-border-width: 0.1875rem;
    $pace-loader-border-spacer: 0.3125rem;


    // Pace theme styles
    // ------------------------------

    // Overlay
    .pace-running {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: $pace-overlay-color;

        // Hide all content
        > *:not(.pace) {
            opacity: 0;
        }
    }

    // Base
    .pace {
        position: fixed;
        left: 50%;
        top: 50%;
        margin-left: -($pace-loader-size / 2);
        margin-top: -($pace-loader-size / 2);
        z-index: 9999;
        user-select: none;
        pointer-events: none;
        @include size($pace-loader-size);

        // Change colors on light/dark backgrounds
        @if (lightness($pace-overlay-color) < 75) {
            color: $white;
        }
    }

    // Progress
    .pace-progress {
        width: 100% !important;
    }

    // Activity
    .pace-activity {
        position: absolute;
        top: 0;
        // left: 0;
        animation: rotation 1.5s ease-in-out infinite;
        @include size($pace-loader-size);

        // Circles
        &,
        &:before,
        &:after {
            border: $pace-loader-border-width solid transparent;
            border-top-color: $pace-loader-color;
            border-radius: $border-radius-circle;
        }

        &:before {
            content: "";
            position: absolute;
            top: -($pace-loader-border-spacer + ($pace-loader-border-width * 2));
            left: -($pace-loader-border-spacer + ($pace-loader-border-width * 2));
            right: -($pace-loader-border-spacer + ($pace-loader-border-width * 2));
            bottom: -($pace-loader-border-spacer + ($pace-loader-border-width * 2));
            animation: rotation 2s ease-in-out infinite;
        }

        &:after {
            content: "";
            position: absolute;
            top: $pace-loader-border-spacer;
            left: $pace-loader-border-spacer;
            right: $pace-loader-border-spacer;
            bottom: $pace-loader-border-spacer;
            animation: rotation 1s ease-in-out infinite;
        }
    }

    // Hide inactive
    .pace-inactive {
        display: none;
    }


    // Progress text
    @if $pace-show-text {
        .pace-progress:after {
            content: attr(data-progress-text);
            text-align: center;
            width: 100%;
            display: inline-block;
            white-space: nowrap;
            margin-top: ($pace-loader-size + $pace-loader-border-width + $pace-loader-border-spacer + ($spacer / 1.5));
        }
    }
}
