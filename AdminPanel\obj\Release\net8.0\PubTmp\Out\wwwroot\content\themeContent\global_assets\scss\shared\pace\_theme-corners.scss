/* ------------------------------------------------------------------------------
 *
 *  # Pace. Corners theme
 *
 *  Corners css spinner theme for Pace.
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-pace {

    // Define variables
    $pace-show-text: true;
    $pace-overlay-color: $color-slate-900;
    $pace-loader-color: $white;
    $pace-loader-size-max: 1.625rem;
    $pace-loader-size-min: ($pace-loader-size-max / 2);


    // Pace theme styles
    // ------------------------------

    // Overlay
    .pace-running {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: $pace-overlay-color;

        // Hide all content
        > *:not(.pace) {
            opacity: 0;
        }
    }

    // Base
    .pace {
        position: fixed;
        top: 50%;
        left: 0;
        right: 0;
        margin-top: -($pace-loader-size-max / 2);
        z-index: 9999;
        user-select: none;
        pointer-events: none;

        // Change colors on light/dark backgrounds
        @if (lightness($pace-overlay-color) < 75) {
            color: $white;
        }
    }

    // Progress
    .pace-progress {
        width: 100% !important;
    }

    // Activity
    .pace-activity {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        margin: auto;
        background-color: $pace-loader-color;
        border-radius: $border-radius-circle;
        animation: corners 1.5s ease infinite;
        @include size($pace-loader-size-min);
    }

    // Hide inactive
    .pace-inactive {
        display: none;
    }

    // Progress text
    @if $pace-show-text {
        .pace-progress:after {
            content: attr(data-progress-text);
            text-align: center;
            width: 100%;
            display: inline-block;
            white-space: nowrap;
            margin-top: $pace-loader-size-max + ($spacer / 1.5);
        }
    }

    // Animation
    @keyframes corners {
        0% { transform: scale(1) rotate(0deg); }
        50% { border-radius: 0; transform: scale($pace-loader-size-max / $pace-loader-size-min) rotate(-180deg); }
        100% { transform: scale(1) rotate(-360deg); }
    }

    @-webkit-keyframes corners {
        0% { -webkit-transform: scale(1) rotate(0deg); }
        50% { border-radius: 0; -webkit-transform: scale($pace-loader-size-max / $pace-loader-size-min) rotate(-180deg); }
        100% { -webkit-transform: scale(1) rotate(-360deg); }
    }

    @-moz-keyframes corners {
        0% { -moz-transform: scale(1) rotate(0deg); }
        50% { border-radius: 0; -moz-transform: scale($pace-loader-size-max / $pace-loader-size-min) rotate(-180deg); }
        100% { -moz-transform: scale(1) rotate(-360deg); }
    }
}
