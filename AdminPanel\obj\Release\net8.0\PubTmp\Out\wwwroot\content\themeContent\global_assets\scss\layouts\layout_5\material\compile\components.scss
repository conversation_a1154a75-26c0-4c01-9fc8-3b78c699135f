/* ------------------------------------------------------------------------------
 *
 *  # Components
 *
 *  Components import. Ordering matters. See _config.scss for more options
 *
 * ---------------------------------------------------------------------------- */


// Core
// ------------------------------

// Import custom template config
@import "../../../../config";
@import "../../../../shared/utils/ll-functions";
@import "../../../../shared/utils/ll-mixins";
@import "../../../../themes/material/colors/palette";

// Core variables and mixins
@import "../../../../_bootstrap/functions";
@import "../../../../_bootstrap/variables";
@import "../../../../_bootstrap/mixins";

// Import template's variables
@import "../variables/variables-core";
@import "../variables/variables-custom";
@import "../../../../themes/material/bootstrap_limitless/mixins";

// FOUC helpers
@import "../../../../shared/utils/fouc";


// Page loader
// ------------------------------

// Pace page loader
@import "../../../../shared/pace/theme-default";

// Pace.js demo, remove in real project
@import "../../../../shared/pace/pace-demo";


// Forms
// ------------------------------

// Uniform
@import "../../../../themes/material/components/forms/uniform";

// Switchery
@import "../../../../themes/material/components/forms/switchery";

// BS switch
@import "../../../../themes/material/components/forms/bootstrap-switch";


// Select2
@import "../../../../themes/material/components/forms/select2";

// Multiselect
@import "../../../../themes/material/components/forms/multiselect";


// Passy
@import "../../../../themes/material/components/forms/passy";

// Typeahead
@import "../../../../themes/material/components/forms/typeahead";

// Validation
@import "../../../../themes/material/components/forms/validation";

// Floating labels
@import "../../../../themes/material/components/forms/floating-labels";

// Alpaca
@import "../../../../themes/material/components/forms/alpaca";


// Tokenfield
@import "../../../../themes/material/components/forms/tokenfield";

// Tags input
@import "../../../../themes/material/components/forms/tags-input";


// Touchspin spinners
@import "../../../../themes/material/components/forms/touchspin";

// Dual listboxes
@import "../../../../themes/material/components/forms/dual-listbox";


// Wizard
@import "../../../../themes/material/components/forms/wizard";


// Text editors
// ------------------------------

// Summernote
@import "../../../../themes/material/components/forms/editors/summernote";

// Trumbowyg
@import "../../../../themes/material/components/forms/editors/trumbowyg";

// Ace
@import "../../../../themes/material/components/forms/editors/ace";


// Pickers
// ------------------------------

// Daterange picker
@import "../../../../themes/material/components/pickers/daterange";

// Pickadate base
@import "../../../../themes/material/components/pickers/pickadate/base";

// Pickadate date picker
@import "../../../../themes/material/components/pickers/pickadate/date";

// Pickadate time picker
@import "../../../../themes/material/components/pickers/pickadate/time";

// Anytime picker
@import "../../../../themes/material/components/pickers/anytime";

// Color picker
@import "../../../../themes/material/components/pickers/spectrum";


// File uploaders
// ------------------------------

// Plupload
@import "../../../../themes/material/components/forms/uploaders/plupload";

// File input
@import "../../../../themes/material/components/forms/uploaders/file-input";

// Dropzone
@import "../../../../themes/material/components/forms/uploaders/dropzone";


// Notifications
// ------------------------------

// Noty notifications
@import "../../../../themes/material/components/notifications/noty";

// PNotify notifications
@import "../../../../themes/material/components/notifications/pnotify";

// jGrowl notifications
@import "../../../../themes/material/components/notifications/jgrowl.scss";

// Sweet Alerts
@import "../../../../themes/material/components/notifications/sweet-alerts";


// Sliders
// ------------------------------

// Slider pips
@import "../../../../themes/material/components/sliders/slider-pips";

// NoUI slider
@import "../../../../themes/material/components/sliders/noui-slider";

// Ion range slider
@import "../../../../themes/material/components/sliders/ion-range-slider";


// jQuery UI components
// ------------------------------

// Interactions
@import "../../../../themes/material/components/jquery_ui/interactions";

// Widgets
@import "../../../../themes/material/components/jquery_ui/widgets";


// UI components
// ------------------------------

// [Material] Ripple effect
@import "../../../../themes/material/components/ui/ripple";

// Prism - syntax highlighter
@import "../../../../themes/material/components/ui/prism";

// Slinky - multi level drilldown menu
@import "../../../../themes/material/components/ui/slinky";

// Sticky kit
@import "../../../../themes/material/components/ui/sticky";

// Headroom - hhide navbar on scroll
@import "../../../../themes/material/components/ui/headroom";

// Dragula
@import "../../../../themes/material/components/ui/dragula";

// Perfect scrollbar
@import "../../../../themes/material/components/ui/perfect-scrollbar";


// Misc components
// ------------------------------

// [Material] Floating action buttons
@import "../../../../themes/material/components/misc/fab";

// Fancytree
@import "../../../../themes/material/components/misc/fancytree";

// Progress buttons
@import "../../../../themes/material/components/misc/progress-buttons";

// Full calendar
@import "../../../../themes/material/components/misc/fullcalendar.scss";

// Image cropper
@import "../../../../themes/material/components/misc/image-cropper";

// Lightbox plugin
@import "../../../../themes/material/components/misc/fancybox";


// Tables
// ------------------------------

// Footable - responsive table tools
@import "../../../../themes/material/components/tables/footable";


// Handsontable
// ------------------------------

// Handsontable - excel-like spreadsheet for apps
@import "../../../../themes/material/components/tables/handsontable";


// Datatables
// ------------------------------

// Core
@import "../../../../themes/material/components/tables/datatables/datatables";

// Datatables - columns reorder
@import "../../../../themes/material/components/tables/datatables/datatable-columns-reorder";

// Datatables - rows reorder
@import "../../../../themes/material/components/tables/datatables/datatable-rows-reorder";

// Datatables - fixed columns
@import "../../../../themes/material/components/tables/datatables/datatable-fixed-columns";

// Datatables - fixed header
@import "../../../../themes/material/components/tables/datatables/datatable-fixed-header";

// Datatables - autofill
@import "../../../../themes/material/components/tables/datatables/datatable-autofill";

// Datatables - select
@import "../../../../themes/material/components/tables/datatables/datatable-select";

// Datatables - buttons
@import "../../../../themes/material/components/tables/datatables/datatable-buttons";

// Datatables - key table
@import "../../../../themes/material/components/tables/datatables/datatable-keytable";

// Datatables - scroller
@import "../../../../themes/material/components/tables/datatables/datatable-scroller";

// Datatables - responsive
@import "../../../../themes/material/components/tables/datatables/datatable-responsive";


// Maps
// ------------------------------

// Google Maps
@import "../../../../shared/maps/google-maps";

// jVectorMap
@import "../../../../shared/maps/jvectormap";


// Charts
// ------------------------------

// Charts base
@import "../../../../shared/charts/charts";

// C3 chart library
@import "../../../../shared/charts/c3";

// D3 chart library
@import "../../../../shared/charts/d3";


// Page kits
// ------------------------------

// ECommerce
@import "../../../../shared/pages/ecommerce";

// Blog
@import "../../../../shared/pages/blog";

// Task manager
@import "../../../../shared/pages/task-manager";

// Inbox
@import "../../../../shared/pages/inbox";

// Profile
@import "../../../../shared/pages/profile";

// Login
@import "../../../../shared/pages/login";

// Timelines
@import "../../../../shared/pages/timelines";

// Chats
@import "../../../../shared/pages/chats";

// Error page
@import "../../../../shared/pages/error";


// Other components
// ------------------------------

// Heading elements
@import "../../../../themes/material/components/ui/header-elements";

// Helpers
@import "../../../../shared/utils/helpers";

// Plugins demo, remove in real project
@import "../../../../themes/material/components/demo";
