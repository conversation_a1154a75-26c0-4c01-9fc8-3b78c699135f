
// Database-backed verification store
// Note: This file now uses database storage instead of in-memory storage

interface VerificationCodeData {
  code: string;
  expires: number;
  attempts: number;
}

interface VerificationAttemptData {
  count: number;
  resetTime: number;
}

// In-memory store for verification attempts (rate limiting)
const verificationAttempts = new Map<string, VerificationAttemptData>();

// Clean up old verification attempts every 5 minutes
setInterval(() => {
  const now = Date.now();
  verificationAttempts.forEach((value, key) => {
    if (value.resetTime < now) {
      verificationAttempts.delete(key);
    }
  });
}, 5 * 60 * 1000); // Run every 5 minutes

// Runtime helpers
function isServer() {
  return typeof window === 'undefined';
}

function getApiBaseUrl() {
  const raw = process.env.ADMIN_BASE_URL || process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com';
  return raw.endsWith('/') ? raw.slice(0, -1) : raw;
}

// Store verification code in database
export async function storeVerificationCode(phoneNumber: string, code: string): Promise<boolean> {
  try {
    console.log('🔧 Verification store - Starting request:', {
      phoneNumber: phoneNumber,
      codeLength: code.length
    });

    // Always use absolute URL to backend API
    const apiUrl = `${getApiBaseUrl()}/api/v1/verification/store`;
    console.log('🔧 Using API URL:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        PhoneNumber: phoneNumber,        // PascalCase for backend API
        VerificationCode: code,          // PascalCase for backend API
        ExpirationMinutes: 10
      })
    });

    console.log('📥 Verification store response status:', response.status);
    console.log('📥 Verification store response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Verification store HTTP error:', response.status, response.statusText);
      console.error('❌ Verification store error response body:', errorText);
      return false;
    }

    const result = await response.json();
    console.log('✅ Verification store response:', result);

    if (result.success) {
      console.log('✅ Verification code stored successfully');
      return true;
    } else {
      console.error('❌ Verification store failed:', result.error || 'Unknown error');
      return false;
    }
  } catch (error) {
    console.error('❌ Error storing verification code:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('❌ Error details:', errorMessage);
    return false;
  }
}

// Get verification code from database
export async function getVerificationCode(phoneNumber: string): Promise<VerificationCodeData | null> {
  try {
    // Always use absolute URL to backend API
    const apiUrl = `${getApiBaseUrl()}/api/v1/verification/get`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber: phoneNumber }) // camelCase for frontend API
    });

    if (!response.ok) {
      console.error('HTTP error:', response.status, response.statusText);
      return null;
    }

    const result = await response.json();
    console.log('Verification get response:', result);

    if (result.success && result.data) {
      return {
        code: result.data.verificationCode,
        expires: new Date(result.data.expiresAt).getTime(),
        attempts: result.data.attemptCount
      };
    }
    return null;
  } catch (error) {
    console.error('Error getting verification code:', error);
    return null;
  }
}

// Delete verification code from database
export async function deleteVerificationCode(phoneNumber: string): Promise<boolean> {
  try {
    // Always use absolute URL to backend API
    const apiUrl = `${getApiBaseUrl()}/api/v1/verification/delete`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber: phoneNumber }) // camelCase for frontend API
    });

    if (!response.ok) {
      console.error('HTTP error:', response.status, response.statusText);
      return false;
    }

    const result = await response.json();
    console.log('Verification delete response:', result);
    return result.success;
  } catch (error) {
    console.error('Error deleting verification code:', error);
    return false;
  }
}

// Track verification attempts
export async function trackVerificationAttempt(phoneNumber: string) {
  const now = Date.now();
  const attemptKey = `va:${phoneNumber}`;
  
  let attemptData = verificationAttempts.get(attemptKey) || { 
    count: 0, 
    resetTime: now + (15 * 60 * 1000) // 15 minutes from now
  };
  
  // Reset counter if window has passed
  if (now >= attemptData.resetTime) {
    attemptData = { 
      count: 1, 
      resetTime: now + (15 * 60 * 1000) 
    };
  } else {
    attemptData.count += 1;
  }
  
  verificationAttempts.set(attemptKey, attemptData);
  return attemptData;
}
