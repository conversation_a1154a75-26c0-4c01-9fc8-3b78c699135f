/* @license C3.js v0.4.21 | (c) C3 Team and other contributors | http://c3js.org/ */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.c3=e()}(this,function(){"use strict";var t,e,i={target:"c3-target",chart:"c3-chart",chartLine:"c3-chart-line",chartLines:"c3-chart-lines",chartBar:"c3-chart-bar",chartBars:"c3-chart-bars",chartText:"c3-chart-text",chartTexts:"c3-chart-texts",chartArc:"c3-chart-arc",chartArcs:"c3-chart-arcs",chartArcsTitle:"c3-chart-arcs-title",chartArcsBackground:"c3-chart-arcs-background",chartArcsGaugeUnit:"c3-chart-arcs-gauge-unit",chartArcsGaugeMax:"c3-chart-arcs-gauge-max",chartArcsGaugeMin:"c3-chart-arcs-gauge-min",selectedCircle:"c3-selected-circle",selectedCircles:"c3-selected-circles",eventRect:"c3-event-rect",eventRects:"c3-event-rects",eventRectsSingle:"c3-event-rects-single",eventRectsMultiple:"c3-event-rects-multiple",zoomRect:"c3-zoom-rect",brush:"c3-brush",focused:"c3-focused",defocused:"c3-defocused",region:"c3-region",regions:"c3-regions",title:"c3-title",tooltipContainer:"c3-tooltip-container",tooltip:"c3-tooltip",tooltipName:"c3-tooltip-name",shape:"c3-shape",shapes:"c3-shapes",line:"c3-line",lines:"c3-lines",bar:"c3-bar",bars:"c3-bars",circle:"c3-circle",circles:"c3-circles",arc:"c3-arc",arcLabelLine:"c3-arc-label-line",arcs:"c3-arcs",area:"c3-area",areas:"c3-areas",empty:"c3-empty",text:"c3-text",texts:"c3-texts",gaugeValue:"c3-gauge-value",grid:"c3-grid",gridLines:"c3-grid-lines",xgrid:"c3-xgrid",xgrids:"c3-xgrids",xgridLine:"c3-xgrid-line",xgridLines:"c3-xgrid-lines",xgridFocus:"c3-xgrid-focus",ygrid:"c3-ygrid",ygrids:"c3-ygrids",ygridLine:"c3-ygrid-line",ygridLines:"c3-ygrid-lines",axis:"c3-axis",axisX:"c3-axis-x",axisXLabel:"c3-axis-x-label",axisY:"c3-axis-y",axisYLabel:"c3-axis-y-label",axisY2:"c3-axis-y2",axisY2Label:"c3-axis-y2-label",legendBackground:"c3-legend-background",legendItem:"c3-legend-item",legendItemEvent:"c3-legend-item-event",legendItemTile:"c3-legend-item-tile",legendItemHidden:"c3-legend-item-hidden",legendItemFocused:"c3-legend-item-focused",dragarea:"c3-dragarea",EXPANDED:"_expanded_",SELECTED:"_selected_",INCLUDED:"_included_"},n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},s=function(t){return t||0===t},o=function(t){return"function"==typeof t},c=function(t){return Array.isArray(t)},d=function(t){return"string"==typeof t},l=function(t){return void 0===t},u=function(t){return void 0!==t},h=function(t){return 10*Math.ceil(t/10)},g=function(t){return Math.ceil(t)+.5},f=function(t){return t[1]-t[0]},p=function(t){return null==t||d(t)&&0===t.length||"object"===(void 0===t?"undefined":n(t))&&0===Object.keys(t).length},_=function(t){return!T.isEmpty(t)},x=function(t,e,i){return u(t[e])?t[e]:i},y=function(t,e){var i=!1;return Object.keys(t).forEach(function(n){t[n]===e&&(i=!0)}),i},m=function(t){return"string"==typeof t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;"):t},S=function(t){var e=t.getBoundingClientRect(),i=[t.pathSegList.getItem(0),t.pathSegList.getItem(1)];return{x:i[0].x,y:Math.min(i[0].y,i[1].y),width:e.width,height:e.height}};function w(t,e){var i=this;i.component=t,i.params=e||{},i.d3=t.d3,i.scale=i.d3.scale.linear(),i.range,i.orient="bottom",i.innerTickSize=6,i.outerTickSize=this.params.withOuterTick?6:0,i.tickPadding=3,i.tickValues=null,i.tickFormat,i.tickArguments,i.tickOffset=0,i.tickCulling=!0,i.tickCentered,i.tickTextCharSize,i.tickTextRotate=i.params.tickTextRotate,i.tickLength,i.axis=i.generateAxis()}(e=w.prototype).axisX=function(t,e,i){t.attr("transform",function(t){return"translate("+Math.ceil(e(t)+i)+", 0)"})},e.axisY=function(t,e){t.attr("transform",function(t){return"translate(0,"+Math.ceil(e(t))+")"})},e.scaleExtent=function(t){var e=t[0],i=t[t.length-1];return e<i?[e,i]:[i,e]},e.generateTicks=function(t){var e,i,n=[];if(t.ticks)return t.ticks.apply(t,this.tickArguments);for(i=t.domain(),e=Math.ceil(i[0]);e<i[1];e++)n.push(e);return n.length>0&&n[0]>0&&n.unshift(n[0]-(n[1]-n[0])),n},e.copyScale=function(){var t,e=this.scale.copy();return this.params.isCategory&&(t=this.scale.domain(),e.domain([t[0],t[1]-1])),e},e.textFormatted=function(t){var e=this.tickFormat?this.tickFormat(t):t;return void 0!==e?e:""},e.updateRange=function(){var t=this;return t.range=t.scale.rangeExtent?t.scale.rangeExtent():t.scaleExtent(t.scale.range()),t.range},e.updateTickTextCharSize=function(t){var e=this;if(e.tickTextCharSize)return e.tickTextCharSize;var i={h:11.5,w:5.5};return t.select("text").text(function(t){return e.textFormatted(t)}).each(function(t){var n=this.getBoundingClientRect(),a=e.textFormatted(t),r=n.height,s=a?n.width/a.length:void 0;r&&s&&(i.h=r,i.w=s)}).text(""),e.tickTextCharSize=i,i},e.transitionise=function(t){return this.params.withoutTransition?t:this.d3.transition(t)},e.isVertical=function(){return"left"===this.orient||"right"===this.orient},e.tspanData=function(t,e,i,n){var a=this.params.tickMultiline?this.splitTickText(t,i,n):[].concat(this.textFormatted(t));return a.map(function(t){return{index:e,splitted:t,length:a.length}})},e.splitTickText=function(t,e,i){var n,a,r,s=this,o=s.textFormatted(t),c=s.params.tickWidth;if("[object Array]"===Object.prototype.toString.call(o))return o;return(!c||c<=0)&&(c=s.isVertical()?95:s.params.isCategory?Math.ceil(i(e[1])-i(e[0]))-12:110),function t(e,i){a=void 0;for(var o=1;o<i.length;o++)if(" "===i.charAt(o)&&(a=o),n=i.substr(0,o+1),r=s.tickTextCharSize.w*n.length,c<r)return t(e.concat(i.substr(0,a||o)),i.slice(a?a+1:o));return e.concat(i)}([],o+"")},e.updateTickLength=function(){this.tickLength=Math.max(this.innerTickSize,0)+this.tickPadding},e.lineY2=function(t){var e=this,i=e.scale(t)+(e.tickCentered?0:e.tickOffset);return e.range[0]<i&&i<e.range[1]?e.innerTickSize:0},e.textY=function(){var t=this.tickTextRotate;return t?11.5-t/15*2.5*(t>0?1:-1):this.tickLength},e.textTransform=function(){var t=this.tickTextRotate;return t?"rotate("+t+")":""},e.textTextAnchor=function(){var t=this.tickTextRotate;return t?t>0?"start":"end":"middle"},e.tspanDx=function(){var t=this.tickTextRotate;return t?8*Math.sin(Math.PI*(t/180)):0},e.tspanDy=function(t,e){var i=this.tickTextCharSize.h;return 0===e&&(i=this.isVertical()?-((t.length-1)*(this.tickTextCharSize.h/2)-3):".71em"),i},e.generateAxis=function(){var t=this,e=t.d3,i=t.params;function n(a){a.each(function(){var a,r,s,o=n.g=e.select(this),c=this.__chart__||t.scale,d=this.__chart__=t.copyScale(),l=t.tickValues?t.tickValues:t.generateTicks(d),u=o.selectAll(".tick").data(l,d),h=u.enter().insert("g",".domain").attr("class","tick").style("opacity",1e-6),g=u.exit().remove(),f=t.transitionise(u).style("opacity",1);i.isCategory?(t.tickOffset=Math.ceil((d(1)-d(0))/2),r=t.tickCentered?0:t.tickOffset,s=t.tickCentered?t.tickOffset:0):t.tickOffset=r=0,h.append("line"),h.append("text"),t.updateRange(),t.updateTickLength(),t.updateTickTextCharSize(o.select(".tick"));var p=f.select("line"),_=f.select("text"),x=u.select("text").selectAll("tspan").data(function(e,i){return t.tspanData(e,i,l,d)});x.enter().append("tspan"),x.exit().remove(),x.text(function(t){return t.splitted});var y=o.selectAll(".domain").data([0]),m=(y.enter().append("path").attr("class","domain"),t.transitionise(y));switch(t.orient){case"bottom":a=t.axisX,p.attr("x1",r).attr("x2",r).attr("y2",function(e,i){return t.lineY2(e,i)}),_.attr("x",0).attr("y",function(e,i){return t.textY(e,i)}).attr("transform",function(e,i){return t.textTransform(e,i)}).style("text-anchor",function(e,i){return t.textTextAnchor(e,i)}),x.attr("x",0).attr("dy",function(e,i){return t.tspanDy(e,i)}).attr("dx",function(e,i){return t.tspanDx(e,i)}),m.attr("d","M"+t.range[0]+","+t.outerTickSize+"V0H"+t.range[1]+"V"+t.outerTickSize);break;case"top":a=t.axisX,p.attr("x2",0).attr("y2",-t.innerTickSize),_.attr("x",0).attr("y",-t.tickLength).style("text-anchor","middle"),x.attr("x",0).attr("dy","0em"),m.attr("d","M"+t.range[0]+","+-t.outerTickSize+"V0H"+t.range[1]+"V"+-t.outerTickSize);break;case"left":a=t.axisY,p.attr("x2",-t.innerTickSize).attr("y1",s).attr("y2",s),_.attr("x",-t.tickLength).attr("y",t.tickOffset).style("text-anchor","end"),x.attr("x",-t.tickLength).attr("dy",function(e,i){return t.tspanDy(e,i)}),m.attr("d","M"+-t.outerTickSize+","+t.range[0]+"H0V"+t.range[1]+"H"+-t.outerTickSize);break;case"right":a=t.axisY,p.attr("x2",t.innerTickSize).attr("y2",0),_.attr("x",t.tickLength).attr("y",0).style("text-anchor","start"),x.attr("x",t.tickLength).attr("dy",function(e,i){return t.tspanDy(e,i)}),m.attr("d","M"+t.outerTickSize+","+t.range[0]+"H0V"+t.range[1]+"H"+t.outerTickSize)}if(d.rangeBand){var S=d,w=S.rangeBand()/2;c=d=function(t){return S(t)+w}}else c.rangeBand?c=d:g.call(a,d,t.tickOffset);h.call(a,c,t.tickOffset),f.call(a,d,t.tickOffset)})}return n.scale=function(e){return arguments.length?(t.scale=e,n):t.scale},n.orient=function(e){return arguments.length?(t.orient=e in{top:1,right:1,bottom:1,left:1}?e+"":"bottom",n):t.orient},n.tickFormat=function(e){return arguments.length?(t.tickFormat=e,n):t.tickFormat},n.tickCentered=function(e){return arguments.length?(t.tickCentered=e,n):t.tickCentered},n.tickOffset=function(){return t.tickOffset},n.tickInterval=function(){var e;return(e=i.isCategory?2*t.tickOffset:(n.g.select("path.domain").node().getTotalLength()-2*t.outerTickSize)/n.g.selectAll("line").size())===1/0?0:e},n.ticks=function(){return arguments.length?(t.tickArguments=arguments,n):t.tickArguments},n.tickCulling=function(e){return arguments.length?(t.tickCulling=e,n):t.tickCulling},n.tickValues=function(e){if("function"==typeof e)t.tickValues=function(){return e(t.scale.domain())};else{if(!arguments.length)return t.tickValues;t.tickValues=e}return n},n};var v=function(i){function n(i){a(this,n);var s={fn:t,internal:{fn:e}},o=r(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,i,"axis",s));return o.d3=i.d3,o.internal=w,o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(n,P),n}();(t=v.prototype).init=function(){var t=this.owner,e=t.config,n=t.main;t.axes.x=n.append("g").attr("class",i.axis+" "+i.axisX).attr("clip-path",t.clipPathForXAxis).attr("transform",t.getTranslate("x")).style("visibility",e.axis_x_show?"visible":"hidden"),t.axes.x.append("text").attr("class",i.axisXLabel).attr("transform",e.axis_rotated?"rotate(-90)":"").style("text-anchor",this.textAnchorForXAxisLabel.bind(this)),t.axes.y=n.append("g").attr("class",i.axis+" "+i.axisY).attr("clip-path",e.axis_y_inner?"":t.clipPathForYAxis).attr("transform",t.getTranslate("y")).style("visibility",e.axis_y_show?"visible":"hidden"),t.axes.y.append("text").attr("class",i.axisYLabel).attr("transform",e.axis_rotated?"":"rotate(-90)").style("text-anchor",this.textAnchorForYAxisLabel.bind(this)),t.axes.y2=n.append("g").attr("class",i.axis+" "+i.axisY2).attr("transform",t.getTranslate("y2")).style("visibility",e.axis_y2_show?"visible":"hidden"),t.axes.y2.append("text").attr("class",i.axisY2Label).attr("transform",e.axis_rotated?"":"rotate(-90)").style("text-anchor",this.textAnchorForY2AxisLabel.bind(this))},t.getXAxis=function(t,e,i,n,a,r,s){var o=this.owner,c=o.config,d={isCategory:o.isCategorized(),withOuterTick:a,tickMultiline:c.axis_x_tick_multiline,tickWidth:c.axis_x_tick_width,tickTextRotate:s?0:c.axis_x_tick_rotate,withoutTransition:r},l=new this.internal(this,d).axis.scale(t).orient(e);return o.isTimeSeries()&&n&&"function"!=typeof n&&(n=n.map(function(t){return o.parseDate(t)})),l.tickFormat(i).tickValues(n),o.isCategorized()&&(l.tickCentered(c.axis_x_tick_centered),p(c.axis_x_tick_culling)&&(c.axis_x_tick_culling=!1)),l},t.updateXAxisTickValues=function(t,e){var i,n=this.owner,a=n.config;return(a.axis_x_tick_fit||a.axis_x_tick_count)&&(i=this.generateTickValues(n.mapTargetsToUniqueXs(t),a.axis_x_tick_count,n.isTimeSeries())),e?e.tickValues(i):(n.xAxis.tickValues(i),n.subXAxis.tickValues(i)),i},t.getYAxis=function(t,e,i,n,a,r,s){var o=this.owner,c=o.config,d={withOuterTick:a,withoutTransition:r,tickTextRotate:s?0:c.axis_y_tick_rotate},l=new this.internal(this,d).axis.scale(t).orient(e).tickFormat(i);return o.isTimeSeriesY()?l.ticks(o.d3.time[c.axis_y_tick_time_value],c.axis_y_tick_time_interval):l.tickValues(n),l},t.getId=function(t){var e=this.owner.config;return t in e.data_axes?e.data_axes[t]:"y"},t.getXAxisTickFormat=function(){var t=this.owner,e=t.config,i=t.isTimeSeries()?t.defaultAxisTimeFormat:t.isCategorized()?t.categoryName:function(t){return t<0?t.toFixed(0):t};return e.axis_x_tick_format&&(o(e.axis_x_tick_format)?i=e.axis_x_tick_format:t.isTimeSeries()&&(i=function(i){return i?t.axisTimeFormat(e.axis_x_tick_format)(i):""})),o(i)?function(e){return i.call(t,e)}:i},t.getTickValues=function(t,e){return t||(e?e.tickValues():void 0)},t.getXAxisTickValues=function(){return this.getTickValues(this.owner.config.axis_x_tick_values,this.owner.xAxis)},t.getYAxisTickValues=function(){return this.getTickValues(this.owner.config.axis_y_tick_values,this.owner.yAxis)},t.getY2AxisTickValues=function(){return this.getTickValues(this.owner.config.axis_y2_tick_values,this.owner.y2Axis)},t.getLabelOptionByAxisId=function(t){var e,i=this.owner.config;return"y"===t?e=i.axis_y_label:"y2"===t?e=i.axis_y2_label:"x"===t&&(e=i.axis_x_label),e},t.getLabelText=function(t){var e=this.getLabelOptionByAxisId(t);return d(e)?e:e?e.text:null},t.setLabelText=function(t,e){var i=this.owner.config,n=this.getLabelOptionByAxisId(t);d(n)?"y"===t?i.axis_y_label=e:"y2"===t?i.axis_y2_label=e:"x"===t&&(i.axis_x_label=e):n&&(n.text=e)},t.getLabelPosition=function(t,e){var i=this.getLabelOptionByAxisId(t),a=i&&"object"===(void 0===i?"undefined":n(i))&&i.position?i.position:e;return{isInner:a.indexOf("inner")>=0,isOuter:a.indexOf("outer")>=0,isLeft:a.indexOf("left")>=0,isCenter:a.indexOf("center")>=0,isRight:a.indexOf("right")>=0,isTop:a.indexOf("top")>=0,isMiddle:a.indexOf("middle")>=0,isBottom:a.indexOf("bottom")>=0}},t.getXAxisLabelPosition=function(){return this.getLabelPosition("x",this.owner.config.axis_rotated?"inner-top":"inner-right")},t.getYAxisLabelPosition=function(){return this.getLabelPosition("y",this.owner.config.axis_rotated?"inner-right":"inner-top")},t.getY2AxisLabelPosition=function(){return this.getLabelPosition("y2",this.owner.config.axis_rotated?"inner-right":"inner-top")},t.getLabelPositionById=function(t){return"y2"===t?this.getY2AxisLabelPosition():"y"===t?this.getYAxisLabelPosition():this.getXAxisLabelPosition()},t.textForXAxisLabel=function(){return this.getLabelText("x")},t.textForYAxisLabel=function(){return this.getLabelText("y")},t.textForY2AxisLabel=function(){return this.getLabelText("y2")},t.xForAxisLabel=function(t,e){var i=this.owner;return t?e.isLeft?0:e.isCenter?i.width/2:i.width:e.isBottom?-i.height:e.isMiddle?-i.height/2:0},t.dxForAxisLabel=function(t,e){return t?e.isLeft?"0.5em":e.isRight?"-0.5em":"0":e.isTop?"-0.5em":e.isBottom?"0.5em":"0"},t.textAnchorForAxisLabel=function(t,e){return t?e.isLeft?"start":e.isCenter?"middle":"end":e.isBottom?"start":e.isMiddle?"middle":"end"},t.xForXAxisLabel=function(){return this.xForAxisLabel(!this.owner.config.axis_rotated,this.getXAxisLabelPosition())},t.xForYAxisLabel=function(){return this.xForAxisLabel(this.owner.config.axis_rotated,this.getYAxisLabelPosition())},t.xForY2AxisLabel=function(){return this.xForAxisLabel(this.owner.config.axis_rotated,this.getY2AxisLabelPosition())},t.dxForXAxisLabel=function(){return this.dxForAxisLabel(!this.owner.config.axis_rotated,this.getXAxisLabelPosition())},t.dxForYAxisLabel=function(){return this.dxForAxisLabel(this.owner.config.axis_rotated,this.getYAxisLabelPosition())},t.dxForY2AxisLabel=function(){return this.dxForAxisLabel(this.owner.config.axis_rotated,this.getY2AxisLabelPosition())},t.dyForXAxisLabel=function(){var t=this.owner.config,e=this.getXAxisLabelPosition();return t.axis_rotated?e.isInner?"1.2em":-25-this.getMaxTickWidth("x"):e.isInner?"-0.5em":t.axis_x_height?t.axis_x_height-10:"3em"},t.dyForYAxisLabel=function(){var t=this.owner,e=this.getYAxisLabelPosition();return t.config.axis_rotated?e.isInner?"-0.5em":"3em":e.isInner?"1.2em":-10-(t.config.axis_y_inner?0:this.getMaxTickWidth("y")+10)},t.dyForY2AxisLabel=function(){var t=this.owner,e=this.getY2AxisLabelPosition();return t.config.axis_rotated?e.isInner?"1.2em":"-2.2em":e.isInner?"-0.5em":15+(t.config.axis_y2_inner?0:this.getMaxTickWidth("y2")+15)},t.textAnchorForXAxisLabel=function(){var t=this.owner;return this.textAnchorForAxisLabel(!t.config.axis_rotated,this.getXAxisLabelPosition())},t.textAnchorForYAxisLabel=function(){var t=this.owner;return this.textAnchorForAxisLabel(t.config.axis_rotated,this.getYAxisLabelPosition())},t.textAnchorForY2AxisLabel=function(){var t=this.owner;return this.textAnchorForAxisLabel(t.config.axis_rotated,this.getY2AxisLabelPosition())},t.getMaxTickWidth=function(t,e){var i,n,a,r,s=this.owner,o=s.config,c=0;return e&&s.currentMaxTickWidths[t]?s.currentMaxTickWidths[t]:(s.svg&&(i=s.filterTargetsToShow(s.data.targets),"y"===t?(n=s.y.copy().domain(s.getYDomain(i,"y")),a=this.getYAxis(n,s.yOrient,o.axis_y_tick_format,s.yAxisTickValues,!1,!0,!0)):"y2"===t?(n=s.y2.copy().domain(s.getYDomain(i,"y2")),a=this.getYAxis(n,s.y2Orient,o.axis_y2_tick_format,s.y2AxisTickValues,!1,!0,!0)):(n=s.x.copy().domain(s.getXDomain(i)),a=this.getXAxis(n,s.xOrient,s.xAxisTickFormat,s.xAxisTickValues,!1,!0,!0),this.updateXAxisTickValues(i,a)),(r=s.d3.select("body").append("div").classed("c3",!0)).append("svg").style("visibility","hidden").style("position","fixed").style("top",0).style("left",0).append("g").call(a).each(function(){s.d3.select(this).selectAll("text").each(function(){var t=this.getBoundingClientRect();c<t.width&&(c=t.width)}),r.remove()})),s.currentMaxTickWidths[t]=c<=0?s.currentMaxTickWidths[t]:c,s.currentMaxTickWidths[t])},t.updateLabels=function(t){var e=this.owner,n=e.main.select("."+i.axisX+" ."+i.axisXLabel),a=e.main.select("."+i.axisY+" ."+i.axisYLabel),r=e.main.select("."+i.axisY2+" ."+i.axisY2Label);(t?n.transition():n).attr("x",this.xForXAxisLabel.bind(this)).attr("dx",this.dxForXAxisLabel.bind(this)).attr("dy",this.dyForXAxisLabel.bind(this)).text(this.textForXAxisLabel.bind(this)),(t?a.transition():a).attr("x",this.xForYAxisLabel.bind(this)).attr("dx",this.dxForYAxisLabel.bind(this)).attr("dy",this.dyForYAxisLabel.bind(this)).text(this.textForYAxisLabel.bind(this)),(t?r.transition():r).attr("x",this.xForY2AxisLabel.bind(this)).attr("dx",this.dxForY2AxisLabel.bind(this)).attr("dy",this.dyForY2AxisLabel.bind(this)).text(this.textForY2AxisLabel.bind(this))},t.getPadding=function(t,e,i,n){var a="number"==typeof t?t:t[e];return s(a)?"ratio"===t.unit?t[e]*n:this.convertPixelsToAxisPadding(a,n):i},t.convertPixelsToAxisPadding=function(t,e){var i=this.owner;return e*(t/(i.config.axis_rotated?i.width:i.height))},t.generateTickValues=function(t,e,i){var n,a,r,s,c,d,l,u=t;if(e)if(1===(n=o(e)?e():e))u=[t[0]];else if(2===n)u=[t[0],t[t.length-1]];else if(n>2){for(s=n-2,a=t[0],c=((r=t[t.length-1])-a)/(s+1),u=[a],d=0;d<s;d++)l=+a+c*(d+1),u.push(i?new Date(l):l);u.push(r)}return i||(u=u.sort(function(t,e){return t-e})),u},t.generateTransitions=function(t){var e=this.owner.axes;return{axisX:t?e.x.transition().duration(t):e.x,axisY:t?e.y.transition().duration(t):e.y,axisY2:t?e.y2.transition().duration(t):e.y2,axisSubX:t?e.subx.transition().duration(t):e.subx}},t.redraw=function(t,e){var i=this.owner;i.axes.x.style("opacity",e?0:1),i.axes.y.style("opacity",e?0:1),i.axes.y2.style("opacity",e?0:1),i.axes.subx.style("opacity",e?0:1),t.axisX.call(i.xAxis),t.axisY.call(i.yAxis),t.axisY2.call(i.y2Axis),t.axisSubX.call(i.subXAxis)};var b,T,A={version:"0.4.21"};function P(t,e,i){this.owner=t,A.chart.internal[e]=i}function C(t){var e=this.internal=new L(this);e.loadConfig(t),e.beforeInit(t),e.init(),e.afterInit(t),function t(e,i,n){Object.keys(e).forEach(function(a){i[a]=e[a].bind(n),Object.keys(e[a]).length>0&&t(e[a],i[a],n)})}(b,this,this)}function L(t){var e=this;e.d3=window.d3?window.d3:"undefined"!=typeof require?require("d3"):void 0,e.api=t,e.config=e.getDefaultConfig(),e.data={},e.cache={},e.axes={}}return A.generate=function(t){return new C(t)},A.chart={fn:C.prototype,internal:{fn:L.prototype}},b=A.chart.fn,(T=A.chart.internal.fn).beforeInit=function(){},T.afterInit=function(){},T.init=function(){var t=this,e=t.config;if(t.initParams(),e.data_url)t.convertUrlToData(e.data_url,e.data_mimeType,e.data_headers,e.data_keys,t.initWithData);else if(e.data_json)t.initWithData(t.convertJsonToData(e.data_json,e.data_keys));else if(e.data_rows)t.initWithData(t.convertRowsToData(e.data_rows));else{if(!e.data_columns)throw Error("url or json or rows or columns is required.");t.initWithData(t.convertColumnsToData(e.data_columns))}},T.initParams=function(){var t=this,e=t.d3,i=t.config;t.clipId="c3-"+ +new Date+"-clip",t.clipIdForXAxis=t.clipId+"-xaxis",t.clipIdForYAxis=t.clipId+"-yaxis",t.clipIdForGrid=t.clipId+"-grid",t.clipIdForSubchart=t.clipId+"-subchart",t.clipPath=t.getClipPath(t.clipId),t.clipPathForXAxis=t.getClipPath(t.clipIdForXAxis),t.clipPathForYAxis=t.getClipPath(t.clipIdForYAxis),t.clipPathForGrid=t.getClipPath(t.clipIdForGrid),t.clipPathForSubchart=t.getClipPath(t.clipIdForSubchart),t.dragStart=null,t.dragging=!1,t.flowing=!1,t.cancelClick=!1,t.mouseover=!1,t.transiting=!1,t.color=t.generateColor(),t.levelColor=t.generateLevelColor(),t.dataTimeFormat=i.data_xLocaltime?e.time.format:e.time.format.utc,t.axisTimeFormat=i.axis_x_localtime?e.time.format:e.time.format.utc,t.defaultAxisTimeFormat=t.axisTimeFormat.multi([[".%L",function(t){return t.getMilliseconds()}],[":%S",function(t){return t.getSeconds()}],["%I:%M",function(t){return t.getMinutes()}],["%I %p",function(t){return t.getHours()}],["%-m/%-d",function(t){return t.getDay()&&1!==t.getDate()}],["%-m/%-d",function(t){return 1!==t.getDate()}],["%-m/%-d",function(t){return t.getMonth()}],["%Y/%-m/%-d",function(){return!0}]]),t.hiddenTargetIds=[],t.hiddenLegendIds=[],t.focusedTargetIds=[],t.defocusedTargetIds=[],t.xOrient=i.axis_rotated?"left":"bottom",t.yOrient=i.axis_rotated?i.axis_y_inner?"top":"bottom":i.axis_y_inner?"right":"left",t.y2Orient=i.axis_rotated?i.axis_y2_inner?"bottom":"top":i.axis_y2_inner?"left":"right",t.subXOrient=i.axis_rotated?"left":"bottom",t.isLegendRight="right"===i.legend_position,t.isLegendInset="inset"===i.legend_position,t.isLegendTop="top-left"===i.legend_inset_anchor||"top-right"===i.legend_inset_anchor,t.isLegendLeft="top-left"===i.legend_inset_anchor||"bottom-left"===i.legend_inset_anchor,t.legendStep=0,t.legendItemWidth=0,t.legendItemHeight=0,t.currentMaxTickWidths={x:0,y:0,y2:0},t.rotated_padding_left=30,t.rotated_padding_right=i.axis_rotated&&!i.axis_x_show?0:30,t.rotated_padding_top=5,t.withoutFadeIn={},t.intervalForObserveInserted=void 0,t.axes.subx=e.selectAll([])},T.initChartElements=function(){this.initBar&&this.initBar(),this.initLine&&this.initLine(),this.initArc&&this.initArc(),this.initGauge&&this.initGauge(),this.initText&&this.initText()},T.initWithData=function(t){var e,n,a=this,r=a.d3,s=a.config,o=!0;a.axis=new v(a),a.initPie&&a.initPie(),a.initBrush&&a.initBrush(),a.initZoom&&a.initZoom(),s.bindto?"function"==typeof s.bindto.node?a.selectChart=s.bindto:a.selectChart=r.select(s.bindto):a.selectChart=r.selectAll([]),a.selectChart.empty()&&(a.selectChart=r.select(document.createElement("div")).style("opacity",0),a.observeInserted(a.selectChart),o=!1),a.selectChart.html("").classed("c3",!0),a.data.xs={},a.data.targets=a.convertDataToTargets(t),s.data_filter&&(a.data.targets=a.data.targets.filter(s.data_filter)),s.data_hide&&a.addHiddenTargetIds(!0===s.data_hide?a.mapToIds(a.data.targets):s.data_hide),s.legend_hide&&a.addHiddenLegendIds(!0===s.legend_hide?a.mapToIds(a.data.targets):s.legend_hide),a.updateSizes(),a.updateScales(),a.x.domain(r.extent(a.getXDomain(a.data.targets))),a.y.domain(a.getYDomain(a.data.targets,"y")),a.y2.domain(a.getYDomain(a.data.targets,"y2")),a.subX.domain(a.x.domain()),a.subY.domain(a.y.domain()),a.subY2.domain(a.y2.domain()),a.orgXDomain=a.x.domain(),a.brush&&a.brush.scale(a.subX),s.zoom_enabled&&a.zoom.scale(a.x),a.svg=a.selectChart.append("svg").style("overflow","hidden").on("mouseenter",function(){return s.onmouseover.call(a)}).on("mouseleave",function(){return s.onmouseout.call(a)}),a.config.svg_classname&&a.svg.attr("class",a.config.svg_classname),e=a.svg.append("defs"),a.clipChart=a.appendClip(e,a.clipId),a.clipXAxis=a.appendClip(e,a.clipIdForXAxis),a.clipYAxis=a.appendClip(e,a.clipIdForYAxis),a.clipGrid=a.appendClip(e,a.clipIdForGrid),a.clipSubchart=a.appendClip(e,a.clipIdForSubchart),a.updateSvgSize(),n=a.main=a.svg.append("g").attr("transform",a.getTranslate("main")),a.initSubchart&&a.initSubchart(),a.initTooltip&&a.initTooltip(),a.initLegend&&a.initLegend(),a.initTitle&&a.initTitle(),n.append("text").attr("class",i.text+" "+i.empty).attr("text-anchor","middle").attr("dominant-baseline","middle"),a.initRegion(),a.initGrid(),n.append("g").attr("clip-path",a.clipPath).attr("class",i.chart),s.grid_lines_front&&a.initGridLines(),a.initEventRect(),a.initChartElements(),n.insert("rect",s.zoom_privileged?null:"g."+i.regions).attr("class",i.zoomRect).attr("width",a.width).attr("height",a.height).style("opacity",0).on("dblclick.zoom",null),s.axis_x_extent&&a.brush.extent(a.getDefaultExtent()),a.axis.init(),a.updateTargets(a.data.targets),o&&(a.updateDimension(),a.config.oninit.call(a),a.redraw({withTransition:!1,withTransform:!0,withUpdateXDomain:!0,withUpdateOrgXDomain:!0,withTransitionForAxis:!1})),a.bindResize(),a.api.element=a.selectChart.node()},T.smoothLines=function(t,e){var i=this;"grid"===e&&t.each(function(){var t=i.d3.select(this),e=t.attr("x1"),n=t.attr("x2"),a=t.attr("y1"),r=t.attr("y2");t.attr({x1:Math.ceil(e),x2:Math.ceil(n),y1:Math.ceil(a),y2:Math.ceil(r)})})},T.updateSizes=function(){var t=this,e=t.config,i=t.legend?t.getLegendHeight():0,n=t.legend?t.getLegendWidth():0,a=t.isLegendRight||t.isLegendInset?0:i,r=t.hasArcType(),s=e.axis_rotated||r?0:t.getHorizontalAxisHeight("x"),o=e.subchart_show&&!r?e.subchart_size_height+s:0;t.currentWidth=t.getCurrentWidth(),t.currentHeight=t.getCurrentHeight(),t.margin=e.axis_rotated?{top:t.getHorizontalAxisHeight("y2")+t.getCurrentPaddingTop(),right:r?0:t.getCurrentPaddingRight(),bottom:t.getHorizontalAxisHeight("y")+a+t.getCurrentPaddingBottom(),left:o+(r?0:t.getCurrentPaddingLeft())}:{top:4+t.getCurrentPaddingTop(),right:r?0:t.getCurrentPaddingRight(),bottom:s+o+a+t.getCurrentPaddingBottom(),left:r?0:t.getCurrentPaddingLeft()},t.margin2=e.axis_rotated?{top:t.margin.top,right:NaN,bottom:20+a,left:t.rotated_padding_left}:{top:t.currentHeight-o-a,right:NaN,bottom:s+a,left:t.margin.left},t.margin3={top:0,right:NaN,bottom:0,left:0},t.updateSizeForLegend&&t.updateSizeForLegend(i,n),t.width=t.currentWidth-t.margin.left-t.margin.right,t.height=t.currentHeight-t.margin.top-t.margin.bottom,t.width<0&&(t.width=0),t.height<0&&(t.height=0),t.width2=e.axis_rotated?t.margin.left-t.rotated_padding_left-t.rotated_padding_right:t.width,t.height2=e.axis_rotated?t.height:t.currentHeight-t.margin2.top-t.margin2.bottom,t.width2<0&&(t.width2=0),t.height2<0&&(t.height2=0),t.arcWidth=t.width-(t.isLegendRight?n+10:0),t.arcHeight=t.height-(t.isLegendRight?0:10),t.hasType("gauge")&&!e.gauge_fullCircle&&(t.arcHeight+=t.height-t.getGaugeLabelHeight()),t.updateRadius&&t.updateRadius(),t.isLegendRight&&r&&(t.margin3.left=t.arcWidth/2+1.1*t.radiusExpanded)},T.updateTargets=function(t){var e=this;e.updateTargetsForText(t),e.updateTargetsForBar(t),e.updateTargetsForLine(t),e.hasArcType()&&e.updateTargetsForArc&&e.updateTargetsForArc(t),e.updateTargetsForSubchart&&e.updateTargetsForSubchart(t),e.showTargets()},T.showTargets=function(){var t=this;t.svg.selectAll("."+i.target).filter(function(e){return t.isTargetToShow(e.id)}).transition().duration(t.config.transition_duration).style("opacity",1)},T.redraw=function(t,e){var n,a,r,s,o,c,d,l,u,h,g,f,p,_,y,m,S,w,v,b,T,A,P,C,L,V,G,E,O,I=this,R=I.main,k=I.d3,D=I.config,F=I.getShapeIndices(I.isAreaType),X=I.getShapeIndices(I.isBarType),M=I.getShapeIndices(I.isLineType),z=I.hasArcType(),H=I.filterTargetsToShow(I.data.targets),B=I.xv.bind(I);if(n=x(t=t||{},"withY",!0),a=x(t,"withSubchart",!0),r=x(t,"withTransition",!0),c=x(t,"withTransform",!1),d=x(t,"withUpdateXDomain",!1),l=x(t,"withUpdateOrgXDomain",!1),u=x(t,"withTrimXDomain",!0),p=x(t,"withUpdateXAxis",d),h=x(t,"withLegend",!1),g=x(t,"withEventRect",!0),f=x(t,"withDimension",!0),s=x(t,"withTransitionForExit",r),o=x(t,"withTransitionForAxis",r),v=r?D.transition_duration:0,b=s?v:0,T=o?v:0,e=e||I.axis.generateTransitions(T),h&&D.legend_show?I.updateLegend(I.mapToIds(I.data.targets),t,e):f&&I.updateDimension(!0),I.isCategorized()&&0===H.length&&I.x.domain([0,I.axes.x.selectAll(".tick").size()]),H.length?(I.updateXDomain(H,d,l,u),D.axis_x_tick_values||(C=I.axis.updateXAxisTickValues(H))):(I.xAxis.tickValues([]),I.subXAxis.tickValues([])),D.zoom_rescale&&!t.flow&&(G=I.x.orgDomain()),I.y.domain(I.getYDomain(H,"y",G)),I.y2.domain(I.getYDomain(H,"y2",G)),!D.axis_y_tick_values&&D.axis_y_tick_count&&I.yAxis.tickValues(I.axis.generateTickValues(I.y.domain(),D.axis_y_tick_count)),!D.axis_y2_tick_values&&D.axis_y2_tick_count&&I.y2Axis.tickValues(I.axis.generateTickValues(I.y2.domain(),D.axis_y2_tick_count)),I.axis.redraw(e,z),I.axis.updateLabels(r),(d||p)&&H.length)if(D.axis_x_tick_culling&&C){for(L=1;L<C.length;L++)if(C.length/L<D.axis_x_tick_culling_max){V=L;break}I.svg.selectAll("."+i.axisX+" .tick text").each(function(t){var e=C.indexOf(t);e>=0&&k.select(this).style("display",e%V?"none":"block")})}else I.svg.selectAll("."+i.axisX+" .tick text").style("display","block");_=I.generateDrawArea?I.generateDrawArea(F,!1):void 0,y=I.generateDrawBar?I.generateDrawBar(X):void 0,m=I.generateDrawLine?I.generateDrawLine(M,!1):void 0,S=I.generateXYForText(F,X,M,!0),w=I.generateXYForText(F,X,M,!1),n&&(I.subY.domain(I.getYDomain(H,"y")),I.subY2.domain(I.getYDomain(H,"y2"))),I.updateXgridFocus(),R.select("text."+i.text+"."+i.empty).attr("x",I.width/2).attr("y",I.height/2).text(D.data_empty_label_text).transition().style("opacity",H.length?0:1),I.updateGrid(v),I.updateRegion(v),I.updateBar(b),I.updateLine(b),I.updateArea(b),I.updateCircle(),I.hasDataLabel()&&I.updateText(b),I.redrawTitle&&I.redrawTitle(),I.redrawArc&&I.redrawArc(v,b,c),I.redrawSubchart&&I.redrawSubchart(a,e,v,b,F,X,M),R.selectAll("."+i.selectedCircles).filter(I.isBarType.bind(I)).selectAll("circle").remove(),D.interaction_enabled&&!t.flow&&g&&(I.redrawEventRect(),I.updateZoom&&I.updateZoom()),I.updateCircleY(),E=(I.config.axis_rotated?I.circleY:I.circleX).bind(I),O=(I.config.axis_rotated?I.circleX:I.circleY).bind(I),t.flow&&(P=I.generateFlow({targets:H,flow:t.flow,duration:t.flow.duration,drawBar:y,drawLine:m,drawArea:_,cx:E,cy:O,xv:B,xForText:S,yForText:w})),(v||P)&&I.isTabVisible()?k.transition().duration(v).each(function(){var e=[];[I.redrawBar(y,!0),I.redrawLine(m,!0),I.redrawArea(_,!0),I.redrawCircle(E,O,!0),I.redrawText(S,w,t.flow,!0),I.redrawRegion(!0),I.redrawGrid(!0)].forEach(function(t){t.forEach(function(t){e.push(t)})}),A=I.generateWait(),e.forEach(function(t){A.add(t)})}).call(A,function(){P&&P(),D.onrendered&&D.onrendered.call(I)}):(I.redrawBar(y),I.redrawLine(m),I.redrawArea(_),I.redrawCircle(E,O),I.redrawText(S,w,t.flow),I.redrawRegion(),I.redrawGrid(),D.onrendered&&D.onrendered.call(I)),I.mapToIds(I.data.targets).forEach(function(t){I.withoutFadeIn[t]=!0})},T.updateAndRedraw=function(t){var e,i=this,n=i.config;(t=t||{}).withTransition=x(t,"withTransition",!0),t.withTransform=x(t,"withTransform",!1),t.withLegend=x(t,"withLegend",!1),t.withUpdateXDomain=!0,t.withUpdateOrgXDomain=!0,t.withTransitionForExit=!1,t.withTransitionForTransform=x(t,"withTransitionForTransform",t.withTransition),i.updateSizes(),t.withLegend&&n.legend_show||(e=i.axis.generateTransitions(t.withTransitionForAxis?n.transition_duration:0),i.updateScales(),i.updateSvgSize(),i.transformAll(t.withTransitionForTransform,e)),i.redraw(t,e)},T.redrawWithoutRescale=function(){this.redraw({withY:!1,withSubchart:!1,withEventRect:!1,withTransitionForAxis:!1})},T.isTimeSeries=function(){return"timeseries"===this.config.axis_x_type},T.isCategorized=function(){return this.config.axis_x_type.indexOf("categor")>=0},T.isCustomX=function(){var t=this.config;return!this.isTimeSeries()&&(t.data_x||_(t.data_xs))},T.isTimeSeriesY=function(){return"timeseries"===this.config.axis_y_type},T.getTranslate=function(t){var e,i,n=this,a=n.config;return"main"===t?(e=g(n.margin.left),i=g(n.margin.top)):"context"===t?(e=g(n.margin2.left),i=g(n.margin2.top)):"legend"===t?(e=n.margin3.left,i=n.margin3.top):"x"===t?(e=0,i=a.axis_rotated?0:n.height):"y"===t?(e=0,i=a.axis_rotated?n.height:0):"y2"===t?(e=a.axis_rotated?0:n.width,i=a.axis_rotated?1:0):"subx"===t?(e=0,i=a.axis_rotated?0:n.height2):"arc"===t&&(e=n.arcWidth/2,i=n.arcHeight/2-(n.hasType("gauge")?6:0)),"translate("+e+","+i+")"},T.initialOpacity=function(t){return null!==t.value&&this.withoutFadeIn[t.id]?1:0},T.initialOpacityForCircle=function(t){return null!==t.value&&this.withoutFadeIn[t.id]?this.opacityForCircle(t):0},T.opacityForCircle=function(t){var e=(o(this.config.point_show)?this.config.point_show(t):this.config.point_show)?1:0;return s(t.value)?this.isScatterType(t)?.5:e:0},T.opacityForText=function(){return this.hasDataLabel()?1:0},T.xx=function(t){return t?this.x(t.x):null},T.xv=function(t){var e=this,i=t.value;return e.isTimeSeries()?i=e.parseDate(t.value):e.isCategorized()&&"string"==typeof t.value&&(i=e.config.axis_x_categories.indexOf(t.value)),Math.ceil(e.x(i))},T.yv=function(t){var e=t.axis&&"y2"===t.axis?this.y2:this.y;return Math.ceil(e(t.value))},T.subxx=function(t){return t?this.subX(t.x):null},T.transformMain=function(t,e){var n,a,r,s=this;e&&e.axisX?n=e.axisX:(n=s.main.select("."+i.axisX),t&&(n=n.transition())),e&&e.axisY?a=e.axisY:(a=s.main.select("."+i.axisY),t&&(a=a.transition())),e&&e.axisY2?r=e.axisY2:(r=s.main.select("."+i.axisY2),t&&(r=r.transition())),(t?s.main.transition():s.main).attr("transform",s.getTranslate("main")),n.attr("transform",s.getTranslate("x")),a.attr("transform",s.getTranslate("y")),r.attr("transform",s.getTranslate("y2")),s.main.select("."+i.chartArcs).attr("transform",s.getTranslate("arc"))},T.transformAll=function(t,e){var i=this;i.transformMain(t,e),i.config.subchart_show&&i.transformContext(t,e),i.legend&&i.transformLegend(t)},T.updateSvgSize=function(){var t=this,e=t.svg.select(".c3-brush .background");t.svg.attr("width",t.currentWidth).attr("height",t.currentHeight),t.svg.selectAll(["#"+t.clipId,"#"+t.clipIdForGrid]).select("rect").attr("width",t.width).attr("height",t.height),t.svg.select("#"+t.clipIdForXAxis).select("rect").attr("x",t.getXAxisClipX.bind(t)).attr("y",t.getXAxisClipY.bind(t)).attr("width",t.getXAxisClipWidth.bind(t)).attr("height",t.getXAxisClipHeight.bind(t)),t.svg.select("#"+t.clipIdForYAxis).select("rect").attr("x",t.getYAxisClipX.bind(t)).attr("y",t.getYAxisClipY.bind(t)).attr("width",t.getYAxisClipWidth.bind(t)).attr("height",t.getYAxisClipHeight.bind(t)),t.svg.select("#"+t.clipIdForSubchart).select("rect").attr("width",t.width).attr("height",e.size()?e.attr("height"):0),t.svg.select("."+i.zoomRect).attr("width",t.width).attr("height",t.height),t.selectChart.style("max-height",t.currentHeight+"px")},T.updateDimension=function(t){var e=this;t||(e.config.axis_rotated?(e.axes.x.call(e.xAxis),e.axes.subx.call(e.subXAxis)):(e.axes.y.call(e.yAxis),e.axes.y2.call(e.y2Axis))),e.updateSizes(),e.updateScales(),e.updateSvgSize(),e.transformAll(!1)},T.observeInserted=function(t){var e,i=this;"undefined"!=typeof MutationObserver?(e=new MutationObserver(function(n){n.forEach(function(n){"childList"===n.type&&n.previousSibling&&(e.disconnect(),i.intervalForObserveInserted=window.setInterval(function(){t.node().parentNode&&(window.clearInterval(i.intervalForObserveInserted),i.updateDimension(),i.brush&&i.brush.update(),i.config.oninit.call(i),i.redraw({withTransform:!0,withUpdateXDomain:!0,withUpdateOrgXDomain:!0,withTransition:!1,withTransitionForTransform:!1,withLegend:!0}),t.transition().style("opacity",1))},10))})})).observe(t.node(),{attributes:!0,childList:!0,characterData:!0}):window.console.error("MutationObserver not defined.")},T.bindResize=function(){var t=this,e=t.config;if(t.resizeFunction=t.generateResize(),t.resizeFunction.add(function(){e.onresize.call(t)}),e.resize_auto&&t.resizeFunction.add(function(){void 0!==t.resizeTimeout&&window.clearTimeout(t.resizeTimeout),t.resizeTimeout=window.setTimeout(function(){delete t.resizeTimeout,t.api.flush()},100)}),t.resizeFunction.add(function(){e.onresized.call(t)}),t.resizeIfElementDisplayed=function(){null!=t.api&&t.api.element.offsetParent&&t.resizeFunction()},window.attachEvent)window.attachEvent("onresize",t.resizeIfElementDisplayed);else if(window.addEventListener)window.addEventListener("resize",t.resizeIfElementDisplayed,!1);else{var i=window.onresize;i?i.add&&i.remove||(i=t.generateResize()).add(window.onresize):i=t.generateResize(),i.add(t.resizeFunction),window.onresize=function(){t.api.element.offsetParent&&i()}}},T.generateResize=function(){var t=[];function e(){t.forEach(function(t){t()})}return e.add=function(e){t.push(e)},e.remove=function(e){for(var i=0;i<t.length;i++)if(t[i]===e){t.splice(i,1);break}},e},T.endall=function(t,e){var i=0;t.each(function(){++i}).each("end",function(){--i||e.apply(this,arguments)})},T.generateWait=function(){var t=[],e=function(e,i){var n=setInterval(function(){var e=0;t.forEach(function(t){if(t.empty())e+=1;else try{t.transition()}catch(t){e+=1}}),e===t.length&&(clearInterval(n),i&&i())},10)};return e.add=function(e){t.push(e)},e},T.parseDate=function(t){var e;return t instanceof Date?e=t:"string"==typeof t?e=this.dataTimeFormat(this.config.data_xFormat).parse(t):"object"===(void 0===t?"undefined":n(t))?e=new Date(+t):"number"!=typeof t||isNaN(t)||(e=new Date(+t)),e&&!isNaN(+e)||window.console.error("Failed to parse x '"+t+"' to Date object"),e},T.isTabVisible=function(){var t;return void 0!==document.hidden?t="hidden":void 0!==document.mozHidden?t="mozHidden":void 0!==document.msHidden?t="msHidden":void 0!==document.webkitHidden&&(t="webkitHidden"),!document[t]},T.isValue=s,T.isFunction=o,T.isString=d,T.isUndefined=l,T.isDefined=u,T.ceil10=h,T.asHalfPixel=g,T.diffDomain=f,T.isEmpty=p,T.notEmpty=_,T.notEmpty=_,T.getOption=x,T.hasValue=y,T.sanitise=m,T.getPathBox=S,T.CLASS=i,Function.prototype.bind||(Function.prototype.bind=function(t){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var e=Array.prototype.slice.call(arguments,1),i=this,n=function(){},a=function(){return i.apply(this instanceof n?this:t,e.concat(Array.prototype.slice.call(arguments)))};return n.prototype=this.prototype,a.prototype=new n,a}),"SVGPathSeg"in window||(window.SVGPathSeg=function(t,e,i){this.pathSegType=t,this.pathSegTypeAsLetter=e,this._owningPathSegList=i},window.SVGPathSeg.prototype.classname="SVGPathSeg",window.SVGPathSeg.PATHSEG_UNKNOWN=0,window.SVGPathSeg.PATHSEG_CLOSEPATH=1,window.SVGPathSeg.PATHSEG_MOVETO_ABS=2,window.SVGPathSeg.PATHSEG_MOVETO_REL=3,window.SVGPathSeg.PATHSEG_LINETO_ABS=4,window.SVGPathSeg.PATHSEG_LINETO_REL=5,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS=6,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL=7,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS=8,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL=9,window.SVGPathSeg.PATHSEG_ARC_ABS=10,window.SVGPathSeg.PATHSEG_ARC_REL=11,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS=12,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL=13,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS=14,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL=15,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS=16,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL=17,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS=18,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL=19,window.SVGPathSeg.prototype._segmentChanged=function(){this._owningPathSegList&&this._owningPathSegList.segmentChanged(this)},window.SVGPathSegClosePath=function(t){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CLOSEPATH,"z",t)},window.SVGPathSegClosePath.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegClosePath.prototype.toString=function(){return"[object SVGPathSegClosePath]"},window.SVGPathSegClosePath.prototype._asPathString=function(){return this.pathSegTypeAsLetter},window.SVGPathSegClosePath.prototype.clone=function(){return new window.SVGPathSegClosePath(void 0)},window.SVGPathSegMovetoAbs=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_MOVETO_ABS,"M",t),this._x=e,this._y=i},window.SVGPathSegMovetoAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegMovetoAbs.prototype.toString=function(){return"[object SVGPathSegMovetoAbs]"},window.SVGPathSegMovetoAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegMovetoAbs.prototype.clone=function(){return new window.SVGPathSegMovetoAbs(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegMovetoAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegMovetoAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegMovetoRel=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_MOVETO_REL,"m",t),this._x=e,this._y=i},window.SVGPathSegMovetoRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegMovetoRel.prototype.toString=function(){return"[object SVGPathSegMovetoRel]"},window.SVGPathSegMovetoRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegMovetoRel.prototype.clone=function(){return new window.SVGPathSegMovetoRel(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegMovetoRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegMovetoRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoAbs=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_ABS,"L",t),this._x=e,this._y=i},window.SVGPathSegLinetoAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoAbs.prototype.toString=function(){return"[object SVGPathSegLinetoAbs]"},window.SVGPathSegLinetoAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegLinetoAbs.prototype.clone=function(){return new window.SVGPathSegLinetoAbs(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegLinetoAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegLinetoAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoRel=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_REL,"l",t),this._x=e,this._y=i},window.SVGPathSegLinetoRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoRel.prototype.toString=function(){return"[object SVGPathSegLinetoRel]"},window.SVGPathSegLinetoRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegLinetoRel.prototype.clone=function(){return new window.SVGPathSegLinetoRel(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegLinetoRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegLinetoRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicAbs=function(t,e,i,n,a,r,s){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS,"C",t),this._x=e,this._y=i,this._x1=n,this._y1=a,this._x2=r,this._y2=s},window.SVGPathSegCurvetoCubicAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicAbs]"},window.SVGPathSegCurvetoCubicAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicAbs(void 0,this._x,this._y,this._x1,this._y1,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicRel=function(t,e,i,n,a,r,s){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL,"c",t),this._x=e,this._y=i,this._x1=n,this._y1=a,this._x2=r,this._y2=s},window.SVGPathSegCurvetoCubicRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicRel.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicRel]"},window.SVGPathSegCurvetoCubicRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicRel.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicRel(void 0,this._x,this._y,this._x1,this._y1,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticAbs=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS,"Q",t),this._x=e,this._y=i,this._x1=n,this._y1=a},window.SVGPathSegCurvetoQuadraticAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticAbs]"},window.SVGPathSegCurvetoQuadraticAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticAbs(void 0,this._x,this._y,this._x1,this._y1)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticRel=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL,"q",t),this._x=e,this._y=i,this._x1=n,this._y1=a},window.SVGPathSegCurvetoQuadraticRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticRel.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticRel]"},window.SVGPathSegCurvetoQuadraticRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticRel.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticRel(void 0,this._x,this._y,this._x1,this._y1)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegArcAbs=function(t,e,i,n,a,r,s,o){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_ARC_ABS,"A",t),this._x=e,this._y=i,this._r1=n,this._r2=a,this._angle=r,this._largeArcFlag=s,this._sweepFlag=o},window.SVGPathSegArcAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegArcAbs.prototype.toString=function(){return"[object SVGPathSegArcAbs]"},window.SVGPathSegArcAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._r1+" "+this._r2+" "+this._angle+" "+(this._largeArcFlag?"1":"0")+" "+(this._sweepFlag?"1":"0")+" "+this._x+" "+this._y},window.SVGPathSegArcAbs.prototype.clone=function(){return new window.SVGPathSegArcAbs(void 0,this._x,this._y,this._r1,this._r2,this._angle,this._largeArcFlag,this._sweepFlag)},Object.defineProperty(window.SVGPathSegArcAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"r1",{get:function(){return this._r1},set:function(t){this._r1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"r2",{get:function(){return this._r2},set:function(t){this._r2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"angle",{get:function(){return this._angle},set:function(t){this._angle=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"largeArcFlag",{get:function(){return this._largeArcFlag},set:function(t){this._largeArcFlag=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"sweepFlag",{get:function(){return this._sweepFlag},set:function(t){this._sweepFlag=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegArcRel=function(t,e,i,n,a,r,s,o){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_ARC_REL,"a",t),this._x=e,this._y=i,this._r1=n,this._r2=a,this._angle=r,this._largeArcFlag=s,this._sweepFlag=o},window.SVGPathSegArcRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegArcRel.prototype.toString=function(){return"[object SVGPathSegArcRel]"},window.SVGPathSegArcRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._r1+" "+this._r2+" "+this._angle+" "+(this._largeArcFlag?"1":"0")+" "+(this._sweepFlag?"1":"0")+" "+this._x+" "+this._y},window.SVGPathSegArcRel.prototype.clone=function(){return new window.SVGPathSegArcRel(void 0,this._x,this._y,this._r1,this._r2,this._angle,this._largeArcFlag,this._sweepFlag)},Object.defineProperty(window.SVGPathSegArcRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"r1",{get:function(){return this._r1},set:function(t){this._r1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"r2",{get:function(){return this._r2},set:function(t){this._r2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"angle",{get:function(){return this._angle},set:function(t){this._angle=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"largeArcFlag",{get:function(){return this._largeArcFlag},set:function(t){this._largeArcFlag=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"sweepFlag",{get:function(){return this._sweepFlag},set:function(t){this._sweepFlag=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoHorizontalAbs=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS,"H",t),this._x=e},window.SVGPathSegLinetoHorizontalAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoHorizontalAbs.prototype.toString=function(){return"[object SVGPathSegLinetoHorizontalAbs]"},window.SVGPathSegLinetoHorizontalAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x},window.SVGPathSegLinetoHorizontalAbs.prototype.clone=function(){return new window.SVGPathSegLinetoHorizontalAbs(void 0,this._x)},Object.defineProperty(window.SVGPathSegLinetoHorizontalAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoHorizontalRel=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL,"h",t),this._x=e},window.SVGPathSegLinetoHorizontalRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoHorizontalRel.prototype.toString=function(){return"[object SVGPathSegLinetoHorizontalRel]"},window.SVGPathSegLinetoHorizontalRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x},window.SVGPathSegLinetoHorizontalRel.prototype.clone=function(){return new window.SVGPathSegLinetoHorizontalRel(void 0,this._x)},Object.defineProperty(window.SVGPathSegLinetoHorizontalRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoVerticalAbs=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS,"V",t),this._y=e},window.SVGPathSegLinetoVerticalAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoVerticalAbs.prototype.toString=function(){return"[object SVGPathSegLinetoVerticalAbs]"},window.SVGPathSegLinetoVerticalAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._y},window.SVGPathSegLinetoVerticalAbs.prototype.clone=function(){return new window.SVGPathSegLinetoVerticalAbs(void 0,this._y)},Object.defineProperty(window.SVGPathSegLinetoVerticalAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoVerticalRel=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL,"v",t),this._y=e},window.SVGPathSegLinetoVerticalRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoVerticalRel.prototype.toString=function(){return"[object SVGPathSegLinetoVerticalRel]"},window.SVGPathSegLinetoVerticalRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._y},window.SVGPathSegLinetoVerticalRel.prototype.clone=function(){return new window.SVGPathSegLinetoVerticalRel(void 0,this._y)},Object.defineProperty(window.SVGPathSegLinetoVerticalRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicSmoothAbs=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS,"S",t),this._x=e,this._y=i,this._x2=n,this._y2=a},window.SVGPathSegCurvetoCubicSmoothAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicSmoothAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicSmoothAbs]"},window.SVGPathSegCurvetoCubicSmoothAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicSmoothAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicSmoothAbs(void 0,this._x,this._y,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicSmoothRel=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL,"s",t),this._x=e,this._y=i,this._x2=n,this._y2=a},window.SVGPathSegCurvetoCubicSmoothRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicSmoothRel.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicSmoothRel]"},window.SVGPathSegCurvetoCubicSmoothRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicSmoothRel.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicSmoothRel(void 0,this._x,this._y,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticSmoothAbs=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS,"T",t),this._x=e,this._y=i},window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticSmoothAbs]"},window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticSmoothAbs(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticSmoothRel=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL,"t",t),this._x=e,this._y=i},window.SVGPathSegCurvetoQuadraticSmoothRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticSmoothRel]"},window.SVGPathSegCurvetoQuadraticSmoothRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticSmoothRel(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathElement.prototype.createSVGPathSegClosePath=function(){return new window.SVGPathSegClosePath(void 0)},window.SVGPathElement.prototype.createSVGPathSegMovetoAbs=function(t,e){return new window.SVGPathSegMovetoAbs(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegMovetoRel=function(t,e){return new window.SVGPathSegMovetoRel(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegLinetoAbs=function(t,e){return new window.SVGPathSegLinetoAbs(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegLinetoRel=function(t,e){return new window.SVGPathSegLinetoRel(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicAbs=function(t,e,i,n,a,r){return new window.SVGPathSegCurvetoCubicAbs(void 0,t,e,i,n,a,r)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicRel=function(t,e,i,n,a,r){return new window.SVGPathSegCurvetoCubicRel(void 0,t,e,i,n,a,r)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticAbs=function(t,e,i,n){return new window.SVGPathSegCurvetoQuadraticAbs(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticRel=function(t,e,i,n){return new window.SVGPathSegCurvetoQuadraticRel(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegArcAbs=function(t,e,i,n,a,r,s){return new window.SVGPathSegArcAbs(void 0,t,e,i,n,a,r,s)},window.SVGPathElement.prototype.createSVGPathSegArcRel=function(t,e,i,n,a,r,s){return new window.SVGPathSegArcRel(void 0,t,e,i,n,a,r,s)},window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalAbs=function(t){return new window.SVGPathSegLinetoHorizontalAbs(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalRel=function(t){return new window.SVGPathSegLinetoHorizontalRel(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalAbs=function(t){return new window.SVGPathSegLinetoVerticalAbs(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalRel=function(t){return new window.SVGPathSegLinetoVerticalRel(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothAbs=function(t,e,i,n){return new window.SVGPathSegCurvetoCubicSmoothAbs(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothRel=function(t,e,i,n){return new window.SVGPathSegCurvetoCubicSmoothRel(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothAbs=function(t,e){return new window.SVGPathSegCurvetoQuadraticSmoothAbs(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothRel=function(t,e){return new window.SVGPathSegCurvetoQuadraticSmoothRel(void 0,t,e)},"getPathSegAtLength"in window.SVGPathElement.prototype||(window.SVGPathElement.prototype.getPathSegAtLength=function(t){if(void 0===t||!isFinite(t))throw"Invalid arguments.";var e=document.createElementNS("http://www.w3.org/2000/svg","path");e.setAttribute("d",this.getAttribute("d"));var i=e.pathSegList.numberOfItems-1;if(i<=0)return 0;do{if(e.pathSegList.removeItem(i),t>e.getTotalLength())break;i--}while(i>0);return i})),"SVGPathSegList"in window||(window.SVGPathSegList=function(t){this._pathElement=t,this._list=this._parsePath(this._pathElement.getAttribute("d")),this._mutationObserverConfig={attributes:!0,attributeFilter:["d"]},this._pathElementMutationObserver=new MutationObserver(this._updateListFromPathMutations.bind(this)),this._pathElementMutationObserver.observe(this._pathElement,this._mutationObserverConfig)},window.SVGPathSegList.prototype.classname="SVGPathSegList",Object.defineProperty(window.SVGPathSegList.prototype,"numberOfItems",{get:function(){return this._checkPathSynchronizedToList(),this._list.length},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"pathSegList",{get:function(){return this._pathSegList||(this._pathSegList=new window.SVGPathSegList(this)),this._pathSegList},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"normalizedPathSegList",{get:function(){return this.pathSegList},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"animatedPathSegList",{get:function(){return this.pathSegList},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"animatedNormalizedPathSegList",{get:function(){return this.pathSegList},enumerable:!0}),window.SVGPathSegList.prototype._checkPathSynchronizedToList=function(){this._updateListFromPathMutations(this._pathElementMutationObserver.takeRecords())},window.SVGPathSegList.prototype._updateListFromPathMutations=function(t){if(this._pathElement){var e=!1;t.forEach(function(t){"d"==t.attributeName&&(e=!0)}),e&&(this._list=this._parsePath(this._pathElement.getAttribute("d")))}},window.SVGPathSegList.prototype._writeListToPath=function(){this._pathElementMutationObserver.disconnect(),this._pathElement.setAttribute("d",window.SVGPathSegList._pathSegArrayAsString(this._list)),this._pathElementMutationObserver.observe(this._pathElement,this._mutationObserverConfig)},window.SVGPathSegList.prototype.segmentChanged=function(t){this._writeListToPath()},window.SVGPathSegList.prototype.clear=function(){this._checkPathSynchronizedToList(),this._list.forEach(function(t){t._owningPathSegList=null}),this._list=[],this._writeListToPath()},window.SVGPathSegList.prototype.initialize=function(t){return this._checkPathSynchronizedToList(),this._list=[t],t._owningPathSegList=this,this._writeListToPath(),t},window.SVGPathSegList.prototype._checkValidIndex=function(t){if(isNaN(t)||t<0||t>=this.numberOfItems)throw"INDEX_SIZE_ERR"},window.SVGPathSegList.prototype.getItem=function(t){return this._checkPathSynchronizedToList(),this._checkValidIndex(t),this._list[t]},window.SVGPathSegList.prototype.insertItemBefore=function(t,e){return this._checkPathSynchronizedToList(),e>this.numberOfItems&&(e=this.numberOfItems),t._owningPathSegList&&(t=t.clone()),this._list.splice(e,0,t),t._owningPathSegList=this,this._writeListToPath(),t},window.SVGPathSegList.prototype.replaceItem=function(t,e){return this._checkPathSynchronizedToList(),t._owningPathSegList&&(t=t.clone()),this._checkValidIndex(e),this._list[e]=t,t._owningPathSegList=this,this._writeListToPath(),t},window.SVGPathSegList.prototype.removeItem=function(t){this._checkPathSynchronizedToList(),this._checkValidIndex(t);var e=this._list[t];return this._list.splice(t,1),this._writeListToPath(),e},window.SVGPathSegList.prototype.appendItem=function(t){return this._checkPathSynchronizedToList(),t._owningPathSegList&&(t=t.clone()),this._list.push(t),t._owningPathSegList=this,this._writeListToPath(),t},window.SVGPathSegList._pathSegArrayAsString=function(t){var e="",i=!0;return t.forEach(function(t){i?(i=!1,e+=t._asPathString()):e+=" "+t._asPathString()}),e},window.SVGPathSegList.prototype._parsePath=function(t){if(!t||0==t.length)return[];var e=this,i=function(){this.pathSegList=[]};i.prototype.appendSegment=function(t){this.pathSegList.push(t)};var n=function(t){this._string=t,this._currentIndex=0,this._endIndex=this._string.length,this._previousCommand=window.SVGPathSeg.PATHSEG_UNKNOWN,this._skipOptionalSpaces()};n.prototype._isCurrentSpace=function(){var t=this._string[this._currentIndex];return t<=" "&&(" "==t||"\n"==t||"\t"==t||"\r"==t||"\f"==t)},n.prototype._skipOptionalSpaces=function(){for(;this._currentIndex<this._endIndex&&this._isCurrentSpace();)this._currentIndex++;return this._currentIndex<this._endIndex},n.prototype._skipOptionalSpacesOrDelimiter=function(){return!(this._currentIndex<this._endIndex&&!this._isCurrentSpace()&&","!=this._string.charAt(this._currentIndex))&&(this._skipOptionalSpaces()&&this._currentIndex<this._endIndex&&","==this._string.charAt(this._currentIndex)&&(this._currentIndex++,this._skipOptionalSpaces()),this._currentIndex<this._endIndex)},n.prototype.hasMoreData=function(){return this._currentIndex<this._endIndex},n.prototype.peekSegmentType=function(){var t=this._string[this._currentIndex];return this._pathSegTypeFromChar(t)},n.prototype._pathSegTypeFromChar=function(t){switch(t){case"Z":case"z":return window.SVGPathSeg.PATHSEG_CLOSEPATH;case"M":return window.SVGPathSeg.PATHSEG_MOVETO_ABS;case"m":return window.SVGPathSeg.PATHSEG_MOVETO_REL;case"L":return window.SVGPathSeg.PATHSEG_LINETO_ABS;case"l":return window.SVGPathSeg.PATHSEG_LINETO_REL;case"C":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS;case"c":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL;case"Q":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS;case"q":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL;case"A":return window.SVGPathSeg.PATHSEG_ARC_ABS;case"a":return window.SVGPathSeg.PATHSEG_ARC_REL;case"H":return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS;case"h":return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL;case"V":return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS;case"v":return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL;case"S":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS;case"s":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL;case"T":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS;case"t":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL;default:return window.SVGPathSeg.PATHSEG_UNKNOWN}},n.prototype._nextCommandHelper=function(t,e){return("+"==t||"-"==t||"."==t||t>="0"&&t<="9")&&e!=window.SVGPathSeg.PATHSEG_CLOSEPATH?e==window.SVGPathSeg.PATHSEG_MOVETO_ABS?window.SVGPathSeg.PATHSEG_LINETO_ABS:e==window.SVGPathSeg.PATHSEG_MOVETO_REL?window.SVGPathSeg.PATHSEG_LINETO_REL:e:window.SVGPathSeg.PATHSEG_UNKNOWN},n.prototype.initialCommandIsMoveTo=function(){if(!this.hasMoreData())return!0;var t=this.peekSegmentType();return t==window.SVGPathSeg.PATHSEG_MOVETO_ABS||t==window.SVGPathSeg.PATHSEG_MOVETO_REL},n.prototype._parseNumber=function(){var t=0,e=0,i=1,n=0,a=1,r=1,s=this._currentIndex;if(this._skipOptionalSpaces(),this._currentIndex<this._endIndex&&"+"==this._string.charAt(this._currentIndex)?this._currentIndex++:this._currentIndex<this._endIndex&&"-"==this._string.charAt(this._currentIndex)&&(this._currentIndex++,a=-1),!(this._currentIndex==this._endIndex||(this._string.charAt(this._currentIndex)<"0"||this._string.charAt(this._currentIndex)>"9")&&"."!=this._string.charAt(this._currentIndex))){for(var o=this._currentIndex;this._currentIndex<this._endIndex&&this._string.charAt(this._currentIndex)>="0"&&this._string.charAt(this._currentIndex)<="9";)this._currentIndex++;if(this._currentIndex!=o)for(var c=this._currentIndex-1,d=1;c>=o;)e+=d*(this._string.charAt(c--)-"0"),d*=10;if(this._currentIndex<this._endIndex&&"."==this._string.charAt(this._currentIndex)){if(this._currentIndex++,this._currentIndex>=this._endIndex||this._string.charAt(this._currentIndex)<"0"||this._string.charAt(this._currentIndex)>"9")return;for(;this._currentIndex<this._endIndex&&this._string.charAt(this._currentIndex)>="0"&&this._string.charAt(this._currentIndex)<="9";)i*=10,n+=(this._string.charAt(this._currentIndex)-"0")/i,this._currentIndex+=1}if(this._currentIndex!=s&&this._currentIndex+1<this._endIndex&&("e"==this._string.charAt(this._currentIndex)||"E"==this._string.charAt(this._currentIndex))&&"x"!=this._string.charAt(this._currentIndex+1)&&"m"!=this._string.charAt(this._currentIndex+1)){if(this._currentIndex++,"+"==this._string.charAt(this._currentIndex)?this._currentIndex++:"-"==this._string.charAt(this._currentIndex)&&(this._currentIndex++,r=-1),this._currentIndex>=this._endIndex||this._string.charAt(this._currentIndex)<"0"||this._string.charAt(this._currentIndex)>"9")return;for(;this._currentIndex<this._endIndex&&this._string.charAt(this._currentIndex)>="0"&&this._string.charAt(this._currentIndex)<="9";)t*=10,t+=this._string.charAt(this._currentIndex)-"0",this._currentIndex++}var l=e+n;if(l*=a,t&&(l*=Math.pow(10,r*t)),s!=this._currentIndex)return this._skipOptionalSpacesOrDelimiter(),l}},n.prototype._parseArcFlag=function(){if(!(this._currentIndex>=this._endIndex)){var t=!1,e=this._string.charAt(this._currentIndex++);if("0"==e)t=!1;else{if("1"!=e)return;t=!0}return this._skipOptionalSpacesOrDelimiter(),t}},n.prototype.parseSegment=function(){var t=this._string[this._currentIndex],i=this._pathSegTypeFromChar(t);if(i==window.SVGPathSeg.PATHSEG_UNKNOWN){if(this._previousCommand==window.SVGPathSeg.PATHSEG_UNKNOWN)return null;if((i=this._nextCommandHelper(t,this._previousCommand))==window.SVGPathSeg.PATHSEG_UNKNOWN)return null}else this._currentIndex++;switch(this._previousCommand=i,i){case window.SVGPathSeg.PATHSEG_MOVETO_REL:return new window.SVGPathSegMovetoRel(e,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_MOVETO_ABS:return new window.SVGPathSegMovetoAbs(e,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_REL:return new window.SVGPathSegLinetoRel(e,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_ABS:return new window.SVGPathSegLinetoAbs(e,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL:return new window.SVGPathSegLinetoHorizontalRel(e,this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS:return new window.SVGPathSegLinetoHorizontalAbs(e,this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL:return new window.SVGPathSegLinetoVerticalRel(e,this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS:return new window.SVGPathSegLinetoVerticalAbs(e,this._parseNumber());case window.SVGPathSeg.PATHSEG_CLOSEPATH:return this._skipOptionalSpaces(),new window.SVGPathSegClosePath(e);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL:var n={x1:this._parseNumber(),y1:this._parseNumber(),x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()};return new window.SVGPathSegCurvetoCubicRel(e,n.x,n.y,n.x1,n.y1,n.x2,n.y2);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS:return n={x1:this._parseNumber(),y1:this._parseNumber(),x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoCubicAbs(e,n.x,n.y,n.x1,n.y1,n.x2,n.y2);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL:return n={x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoCubicSmoothRel(e,n.x,n.y,n.x2,n.y2);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS:return n={x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoCubicSmoothAbs(e,n.x,n.y,n.x2,n.y2);case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL:return n={x1:this._parseNumber(),y1:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoQuadraticRel(e,n.x,n.y,n.x1,n.y1);case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS:return n={x1:this._parseNumber(),y1:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoQuadraticAbs(e,n.x,n.y,n.x1,n.y1);case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL:return new window.SVGPathSegCurvetoQuadraticSmoothRel(e,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS:return new window.SVGPathSegCurvetoQuadraticSmoothAbs(e,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_ARC_REL:return n={x1:this._parseNumber(),y1:this._parseNumber(),arcAngle:this._parseNumber(),arcLarge:this._parseArcFlag(),arcSweep:this._parseArcFlag(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegArcRel(e,n.x,n.y,n.x1,n.y1,n.arcAngle,n.arcLarge,n.arcSweep);case window.SVGPathSeg.PATHSEG_ARC_ABS:return n={x1:this._parseNumber(),y1:this._parseNumber(),arcAngle:this._parseNumber(),arcLarge:this._parseArcFlag(),arcSweep:this._parseArcFlag(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegArcAbs(e,n.x,n.y,n.x1,n.y1,n.arcAngle,n.arcLarge,n.arcSweep);default:throw"Unknown path seg type."}};var a=new i,r=new n(t);if(!r.initialCommandIsMoveTo())return[];for(;r.hasMoreData();){var s=r.parseSegment();if(!s)return[];a.appendSegment(s)}return a.pathSegList}),b.axis=function(){},b.axis.labels=function(t){var e=this.internal;arguments.length&&(Object.keys(t).forEach(function(i){e.axis.setLabelText(i,t[i])}),e.axis.updateLabels())},b.axis.max=function(t){var e=this.internal,i=e.config;if(!arguments.length)return{x:i.axis_x_max,y:i.axis_y_max,y2:i.axis_y2_max};"object"===(void 0===t?"undefined":n(t))?(s(t.x)&&(i.axis_x_max=t.x),s(t.y)&&(i.axis_y_max=t.y),s(t.y2)&&(i.axis_y2_max=t.y2)):i.axis_y_max=i.axis_y2_max=t,e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})},b.axis.min=function(t){var e=this.internal,i=e.config;if(!arguments.length)return{x:i.axis_x_min,y:i.axis_y_min,y2:i.axis_y2_min};"object"===(void 0===t?"undefined":n(t))?(s(t.x)&&(i.axis_x_min=t.x),s(t.y)&&(i.axis_y_min=t.y),s(t.y2)&&(i.axis_y2_min=t.y2)):i.axis_y_min=i.axis_y2_min=t,e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})},b.axis.range=function(t){if(!arguments.length)return{max:this.axis.max(),min:this.axis.min()};u(t.max)&&this.axis.max(t.max),u(t.min)&&this.axis.min(t.min)},b.category=function(t,e){var i=this.internal,n=i.config;return arguments.length>1&&(n.axis_x_categories[t]=e,i.redraw()),n.axis_x_categories[t]},b.categories=function(t){var e=this.internal,i=e.config;return arguments.length?(i.axis_x_categories=t,e.redraw(),i.axis_x_categories):i.axis_x_categories},b.resize=function(t){var e=this.internal.config;e.size_width=t?t.width:null,e.size_height=t?t.height:null,this.flush()},b.flush=function(){this.internal.updateAndRedraw({withLegend:!0,withTransition:!1,withTransitionForTransform:!1})},b.destroy=function(){var t=this.internal;if(window.clearInterval(t.intervalForObserveInserted),void 0!==t.resizeTimeout&&window.clearTimeout(t.resizeTimeout),window.detachEvent)window.detachEvent("onresize",t.resizeIfElementDisplayed);else if(window.removeEventListener)window.removeEventListener("resize",t.resizeIfElementDisplayed);else{var e=window.onresize;e&&e.add&&e.remove&&e.remove(t.resizeFunction)}return t.resizeFunction.remove(),t.selectChart.classed("c3",!1).html(""),Object.keys(t).forEach(function(e){t[e]=null}),null},b.color=function(t){return this.internal.color(t)},b.data=function(t){var e=this.internal.data.targets;return void 0===t?e:e.filter(function(e){return[].concat(t).indexOf(e.id)>=0})},b.data.shown=function(t){return this.internal.filterTargetsToShow(this.data(t))},b.data.values=function(t){var e,i=null;return t&&(i=(e=this.data(t))[0]?e[0].values.map(function(t){return t.value}):null),i},b.data.names=function(t){return this.internal.clearLegendItemTextBoxCache(),this.internal.updateDataAttributes("names",t)},b.data.colors=function(t){return this.internal.updateDataAttributes("colors",t)},b.data.axes=function(t){return this.internal.updateDataAttributes("axes",t)},b.flow=function(t){var e,i,n,a,r,o,c,d=this.internal,l=[],h=d.getMaxDataCount(),g=0,f=0;if(t.json)i=d.convertJsonToData(t.json,t.keys);else if(t.rows)i=d.convertRowsToData(t.rows);else{if(!t.columns)return;i=d.convertColumnsToData(t.columns)}e=d.convertDataToTargets(i,!0),d.data.targets.forEach(function(t){var i,n,a=!1;for(i=0;i<e.length;i++)if(t.id===e[i].id){for(a=!0,t.values[t.values.length-1]&&(f=t.values[t.values.length-1].index+1),g=e[i].values.length,n=0;n<g;n++)e[i].values[n].index=f+n,d.isTimeSeries()||(e[i].values[n].x=f+n);t.values=t.values.concat(e[i].values),e.splice(i,1);break}a||l.push(t.id)}),d.data.targets.forEach(function(t){var e,i;for(e=0;e<l.length;e++)if(t.id===l[e])for(f=t.values[t.values.length-1].index+1,i=0;i<g;i++)t.values.push({id:t.id,index:f+i,x:d.isTimeSeries()?d.getOtherTargetX(f+i):f+i,value:null})}),d.data.targets.length&&e.forEach(function(t){var e,i=[];for(e=d.data.targets[0].values[0].index;e<f;e++)i.push({id:t.id,index:e,x:d.isTimeSeries()?d.getOtherTargetX(e):e,value:null});t.values.forEach(function(t){t.index+=f,d.isTimeSeries()||(t.x+=f)}),t.values=i.concat(t.values)}),d.data.targets=d.data.targets.concat(e),d.getMaxDataCount(),r=(a=d.data.targets[0]).values[0],u(t.to)?(g=0,c=d.isTimeSeries()?d.parseDate(t.to):t.to,a.values.forEach(function(t){t.x<c&&g++})):u(t.length)&&(g=t.length),h?1===h&&d.isTimeSeries()&&(o=(a.values[a.values.length-1].x-r.x)/2,n=[new Date(+r.x-o),new Date(+r.x+o)],d.updateXDomain(null,!0,!0,!1,n)):(o=d.isTimeSeries()?a.values.length>1?a.values[a.values.length-1].x-r.x:r.x-d.getXDomain(d.data.targets)[0]:1,n=[r.x-o,r.x],d.updateXDomain(null,!0,!0,!1,n)),d.updateTargets(d.data.targets),d.redraw({flow:{index:r.index,length:g,duration:s(t.duration)?t.duration:d.config.transition_duration,done:t.done,orgDataCount:h},withLegend:!0,withTransition:h>1,withTrimXDomain:!1,withUpdateXAxis:!0})},T.generateFlow=function(t){var e=this,n=e.config,a=e.d3;return function(){var r,s,o,c,d=t.targets,l=t.flow,u=t.drawBar,h=t.drawLine,g=t.drawArea,p=t.cx,_=t.cy,x=t.xv,y=t.xForText,m=t.yForText,S=t.duration,w=l.index,v=l.length,b=e.getValueOnIndex(e.data.targets[0].values,w),T=e.getValueOnIndex(e.data.targets[0].values,w+v),A=e.x.domain(),P=l.duration||S,C=l.done||function(){},L=e.generateWait(),V=e.xgrid||a.selectAll([]),G=e.xgridLines||a.selectAll([]),E=e.mainRegion||a.selectAll([]),O=e.mainText||a.selectAll([]),I=e.mainBar||a.selectAll([]),R=e.mainLine||a.selectAll([]),k=e.mainArea||a.selectAll([]),D=e.mainCircle||a.selectAll([]);e.flowing=!0,e.data.targets.forEach(function(t){t.values.splice(0,v)}),c=e.updateXDomain(d,!0,!0),e.updateXGrid&&e.updateXGrid(!0),l.orgDataCount?r=1===l.orgDataCount||(b&&b.x)===(T&&T.x)?e.x(A[0])-e.x(c[0]):e.isTimeSeries()?e.x(A[0])-e.x(c[0]):e.x(b.x)-e.x(T.x):1!==e.data.targets[0].values.length?r=e.x(A[0])-e.x(c[0]):e.isTimeSeries()?(b=e.getValueOnIndex(e.data.targets[0].values,0),T=e.getValueOnIndex(e.data.targets[0].values,e.data.targets[0].values.length-1),r=e.x(b.x)-e.x(T.x)):r=f(c)/2,s=f(A)/f(c),o="translate("+r+",0) scale("+s+",1)",e.hideXGridFocus(),a.transition().ease("linear").duration(P).each(function(){L.add(e.axes.x.transition().call(e.xAxis)),L.add(I.transition().attr("transform",o)),L.add(R.transition().attr("transform",o)),L.add(k.transition().attr("transform",o)),L.add(D.transition().attr("transform",o)),L.add(O.transition().attr("transform",o)),L.add(E.filter(e.isRegionOnX).transition().attr("transform",o)),L.add(V.transition().attr("transform",o)),L.add(G.transition().attr("transform",o))}).call(L,function(){var t,a=[],r=[],s=[];if(v){for(t=0;t<v;t++)a.push("."+i.shape+"-"+(w+t)),r.push("."+i.text+"-"+(w+t)),s.push("."+i.eventRect+"-"+(w+t));e.svg.selectAll("."+i.shapes).selectAll(a).remove(),e.svg.selectAll("."+i.texts).selectAll(r).remove(),e.svg.selectAll("."+i.eventRects).selectAll(s).remove(),e.svg.select("."+i.xgrid).remove()}V.attr("transform",null).attr(e.xgridAttr),G.attr("transform",null),G.select("line").attr("x1",n.axis_rotated?0:x).attr("x2",n.axis_rotated?e.width:x),G.select("text").attr("x",n.axis_rotated?e.width:0).attr("y",x),I.attr("transform",null).attr("d",u),R.attr("transform",null).attr("d",h),k.attr("transform",null).attr("d",g),D.attr("transform",null).attr("cx",p).attr("cy",_),O.attr("transform",null).attr("x",y).attr("y",m).style("fill-opacity",e.opacityForText.bind(e)),E.attr("transform",null),E.select("rect").filter(e.isRegionOnX).attr("x",e.regionX.bind(e)).attr("width",e.regionWidth.bind(e)),n.interaction_enabled&&e.redrawEventRect(),C(),e.flowing=!1})}},b.focus=function(t){var e,n=this.internal;t=n.mapToTargetIds(t),e=n.svg.selectAll(n.selectorTargets(t.filter(n.isTargetToShow,n))),this.revert(),this.defocus(),e.classed(i.focused,!0).classed(i.defocused,!1),n.hasArcType()&&n.expandArc(t),n.toggleFocusLegend(t,!0),n.focusedTargetIds=t,n.defocusedTargetIds=n.defocusedTargetIds.filter(function(e){return t.indexOf(e)<0})},b.defocus=function(t){var e=this.internal;t=e.mapToTargetIds(t),e.svg.selectAll(e.selectorTargets(t.filter(e.isTargetToShow,e))).classed(i.focused,!1).classed(i.defocused,!0),e.hasArcType()&&e.unexpandArc(t),e.toggleFocusLegend(t,!1),e.focusedTargetIds=e.focusedTargetIds.filter(function(e){return t.indexOf(e)<0}),e.defocusedTargetIds=t},b.revert=function(t){var e=this.internal;t=e.mapToTargetIds(t),e.svg.selectAll(e.selectorTargets(t)).classed(i.focused,!1).classed(i.defocused,!1),e.hasArcType()&&e.unexpandArc(t),e.config.legend_show&&(e.showLegend(t.filter(e.isLegendToShow.bind(e))),e.legend.selectAll(e.selectorLegends(t)).filter(function(){return e.d3.select(this).classed(i.legendItemFocused)}).classed(i.legendItemFocused,!1)),e.focusedTargetIds=[],e.defocusedTargetIds=[]},b.xgrids=function(t){var e=this.internal,i=e.config;return t?(i.grid_x_lines=t,e.redrawWithoutRescale(),i.grid_x_lines):i.grid_x_lines},b.xgrids.add=function(t){var e=this.internal;return this.xgrids(e.config.grid_x_lines.concat(t||[]))},b.xgrids.remove=function(t){this.internal.removeGridLines(t,!0)},b.ygrids=function(t){var e=this.internal,i=e.config;return t?(i.grid_y_lines=t,e.redrawWithoutRescale(),i.grid_y_lines):i.grid_y_lines},b.ygrids.add=function(t){var e=this.internal;return this.ygrids(e.config.grid_y_lines.concat(t||[]))},b.ygrids.remove=function(t){this.internal.removeGridLines(t,!1)},b.groups=function(t){var e=this.internal,i=e.config;return l(t)?i.data_groups:(i.data_groups=t,e.redraw(),i.data_groups)},b.legend=function(){},b.legend.show=function(t){var e=this.internal;e.showLegend(e.mapToTargetIds(t)),e.updateAndRedraw({withLegend:!0})},b.legend.hide=function(t){var e=this.internal;e.hideLegend(e.mapToTargetIds(t)),e.updateAndRedraw({withLegend:!0})},b.load=function(t){var e=this.internal,i=e.config;t.xs&&e.addXs(t.xs),"names"in t&&b.data.names.bind(this)(t.names),"classes"in t&&Object.keys(t.classes).forEach(function(e){i.data_classes[e]=t.classes[e]}),"categories"in t&&e.isCategorized()&&(i.axis_x_categories=t.categories),"axes"in t&&Object.keys(t.axes).forEach(function(e){i.data_axes[e]=t.axes[e]}),"colors"in t&&Object.keys(t.colors).forEach(function(e){i.data_colors[e]=t.colors[e]}),"cacheIds"in t&&e.hasCaches(t.cacheIds)?e.load(e.getCaches(t.cacheIds),t.done):"unload"in t?e.unload(e.mapToTargetIds("boolean"==typeof t.unload&&t.unload?null:t.unload),function(){e.loadFromArgs(t)}):e.loadFromArgs(t)},b.unload=function(t){var e=this.internal;(t=t||{})instanceof Array?t={ids:t}:"string"==typeof t&&(t={ids:[t]}),e.unload(e.mapToTargetIds(t.ids),function(){e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0}),t.done&&t.done()})},b.regions=function(t){var e=this.internal,i=e.config;return t?(i.regions=t,e.redrawWithoutRescale(),i.regions):i.regions},b.regions.add=function(t){var e=this.internal,i=e.config;return t?(i.regions=i.regions.concat(t),e.redrawWithoutRescale(),i.regions):i.regions},b.regions.remove=function(t){var e,n,a,r=this.internal,s=r.config;return t=t||{},e=r.getOption(t,"duration",s.transition_duration),n=r.getOption(t,"classes",[i.region]),a=r.main.select("."+i.regions).selectAll(n.map(function(t){return"."+t})),(e?a.transition().duration(e):a).style("opacity",0).remove(),s.regions=s.regions.filter(function(t){var e=!1;return!t.class||(t.class.split(" ").forEach(function(t){n.indexOf(t)>=0&&(e=!0)}),!e)}),s.regions},b.selected=function(t){var e=this.internal,n=e.d3;return n.merge(e.main.selectAll("."+i.shapes+e.getTargetSelectorSuffix(t)).selectAll("."+i.shape).filter(function(){return n.select(this).classed(i.SELECTED)}).map(function(t){return t.map(function(t){var e=t.__data__;return e.data?e.data:e})}))},b.select=function(t,e,n){var a=this.internal,r=a.d3,s=a.config;s.data_selection_enabled&&a.main.selectAll("."+i.shapes).selectAll("."+i.shape).each(function(o,c){var d=r.select(this),l=o.data?o.data.id:o.id,h=a.getToggle(this,o).bind(a),g=s.data_selection_grouped||!t||t.indexOf(l)>=0,f=!e||e.indexOf(c)>=0,p=d.classed(i.SELECTED);d.classed(i.line)||d.classed(i.area)||(g&&f?s.data_selection_isselectable(o)&&!p&&h(!0,d.classed(i.SELECTED,!0),o,c):u(n)&&n&&p&&h(!1,d.classed(i.SELECTED,!1),o,c))})},b.unselect=function(t,e){var n=this.internal,a=n.d3,r=n.config;r.data_selection_enabled&&n.main.selectAll("."+i.shapes).selectAll("."+i.shape).each(function(s,o){var c=a.select(this),d=s.data?s.data.id:s.id,l=n.getToggle(this,s).bind(n),u=r.data_selection_grouped||!t||t.indexOf(d)>=0,h=!e||e.indexOf(o)>=0,g=c.classed(i.SELECTED);c.classed(i.line)||c.classed(i.area)||u&&h&&r.data_selection_isselectable(s)&&g&&l(!1,c.classed(i.SELECTED,!1),s,o)})},b.show=function(t,e){var i,n=this.internal;t=n.mapToTargetIds(t),e=e||{},n.removeHiddenTargetIds(t),(i=n.svg.selectAll(n.selectorTargets(t))).transition().style("opacity",1,"important").call(n.endall,function(){i.style("opacity",null).style("opacity",1)}),e.withLegend&&n.showLegend(t),n.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0})},b.hide=function(t,e){var i,n=this.internal;t=n.mapToTargetIds(t),e=e||{},n.addHiddenTargetIds(t),(i=n.svg.selectAll(n.selectorTargets(t))).transition().style("opacity",0,"important").call(n.endall,function(){i.style("opacity",null).style("opacity",0)}),e.withLegend&&n.hideLegend(t),n.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0})},b.toggle=function(t,e){var i=this,n=this.internal;n.mapToTargetIds(t).forEach(function(t){n.isTargetToShow(t)?i.hide(t,e):i.show(t,e)})},b.tooltip=function(){},b.tooltip.show=function(t){var e,i,n=this.internal;t.mouse&&(i=t.mouse),t.data?n.isMultipleX()?(i=[n.x(t.data.x),n.getYScale(t.data.id)(t.data.value)],e=null):e=s(t.data.index)?t.data.index:n.getIndexByX(t.data.x):void 0!==t.x?e=n.getIndexByX(t.x):void 0!==t.index&&(e=t.index),n.dispatchEvent("mouseover",e,i),n.dispatchEvent("mousemove",e,i),n.config.tooltip_onshow.call(n,t.data)},b.tooltip.hide=function(){this.internal.dispatchEvent("mouseout",0),this.internal.config.tooltip_onhide.call(this)},b.transform=function(t,e){var i=this.internal,n=["pie","donut"].indexOf(t)>=0?{withTransform:!0}:null;i.transformTo(e,t,n)},T.transformTo=function(t,e,i){var n=this,a=!n.hasArcType(),r=i||{withTransitionForAxis:a};r.withTransitionForTransform=!1,n.transiting=!1,n.setTargetType(t,e),n.updateTargets(n.data.targets),n.updateAndRedraw(r)},b.x=function(t){var e=this.internal;return arguments.length&&(e.updateTargetX(e.data.targets,t),e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})),e.data.xs},b.xs=function(t){var e=this.internal;return arguments.length&&(e.updateTargetXs(e.data.targets,t),e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})),e.data.xs},b.zoom=function(t){var e=this.internal;return t&&(e.isTimeSeries()&&(t=t.map(function(t){return e.parseDate(t)})),e.brush.extent(t),e.redraw({withUpdateXDomain:!0,withY:e.config.zoom_rescale}),e.config.zoom_onzoom.call(this,e.x.orgDomain())),e.brush.extent()},b.zoom.enable=function(t){var e=this.internal;e.config.zoom_enabled=t,e.updateAndRedraw()},b.unzoom=function(){var t=this.internal;t.brush.clear().update(),t.redraw({withUpdateXDomain:!0})},b.zoom.max=function(t){var e=this.internal,i=e.config,n=e.d3;if(0!==t&&!t)return i.zoom_x_max;i.zoom_x_max=n.max([e.orgXDomain[1],t])},b.zoom.min=function(t){var e=this.internal,i=e.config,n=e.d3;if(0!==t&&!t)return i.zoom_x_min;i.zoom_x_min=n.min([e.orgXDomain[0],t])},b.zoom.range=function(t){if(!arguments.length)return{max:this.domain.max(),min:this.domain.min()};u(t.max)&&this.domain.max(t.max),u(t.min)&&this.domain.min(t.min)},T.initPie=function(){var t=this.d3;this.pie=t.layout.pie().value(function(t){return t.values.reduce(function(t,e){return t+e.value},0)}),this.pie.sort(this.getOrderFunction()||null)},T.updateRadius=function(){var t=this,e=t.config,i=e.gauge_width||e.donut_width,n=t.filterTargetsToShow(t.data.targets).length*t.config.gauge_arcs_minWidth;t.radiusExpanded=Math.min(t.arcWidth,t.arcHeight)/2*(t.hasType("gauge")?.85:1),t.radius=.95*t.radiusExpanded,t.innerRadiusRatio=i?(t.radius-i)/t.radius:.6,t.innerRadius=t.hasType("donut")||t.hasType("gauge")?t.radius*t.innerRadiusRatio:0,t.gaugeArcWidth=i||(n<=t.radius-t.innerRadius?t.radius-t.innerRadius:n<=t.radius?n:t.radius)},T.updateArc=function(){var t=this;t.svgArc=t.getSvgArc(),t.svgArcExpanded=t.getSvgArcExpanded(),t.svgArcExpandedSub=t.getSvgArcExpanded(.98)},T.updateAngle=function(t){var e,i,n,a,r=this,s=r.config,o=!1,c=0;return s?(r.pie(r.filterTargetsToShow(r.data.targets)).forEach(function(e){o||e.data.id!==t.data.id||(o=!0,(t=e).index=c),c++}),isNaN(t.startAngle)&&(t.startAngle=0),isNaN(t.endAngle)&&(t.endAngle=t.startAngle),r.isGaugeType(t.data)&&(e=s.gauge_min,i=s.gauge_max,n=Math.PI*(s.gauge_fullCircle?2:1)/(i-e),a=t.value<e?0:t.value<i?t.value-e:i-e,t.startAngle=s.gauge_startingAngle,t.endAngle=t.startAngle+n*a),o?t:null):null},T.getSvgArc=function(){var t=this,e=t.hasType("gauge"),i=t.gaugeArcWidth/t.filterTargetsToShow(t.data.targets).length,n=t.d3.svg.arc().outerRadius(function(n){return e?t.radius-i*n.index:t.radius}).innerRadius(function(n){return e?t.radius-i*(n.index+1):t.innerRadius}),a=function(e,i){var a;return i?n(e):(a=t.updateAngle(e))?n(a):"M 0 0"};return a.centroid=n.centroid,a},T.getSvgArcExpanded=function(t){t=t||1;var e=this,i=e.hasType("gauge"),n=e.gaugeArcWidth/e.filterTargetsToShow(e.data.targets).length,a=Math.min(e.radiusExpanded*t-e.radius,.8*n-100*(1-t)),r=e.d3.svg.arc().outerRadius(function(r){return i?e.radius-n*r.index+a:e.radiusExpanded*t}).innerRadius(function(t){return i?e.radius-n*(t.index+1):e.innerRadius});return function(t){var i=e.updateAngle(t);return i?r(i):"M 0 0"}},T.getArc=function(t,e,i){return i||this.isArcType(t.data)?this.svgArc(t,e):"M 0 0"},T.transformForArcLabel=function(t){var e,i,n,a,r,s=this,c=s.config,d=s.updateAngle(t),l="",u=s.hasType("gauge");if(d&&!u)e=this.svgArc.centroid(d),i=isNaN(e[0])?0:e[0],n=isNaN(e[1])?0:e[1],a=Math.sqrt(i*i+n*n),l="translate("+i*(r=s.hasType("donut")&&c.donut_label_ratio?o(c.donut_label_ratio)?c.donut_label_ratio(t,s.radius,a):c.donut_label_ratio:s.hasType("pie")&&c.pie_label_ratio?o(c.pie_label_ratio)?c.pie_label_ratio(t,s.radius,a):c.pie_label_ratio:s.radius&&a?(36/s.radius>.375?1.175-36/s.radius:.8)*s.radius/a:0)+","+n*r+")";else if(d&&u&&s.filterTargetsToShow(s.data.targets).length>1){var h=Math.sin(d.endAngle-Math.PI/2);l="translate("+(i=Math.cos(d.endAngle-Math.PI/2)*(s.radiusExpanded+25))+","+(n=h*(s.radiusExpanded+15-Math.abs(10*h))+3)+")"}return l},T.getArcRatio=function(t){var e=this.config,i=Math.PI*(this.hasType("gauge")&&!e.gauge_fullCircle?1:2);return t?(t.endAngle-t.startAngle)/i:null},T.convertToArcData=function(t){return this.addName({id:t.data.id,value:t.value,ratio:this.getArcRatio(t),index:t.index})},T.textForArcLabel=function(t){var e,i,n,a,r,s=this;return s.shouldShowArcLabel()?(i=(e=s.updateAngle(t))?e.value:null,n=s.getArcRatio(e),a=t.data.id,s.hasType("gauge")||s.meetsArcLabelThreshold(n)?(r=s.getArcLabelFormat())?r(i,n,a):s.defaultArcValueFormat(i,n):""):""},T.textForGaugeMinMax=function(t,e){var i=this.getGaugeLabelExtents();return i?i(t,e):t},T.expandArc=function(t){var e,n=this;n.transiting?e=window.setInterval(function(){n.transiting||(window.clearInterval(e),n.legend.selectAll(".c3-legend-item-focused").size()>0&&n.expandArc(t))},10):(t=n.mapToTargetIds(t),n.svg.selectAll(n.selectorTargets(t,"."+i.chartArc)).each(function(t){n.shouldExpand(t.data.id)&&n.d3.select(this).selectAll("path").transition().duration(n.expandDuration(t.data.id)).attr("d",n.svgArcExpanded).transition().duration(2*n.expandDuration(t.data.id)).attr("d",n.svgArcExpandedSub).each(function(t){n.isDonutType(t.data)})}))},T.unexpandArc=function(t){var e=this;e.transiting||(t=e.mapToTargetIds(t),e.svg.selectAll(e.selectorTargets(t,"."+i.chartArc)).selectAll("path").transition().duration(function(t){return e.expandDuration(t.data.id)}).attr("d",e.svgArc),e.svg.selectAll("."+i.arc))},T.expandDuration=function(t){var e=this.config;return this.isDonutType(t)?e.donut_expand_duration:this.isGaugeType(t)?e.gauge_expand_duration:this.isPieType(t)?e.pie_expand_duration:50},T.shouldExpand=function(t){var e=this.config;return this.isDonutType(t)&&e.donut_expand||this.isGaugeType(t)&&e.gauge_expand||this.isPieType(t)&&e.pie_expand},T.shouldShowArcLabel=function(){var t=this.config,e=!0;return this.hasType("donut")?e=t.donut_label_show:this.hasType("pie")&&(e=t.pie_label_show),e},T.meetsArcLabelThreshold=function(t){var e=this.config;return t>=(this.hasType("donut")?e.donut_label_threshold:e.pie_label_threshold)},T.getArcLabelFormat=function(){var t=this.config,e=t.pie_label_format;return this.hasType("gauge")?e=t.gauge_label_format:this.hasType("donut")&&(e=t.donut_label_format),e},T.getGaugeLabelExtents=function(){return this.config.gauge_label_extents},T.getArcTitle=function(){return this.hasType("donut")?this.config.donut_title:""},T.updateTargetsForArc=function(t){var e,n=this,a=n.main,r=n.classChartArc.bind(n),s=n.classArcs.bind(n),o=n.classFocus.bind(n);(e=a.select("."+i.chartArcs).selectAll("."+i.chartArc).data(n.pie(t)).attr("class",function(t){return r(t)+o(t.data)}).enter().append("g").attr("class",r)).append("g").attr("class",s),e.append("text").attr("dy",n.hasType("gauge")?"-.1em":".35em").style("opacity",0).style("text-anchor","middle").style("pointer-events","none")},T.initArc=function(){var t=this;t.arcs=t.main.select("."+i.chart).append("g").attr("class",i.chartArcs).attr("transform",t.getTranslate("arc")),t.arcs.append("text").attr("class",i.chartArcsTitle).style("text-anchor","middle").text(t.getArcTitle())},T.redrawArc=function(t,e,n){var a,r,s,o=this,c=o.d3,d=o.config,l=o.main,u=o.hasType("gauge");if((a=l.selectAll("."+i.arcs).selectAll("."+i.arc).data(o.arcData.bind(o))).enter().append("path").attr("class",o.classArc.bind(o)).style("fill",function(t){return o.color(t.data)}).style("cursor",function(t){return d.interaction_enabled&&d.data_selection_isselectable(t)?"pointer":null}).each(function(t){o.isGaugeType(t.data)&&(t.startAngle=t.endAngle=d.gauge_startingAngle),this._current=t}),u&&((s=l.selectAll("."+i.arcs).selectAll("."+i.arcLabelLine).data(o.arcData.bind(o))).enter().append("rect").attr("class",function(t){return i.arcLabelLine+" "+i.target+" "+i.target+"-"+t.data.id}),1===o.filterTargetsToShow(o.data.targets).length?s.style("display","none"):s.style("fill",function(t){return d.color_pattern.length>0?o.levelColor(t.data.values[0].value):o.color(t.data)}).style("display",d.gauge_labelLine_show?"":"none").each(function(t){var e=0,i=0,n=0,a="";if(o.hiddenTargetIds.indexOf(t.data.id)<0){var r=o.updateAngle(t),s=o.gaugeArcWidth/o.filterTargetsToShow(o.data.targets).length*(r.index+1),d=r.endAngle-Math.PI/2,l=o.radius-s,u=d-(0===l?0:1/l);e=o.radiusExpanded-o.radius+s,i=Math.cos(u)*l,n=Math.sin(u)*l,a="rotate("+180*d/Math.PI+", "+i+", "+n+")"}c.select(this).attr({x:i,y:n,width:e,height:2,transform:a}).style("stroke-dasharray","0, "+(e+2)+", 0")})),a.attr("transform",function(t){return!o.isGaugeType(t.data)&&n?"scale(0)":""}).on("mouseover",d.interaction_enabled?function(t){var e,i;o.transiting||(e=o.updateAngle(t))&&(i=o.convertToArcData(e),o.expandArc(e.data.id),o.api.focus(e.data.id),o.toggleFocusLegend(e.data.id,!0),o.config.data_onmouseover(i,this))}:null).on("mousemove",d.interaction_enabled?function(t){var e,i=o.updateAngle(t);i&&(e=[o.convertToArcData(i)],o.showTooltip(e,this))}:null).on("mouseout",d.interaction_enabled?function(t){var e,i;o.transiting||(e=o.updateAngle(t))&&(i=o.convertToArcData(e),o.unexpandArc(e.data.id),o.api.revert(),o.revertLegend(),o.hideTooltip(),o.config.data_onmouseout(i,this))}:null).on("click",d.interaction_enabled?function(t,e){var i,n=o.updateAngle(t);n&&(i=o.convertToArcData(n),o.toggleShape&&o.toggleShape(this,i,e),o.config.data_onclick.call(o.api,i,this))}:null).each(function(){o.transiting=!0}).transition().duration(t).attrTween("d",function(t){var e,i=o.updateAngle(t);return i?(isNaN(this._current.startAngle)&&(this._current.startAngle=0),isNaN(this._current.endAngle)&&(this._current.endAngle=this._current.startAngle),e=c.interpolate(this._current,i),this._current=e(0),function(i){var n=e(i);return n.data=t.data,o.getArc(n,!0)}):function(){return"M 0 0"}}).attr("transform",n?"scale(1)":"").style("fill",function(t){return o.levelColor?o.levelColor(t.data.values[0].value):o.color(t.data.id)}).call(o.endall,function(){o.transiting=!1}),a.exit().transition().duration(e).style("opacity",0).remove(),l.selectAll("."+i.chartArc).select("text").style("opacity",0).attr("class",function(t){return o.isGaugeType(t.data)?i.gaugeValue:""}).text(o.textForArcLabel.bind(o)).attr("transform",o.transformForArcLabel.bind(o)).style("font-size",function(t){return o.isGaugeType(t.data)&&1===o.filterTargetsToShow(o.data.targets).length?Math.round(o.radius/5)+"px":""}).transition().duration(t).style("opacity",function(t){return o.isTargetToShow(t.data.id)&&o.isArcType(t.data)?1:0}),l.select("."+i.chartArcsTitle).style("opacity",o.hasType("donut")||u?1:0),u){var h=0;(r=o.arcs.select("g."+i.chartArcsBackground).selectAll("path."+i.chartArcsBackground).data(o.data.targets)).enter().append("path"),r.attr("class",function(t,e){return i.chartArcsBackground+" "+i.chartArcsBackground+"-"+e}).attr("d",function(t){if(o.hiddenTargetIds.indexOf(t.id)>=0)return"M 0 0";var e={data:[{value:d.gauge_max}],startAngle:d.gauge_startingAngle,endAngle:-1*d.gauge_startingAngle*(d.gauge_fullCircle?Math.PI:1),index:h++};return o.getArc(e,!0,!0)}),r.exit().remove(),o.arcs.select("."+i.chartArcsGaugeUnit).attr("dy",".75em").text(d.gauge_label_show?d.gauge_units:""),o.arcs.select("."+i.chartArcsGaugeMin).attr("dx",-1*(o.innerRadius+(o.radius-o.innerRadius)/(d.gauge_fullCircle?1:2))+"px").attr("dy","1.2em").text(d.gauge_label_show?o.textForGaugeMinMax(d.gauge_min,!1):""),o.arcs.select("."+i.chartArcsGaugeMax).attr("dx",o.innerRadius+(o.radius-o.innerRadius)/(d.gauge_fullCircle?1:2)+"px").attr("dy","1.2em").text(d.gauge_label_show?o.textForGaugeMinMax(d.gauge_max,!0):"")}},T.initGauge=function(){var t=this.arcs;this.hasType("gauge")&&(t.append("g").attr("class",i.chartArcsBackground),t.append("text").attr("class",i.chartArcsGaugeUnit).style("text-anchor","middle").style("pointer-events","none"),t.append("text").attr("class",i.chartArcsGaugeMin).style("text-anchor","middle").style("pointer-events","none"),t.append("text").attr("class",i.chartArcsGaugeMax).style("text-anchor","middle").style("pointer-events","none"))},T.getGaugeLabelHeight=function(){return this.config.gauge_label_show?20:0},T.hasCaches=function(t){for(var e=0;e<t.length;e++)if(!(t[e]in this.cache))return!1;return!0},T.addCache=function(t,e){this.cache[t]=this.cloneTarget(e)},T.getCaches=function(t){var e,i=[];for(e=0;e<t.length;e++)t[e]in this.cache&&i.push(this.cloneTarget(this.cache[t[e]]));return i},T.categoryName=function(t){var e=this.config;return t<e.axis_x_categories.length?e.axis_x_categories[t]:t},T.generateClass=function(t,e){return" "+t+" "+t+this.getTargetSelectorSuffix(e)},T.classText=function(t){return this.generateClass(i.text,t.index)},T.classTexts=function(t){return this.generateClass(i.texts,t.id)},T.classShape=function(t){return this.generateClass(i.shape,t.index)},T.classShapes=function(t){return this.generateClass(i.shapes,t.id)},T.classLine=function(t){return this.classShape(t)+this.generateClass(i.line,t.id)},T.classLines=function(t){return this.classShapes(t)+this.generateClass(i.lines,t.id)},T.classCircle=function(t){return this.classShape(t)+this.generateClass(i.circle,t.index)},T.classCircles=function(t){return this.classShapes(t)+this.generateClass(i.circles,t.id)},T.classBar=function(t){return this.classShape(t)+this.generateClass(i.bar,t.index)},T.classBars=function(t){return this.classShapes(t)+this.generateClass(i.bars,t.id)},T.classArc=function(t){return this.classShape(t.data)+this.generateClass(i.arc,t.data.id)},T.classArcs=function(t){return this.classShapes(t.data)+this.generateClass(i.arcs,t.data.id)},T.classArea=function(t){return this.classShape(t)+this.generateClass(i.area,t.id)},T.classAreas=function(t){return this.classShapes(t)+this.generateClass(i.areas,t.id)},T.classRegion=function(t,e){return this.generateClass(i.region,e)+" "+("class"in t?t.class:"")},T.classEvent=function(t){return this.generateClass(i.eventRect,t.index)},T.classTarget=function(t){var e=this.config.data_classes[t],n="";return e&&(n=" "+i.target+"-"+e),this.generateClass(i.target,t)+n},T.classFocus=function(t){return this.classFocused(t)+this.classDefocused(t)},T.classFocused=function(t){return" "+(this.focusedTargetIds.indexOf(t.id)>=0?i.focused:"")},T.classDefocused=function(t){return" "+(this.defocusedTargetIds.indexOf(t.id)>=0?i.defocused:"")},T.classChartText=function(t){return i.chartText+this.classTarget(t.id)},T.classChartLine=function(t){return i.chartLine+this.classTarget(t.id)},T.classChartBar=function(t){return i.chartBar+this.classTarget(t.id)},T.classChartArc=function(t){return i.chartArc+this.classTarget(t.data.id)},T.getTargetSelectorSuffix=function(t){return t||0===t?("-"+t).replace(/[\s?!@#$%^&*()_=+,.<>'":;\[\]\/|~`{}\\]/g,"-"):""},T.selectorTarget=function(t,e){return(e||"")+"."+i.target+this.getTargetSelectorSuffix(t)},T.selectorTargets=function(t,e){var i=this;return(t=t||[]).length?t.map(function(t){return i.selectorTarget(t,e)}):null},T.selectorLegend=function(t){return"."+i.legendItem+this.getTargetSelectorSuffix(t)},T.selectorLegends=function(t){var e=this;return t&&t.length?t.map(function(t){return e.selectorLegend(t)}):null},T.getClipPath=function(t){return"url("+(window.navigator.appVersion.toLowerCase().indexOf("msie 9.")>=0?"":document.URL.split("#")[0])+"#"+t+")"},T.appendClip=function(t,e){return t.append("clipPath").attr("id",e).append("rect")},T.getAxisClipX=function(t){var e=Math.max(30,this.margin.left);return t?-(1+e):-(e-1)},T.getAxisClipY=function(t){return t?-20:-this.margin.top},T.getXAxisClipX=function(){return this.getAxisClipX(!this.config.axis_rotated)},T.getXAxisClipY=function(){return this.getAxisClipY(!this.config.axis_rotated)},T.getYAxisClipX=function(){return this.config.axis_y_inner?-1:this.getAxisClipX(this.config.axis_rotated)},T.getYAxisClipY=function(){return this.getAxisClipY(this.config.axis_rotated)},T.getAxisClipWidth=function(t){var e=Math.max(30,this.margin.left),i=Math.max(30,this.margin.right);return t?this.width+2+e+i:this.margin.left+20},T.getAxisClipHeight=function(t){return(t?this.margin.bottom:this.margin.top+this.height)+20},T.getXAxisClipWidth=function(){return this.getAxisClipWidth(!this.config.axis_rotated)},T.getXAxisClipHeight=function(){return this.getAxisClipHeight(!this.config.axis_rotated)},T.getYAxisClipWidth=function(){return this.getAxisClipWidth(this.config.axis_rotated)+(this.config.axis_y_inner?20:0)},T.getYAxisClipHeight=function(){return this.getAxisClipHeight(this.config.axis_rotated)},T.generateColor=function(){var t=this.config,e=this.d3,i=t.data_colors,n=_(t.color_pattern)?t.color_pattern:e.scale.category10().range(),a=t.data_color,r=[];return function(t){var e,s=t.id||t.data&&t.data.id||t;return i[s]instanceof Function?e=i[s](t):i[s]?e=i[s]:(r.indexOf(s)<0&&r.push(s),e=n[r.indexOf(s)%n.length],i[s]=e),a instanceof Function?a(e,t):e}},T.generateLevelColor=function(){var t=this.config,e=t.color_pattern,i=t.color_threshold,n="value"===i.unit,a=i.values&&i.values.length?i.values:[],r=i.max||100;return _(t.color_threshold)?function(t){var i,s=e[e.length-1];for(i=0;i<a.length;i++)if((n?t:100*t/r)<a[i]){s=e[i];break}return s}:null},T.getDefaultConfig=function(){var t={bindto:"#chart",svg_classname:void 0,size_width:void 0,size_height:void 0,padding_left:void 0,padding_right:void 0,padding_top:void 0,padding_bottom:void 0,resize_auto:!0,zoom_enabled:!1,zoom_extent:void 0,zoom_privileged:!1,zoom_rescale:!1,zoom_onzoom:function(){},zoom_onzoomstart:function(){},zoom_onzoomend:function(){},zoom_x_min:void 0,zoom_x_max:void 0,interaction_brighten:!0,interaction_enabled:!0,onmouseover:function(){},onmouseout:function(){},onresize:function(){},onresized:function(){},oninit:function(){},onrendered:function(){},transition_duration:350,data_x:void 0,data_xs:{},data_xFormat:"%Y-%m-%d",data_xLocaltime:!0,data_xSort:!0,data_idConverter:function(t){return t},data_names:{},data_classes:{},data_groups:[],data_axes:{},data_type:void 0,data_types:{},data_labels:{},data_order:"desc",data_regions:{},data_color:void 0,data_colors:{},data_hide:!1,data_filter:void 0,data_selection_enabled:!1,data_selection_grouped:!1,data_selection_isselectable:function(){return!0},data_selection_multiple:!0,data_selection_draggable:!1,data_onclick:function(){},data_onmouseover:function(){},data_onmouseout:function(){},data_onselected:function(){},data_onunselected:function(){},data_url:void 0,data_headers:void 0,data_json:void 0,data_rows:void 0,data_columns:void 0,data_mimeType:void 0,data_keys:void 0,data_empty_label_text:"",subchart_show:!1,subchart_size_height:60,subchart_axis_x_show:!0,subchart_onbrush:function(){},color_pattern:[],color_threshold:{},legend_show:!0,legend_hide:!1,legend_position:"bottom",legend_inset_anchor:"top-left",legend_inset_x:10,legend_inset_y:0,legend_inset_step:void 0,legend_item_onclick:void 0,legend_item_onmouseover:void 0,legend_item_onmouseout:void 0,legend_equally:!1,legend_padding:0,legend_item_tile_width:10,legend_item_tile_height:10,axis_rotated:!1,axis_x_show:!0,axis_x_type:"indexed",axis_x_localtime:!0,axis_x_categories:[],axis_x_tick_centered:!1,axis_x_tick_format:void 0,axis_x_tick_culling:{},axis_x_tick_culling_max:10,axis_x_tick_count:void 0,axis_x_tick_fit:!0,axis_x_tick_values:null,axis_x_tick_rotate:0,axis_x_tick_outer:!0,axis_x_tick_multiline:!0,axis_x_tick_width:null,axis_x_max:void 0,axis_x_min:void 0,axis_x_padding:{},axis_x_height:void 0,axis_x_extent:void 0,axis_x_label:{},axis_y_show:!0,axis_y_type:void 0,axis_y_max:void 0,axis_y_min:void 0,axis_y_inverted:!1,axis_y_center:void 0,axis_y_inner:void 0,axis_y_label:{},axis_y_tick_format:void 0,axis_y_tick_outer:!0,axis_y_tick_values:null,axis_y_tick_rotate:0,axis_y_tick_count:void 0,axis_y_tick_time_value:void 0,axis_y_tick_time_interval:void 0,axis_y_padding:{},axis_y_default:void 0,axis_y2_show:!1,axis_y2_max:void 0,axis_y2_min:void 0,axis_y2_inverted:!1,axis_y2_center:void 0,axis_y2_inner:void 0,axis_y2_label:{},axis_y2_tick_format:void 0,axis_y2_tick_outer:!0,axis_y2_tick_values:null,axis_y2_tick_count:void 0,axis_y2_padding:{},axis_y2_default:void 0,grid_x_show:!1,grid_x_type:"tick",grid_x_lines:[],grid_y_show:!1,grid_y_lines:[],grid_y_ticks:10,grid_focus_show:!0,grid_lines_front:!0,point_show:!0,point_r:2.5,point_sensitivity:10,point_focus_expand_enabled:!0,point_focus_expand_r:void 0,point_select_r:void 0,line_connectNull:!1,line_step_type:"step",bar_width:void 0,bar_width_ratio:.6,bar_width_max:void 0,bar_zerobased:!0,bar_space:0,area_zerobased:!0,area_above:!1,pie_label_show:!0,pie_label_format:void 0,pie_label_threshold:.05,pie_label_ratio:void 0,pie_expand:{},pie_expand_duration:50,gauge_fullCircle:!1,gauge_label_show:!0,gauge_labelLine_show:!0,gauge_label_format:void 0,gauge_min:0,gauge_max:100,gauge_startingAngle:-1*Math.PI/2,gauge_label_extents:void 0,gauge_units:void 0,gauge_width:void 0,gauge_arcs_minWidth:5,gauge_expand:{},gauge_expand_duration:50,donut_label_show:!0,donut_label_format:void 0,donut_label_threshold:.05,donut_label_ratio:void 0,donut_width:void 0,donut_title:"",donut_expand:{},donut_expand_duration:50,spline_interpolation_type:"cardinal",regions:[],tooltip_show:!0,tooltip_grouped:!0,tooltip_order:void 0,tooltip_format_title:void 0,tooltip_format_name:void 0,tooltip_format_value:void 0,tooltip_position:void 0,tooltip_contents:function(t,e,i,n){return this.getTooltipContent?this.getTooltipContent(t,e,i,n):""},tooltip_init_show:!1,tooltip_init_x:0,tooltip_init_position:{top:"0px",left:"50px"},tooltip_onshow:function(){},tooltip_onhide:function(){},title_text:void 0,title_padding:{top:0,right:0,bottom:0,left:0},title_position:"top-center"};return Object.keys(this.additionalConfig).forEach(function(e){t[e]=this.additionalConfig[e]},this),t},T.additionalConfig={},T.loadConfig=function(t){var e,i,a,r=this.config;Object.keys(r).forEach(function(s){e=t,i=s.split("_"),a=function t(){var a=i.shift();return a&&e&&"object"===(void 0===e?"undefined":n(e))&&a in e?(e=e[a],t()):a?void 0:e}(),u(a)&&(r[s]=a)})},T.convertUrlToData=function(t,e,i,n,a){var r=this,s=e||"csv",o=r.d3.xhr(t);i&&Object.keys(i).forEach(function(t){o.header(t,i[t])}),o.get(function(t,e){var i,o=e.response||e.responseText;if(!e)throw new Error(t.responseURL+" "+t.status+" ("+t.statusText+")");i="json"===s?r.convertJsonToData(JSON.parse(o),n):"tsv"===s?r.convertTsvToData(o):r.convertCsvToData(o),a.call(r,i)})},T.convertXsvToData=function(t,e){var i,n=e.parseRows(t);return 1===n.length?(i=[{}],n[0].forEach(function(t){i[0][t]=null})):i=e.parse(t),i},T.convertCsvToData=function(t){return this.convertXsvToData(t,this.d3.csv)},T.convertTsvToData=function(t){return this.convertXsvToData(t,this.d3.tsv)},T.convertJsonToData=function(t,e){var i,n,a=this,r=[];return e?(e.x?(i=e.value.concat(e.x),a.config.data_x=e.x):i=e.value,r.push(i),t.forEach(function(t){var e=[];i.forEach(function(i){var n=a.findValueInJson(t,i);l(n)&&(n=null),e.push(n)}),r.push(e)}),n=a.convertRowsToData(r)):(Object.keys(t).forEach(function(e){r.push([e].concat(t[e]))}),n=a.convertColumnsToData(r)),n},T.findValueInJson=function(t,e){for(var i=(e=(e=e.replace(/\[(\w+)\]/g,".$1")).replace(/^\./,"")).split("."),n=0;n<i.length;++n){var a=i[n];if(!(a in t))return;t=t[a]}return t},T.convertRowsToData=function(t){for(var e=[],i=t[0],n=1;n<t.length;n++){for(var a={},r=0;r<t[n].length;r++){if(l(t[n][r]))throw new Error("Source data is missing a component at ("+n+","+r+")!");a[i[r]]=t[n][r]}e.push(a)}return e},T.convertColumnsToData=function(t){for(var e=[],i=0;i<t.length;i++)for(var n=t[i][0],a=1;a<t[i].length;a++){if(l(e[a-1])&&(e[a-1]={}),l(t[i][a]))throw new Error("Source data is missing a component at ("+i+","+a+")!");e[a-1][n]=t[i][a]}return e},T.convertDataToTargets=function(t,e){var i,n=this,a=n.config,r=n.d3.keys(t[0]).filter(n.isNotX,n),o=n.d3.keys(t[0]).filter(n.isX,n);return r.forEach(function(i){var r=n.getXKey(i);n.isCustomX()||n.isTimeSeries()?o.indexOf(r)>=0?n.data.xs[i]=(e&&n.data.xs[i]?n.data.xs[i]:[]).concat(t.map(function(t){return t[r]}).filter(s).map(function(t,e){return n.generateTargetX(t,i,e)})):a.data_x?n.data.xs[i]=n.getOtherTargetXs():_(a.data_xs)&&(n.data.xs[i]=n.getXValuesOfXKey(r,n.data.targets)):n.data.xs[i]=t.map(function(t,e){return e})}),r.forEach(function(t){if(!n.data.xs[t])throw new Error('x is not defined for id = "'+t+'".')}),(i=r.map(function(e,i){var r=a.data_idConverter(e);return{id:r,id_org:e,values:t.map(function(t,s){var o,c=t[n.getXKey(e)],d=null===t[e]||isNaN(t[e])?null:+t[e];return n.isCustomX()&&n.isCategorized()&&!l(c)?(0===i&&0===s&&(a.axis_x_categories=[]),-1===(o=a.axis_x_categories.indexOf(c))&&(o=a.axis_x_categories.length,a.axis_x_categories.push(c))):o=n.generateTargetX(c,e,s),(l(t[e])||n.data.xs[e].length<=s)&&(o=void 0),{x:o,value:d,id:r}}).filter(function(t){return u(t.x)})}})).forEach(function(t){var e;a.data_xSort&&(t.values=t.values.sort(function(t,e){return(t.x||0===t.x?t.x:1/0)-(e.x||0===e.x?e.x:1/0)})),e=0,t.values.forEach(function(t){t.index=e++}),n.data.xs[t.id].sort(function(t,e){return t-e})}),n.hasNegativeValue=n.hasNegativeValueInTargets(i),n.hasPositiveValue=n.hasPositiveValueInTargets(i),a.data_type&&n.setTargetType(n.mapToIds(i).filter(function(t){return!(t in a.data_types)}),a.data_type),i.forEach(function(t){n.addCache(t.id_org,t)}),i},T.isX=function(t){var e=this.config;return e.data_x&&t===e.data_x||_(e.data_xs)&&y(e.data_xs,t)},T.isNotX=function(t){return!this.isX(t)},T.getXKey=function(t){var e=this.config;return e.data_x?e.data_x:_(e.data_xs)?e.data_xs[t]:null},T.getXValuesOfXKey=function(t,e){var i,n=this;return(e&&_(e)?n.mapToIds(e):[]).forEach(function(e){n.getXKey(e)===t&&(i=n.data.xs[e])}),i},T.getIndexByX=function(t){var e=this.filterByX(this.data.targets,t);return e.length?e[0].index:null},T.getXValue=function(t,e){return t in this.data.xs&&this.data.xs[t]&&s(this.data.xs[t][e])?this.data.xs[t][e]:e},T.getOtherTargetXs=function(){var t=Object.keys(this.data.xs);return t.length?this.data.xs[t[0]]:null},T.getOtherTargetX=function(t){var e=this.getOtherTargetXs();return e&&t<e.length?e[t]:null},T.addXs=function(t){var e=this;Object.keys(t).forEach(function(i){e.config.data_xs[i]=t[i]})},T.hasMultipleX=function(t){return this.d3.set(Object.keys(t).map(function(e){return t[e]})).size()>1},T.isMultipleX=function(){return _(this.config.data_xs)||!this.config.data_xSort||this.hasType("scatter")},T.addName=function(t){var e;return t&&(e=this.config.data_names[t.id],t.name=void 0!==e?e:t.id),t},T.getValueOnIndex=function(t,e){var i=t.filter(function(t){return t.index===e});return i.length?i[0]:null},T.updateTargetX=function(t,e){var i=this;t.forEach(function(t){t.values.forEach(function(n,a){n.x=i.generateTargetX(e[a],t.id,a)}),i.data.xs[t.id]=e})},T.updateTargetXs=function(t,e){var i=this;t.forEach(function(t){e[t.id]&&i.updateTargetX([t],e[t.id])})},T.generateTargetX=function(t,e,i){var n=this;return n.isTimeSeries()?t?n.parseDate(t):n.parseDate(n.getXValue(e,i)):n.isCustomX()&&!n.isCategorized()?s(t)?+t:n.getXValue(e,i):i},T.cloneTarget=function(t){return{id:t.id,id_org:t.id_org,values:t.values.map(function(t){return{x:t.x,value:t.value,id:t.id}})}},T.updateXs=function(){var t=this;t.data.targets.length&&(t.xs=[],t.data.targets[0].values.forEach(function(e){t.xs[e.index]=e.x}))},T.getPrevX=function(t){var e=this.xs[t-1];return void 0!==e?e:null},T.getNextX=function(t){var e=this.xs[t+1];return void 0!==e?e:null},T.getMaxDataCount=function(){return this.d3.max(this.data.targets,function(t){return t.values.length})},T.getMaxDataCountTarget=function(t){var e,i=t.length,n=0;return i>1?t.forEach(function(t){t.values.length>n&&(e=t,n=t.values.length)}):e=i?t[0]:null,e},T.getEdgeX=function(t){return t.length?[this.d3.min(t,function(t){return t.values[0].x}),this.d3.max(t,function(t){return t.values[t.values.length-1].x})]:[0,0]},T.mapToIds=function(t){return t.map(function(t){return t.id})},T.mapToTargetIds=function(t){return t?[].concat(t):this.mapToIds(this.data.targets)},T.hasTarget=function(t,e){var i,n=this.mapToIds(t);for(i=0;i<n.length;i++)if(n[i]===e)return!0;return!1},T.isTargetToShow=function(t){return this.hiddenTargetIds.indexOf(t)<0},T.isLegendToShow=function(t){return this.hiddenLegendIds.indexOf(t)<0},T.filterTargetsToShow=function(t){var e=this;return t.filter(function(t){return e.isTargetToShow(t.id)})},T.mapTargetsToUniqueXs=function(t){var e=this.d3.set(this.d3.merge(t.map(function(t){return t.values.map(function(t){return+t.x})}))).values();return(e=this.isTimeSeries()?e.map(function(t){return new Date(+t)}):e.map(function(t){return+t})).sort(function(t,e){return t<e?-1:t>e?1:t>=e?0:NaN})},T.addHiddenTargetIds=function(t){t=t instanceof Array?t:new Array(t);for(var e=0;e<t.length;e++)this.hiddenTargetIds.indexOf(t[e])<0&&(this.hiddenTargetIds=this.hiddenTargetIds.concat(t[e]))},T.removeHiddenTargetIds=function(t){this.hiddenTargetIds=this.hiddenTargetIds.filter(function(e){return t.indexOf(e)<0})},T.addHiddenLegendIds=function(t){t=t instanceof Array?t:new Array(t);for(var e=0;e<t.length;e++)this.hiddenLegendIds.indexOf(t[e])<0&&(this.hiddenLegendIds=this.hiddenLegendIds.concat(t[e]))},T.removeHiddenLegendIds=function(t){this.hiddenLegendIds=this.hiddenLegendIds.filter(function(e){return t.indexOf(e)<0})},T.getValuesAsIdKeyed=function(t){var e={};return t.forEach(function(t){e[t.id]=[],t.values.forEach(function(i){e[t.id].push(i.value)})}),e},T.checkValueInTargets=function(t,e){var i,n,a,r=Object.keys(t);for(i=0;i<r.length;i++)for(a=t[r[i]].values,n=0;n<a.length;n++)if(e(a[n].value))return!0;return!1},T.hasNegativeValueInTargets=function(t){return this.checkValueInTargets(t,function(t){return t<0})},T.hasPositiveValueInTargets=function(t){return this.checkValueInTargets(t,function(t){return t>0})},T.isOrderDesc=function(){var t=this.config;return"string"==typeof t.data_order&&"desc"===t.data_order.toLowerCase()},T.isOrderAsc=function(){var t=this.config;return"string"==typeof t.data_order&&"asc"===t.data_order.toLowerCase()},T.getOrderFunction=function(){var t=this.config,e=this.isOrderAsc(),i=this.isOrderDesc();if(e||i)return function(t,e){var n=function(t,e){return t+Math.abs(e.value)},a=t.values.reduce(n,0),r=e.values.reduce(n,0);return i?r-a:a-r};if(o(t.data_order))return t.data_order;if(c(t.data_order)){var n=t.data_order;return function(t,e){return n.indexOf(t.id)-n.indexOf(e.id)}}},T.orderTargets=function(t){var e=this.getOrderFunction();return e&&(t.sort(e),(this.isOrderAsc()||this.isOrderDesc())&&t.reverse()),t},T.filterByX=function(t,e){return this.d3.merge(t.map(function(t){return t.values})).filter(function(t){return t.x-e==0})},T.filterRemoveNull=function(t){return t.filter(function(t){return s(t.value)})},T.filterByXDomain=function(t,e){return t.map(function(t){return{id:t.id,id_org:t.id_org,values:t.values.filter(function(t){return e[0]<=t.x&&t.x<=e[1]})}})},T.hasDataLabel=function(){var t=this.config;return!("boolean"!=typeof t.data_labels||!t.data_labels)||!("object"!==n(t.data_labels)||!_(t.data_labels))},T.getDataLabelLength=function(t,e,i){var n=this,a=[0,0];return n.selectChart.select("svg").selectAll(".dummy").data([t,e]).enter().append("text").text(function(t){return n.dataLabelFormat(t.id)(t)}).each(function(t,e){a[e]=1.3*this.getBoundingClientRect()[i]}).remove(),a},T.isNoneArc=function(t){return this.hasTarget(this.data.targets,t.id)},T.isArc=function(t){return"data"in t&&this.hasTarget(this.data.targets,t.data.id)},T.findSameXOfValues=function(t,e){var i,n=t[e].x,a=[];for(i=e-1;i>=0&&n===t[i].x;i--)a.push(t[i]);for(i=e;i<t.length&&n===t[i].x;i++)a.push(t[i]);return a},T.findClosestFromTargets=function(t,e){var i,n=this;return i=t.map(function(t){return n.findClosest(t.values,e)}),n.findClosest(i,e)},T.findClosest=function(t,e){var n,a=this,r=a.config.point_sensitivity;return t.filter(function(t){return t&&a.isBarType(t.id)}).forEach(function(t){var e=a.main.select("."+i.bars+a.getTargetSelectorSuffix(t.id)+" ."+i.bar+"-"+t.index).node();!n&&a.isWithinBar(e)&&(n=t)}),t.filter(function(t){return t&&!a.isBarType(t.id)}).forEach(function(t){var i=a.dist(t,e);i<r&&(r=i,n=t)}),n},T.dist=function(t,e){var i=this.config,n=i.axis_rotated?1:0,a=i.axis_rotated?0:1,r=this.circleY(t,t.index),s=this.x(t.x);return Math.sqrt(Math.pow(s-e[n],2)+Math.pow(r-e[a],2))},T.convertValuesToStep=function(t){var e,i=[].concat(t);if(!this.isCategorized())return t;for(e=t.length+1;0<e;e--)i[e]=i[e-1];return i[0]={x:i[0].x-1,value:i[0].value,id:i[0].id},i[t.length+1]={x:i[t.length].x+1,value:i[t.length].value,id:i[t.length].id},i},T.updateDataAttributes=function(t,e){var i=this.config["data_"+t];return void 0===e?i:(Object.keys(e).forEach(function(t){i[t]=e[t]}),this.redraw({withLegend:!0}),i)},T.load=function(t,e){var i=this;t&&(e.filter&&(t=t.filter(e.filter)),(e.type||e.types)&&t.forEach(function(t){var n=e.types&&e.types[t.id]?e.types[t.id]:e.type;i.setTargetType(t.id,n)}),i.data.targets.forEach(function(e){for(var i=0;i<t.length;i++)if(e.id===t[i].id){e.values=t[i].values,t.splice(i,1);break}}),i.data.targets=i.data.targets.concat(t)),i.updateTargets(i.data.targets),i.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0}),e.done&&e.done()},T.loadFromArgs=function(t){var e=this;t.data?e.load(e.convertDataToTargets(t.data),t):t.url?e.convertUrlToData(t.url,t.mimeType,t.headers,t.keys,function(i){e.load(e.convertDataToTargets(i),t)}):t.json?e.load(e.convertDataToTargets(e.convertJsonToData(t.json,t.keys)),t):t.rows?e.load(e.convertDataToTargets(e.convertRowsToData(t.rows)),t):t.columns?e.load(e.convertDataToTargets(e.convertColumnsToData(t.columns)),t):e.load(null,t)},T.unload=function(t,e){var n=this;e||(e=function(){}),(t=t.filter(function(t){return n.hasTarget(n.data.targets,t)}))&&0!==t.length?(n.svg.selectAll(t.map(function(t){return n.selectorTarget(t)})).transition().style("opacity",0).remove().call(n.endall,e),t.forEach(function(t){n.withoutFadeIn[t]=!1,n.legend&&n.legend.selectAll("."+i.legendItem+n.getTargetSelectorSuffix(t)).remove(),n.data.targets=n.data.targets.filter(function(e){return e.id!==t})})):e()},T.getYDomainMin=function(t){var e,i,n,a,r,s,o=this,c=o.config,d=o.mapToIds(t),l=o.getValuesAsIdKeyed(t);if(c.data_groups.length>0)for(s=o.hasNegativeValueInTargets(t),e=0;e<c.data_groups.length;e++)if(0!==(a=c.data_groups[e].filter(function(t){return d.indexOf(t)>=0})).length)for(n=a[0],s&&l[n]&&l[n].forEach(function(t,e){l[n][e]=t<0?t:0}),i=1;i<a.length;i++)r=a[i],l[r]&&l[r].forEach(function(t,e){o.axis.getId(r)!==o.axis.getId(n)||!l[n]||s&&+t>0||(l[n][e]+=+t)});return o.d3.min(Object.keys(l).map(function(t){return o.d3.min(l[t])}))},T.getYDomainMax=function(t){var e,i,n,a,r,s,o=this,c=o.config,d=o.mapToIds(t),l=o.getValuesAsIdKeyed(t);if(c.data_groups.length>0)for(s=o.hasPositiveValueInTargets(t),e=0;e<c.data_groups.length;e++)if(0!==(a=c.data_groups[e].filter(function(t){return d.indexOf(t)>=0})).length)for(n=a[0],s&&l[n]&&l[n].forEach(function(t,e){l[n][e]=t>0?t:0}),i=1;i<a.length;i++)r=a[i],l[r]&&l[r].forEach(function(t,e){o.axis.getId(r)!==o.axis.getId(n)||!l[n]||s&&+t<0||(l[n][e]+=+t)});return o.d3.max(Object.keys(l).map(function(t){return o.d3.max(l[t])}))},T.getYDomain=function(t,e,i){var n,a,r,o,c,d,l,u,h,g,p=this,x=p.config,y=t.filter(function(t){return p.axis.getId(t.id)===e}),m=i?p.filterByXDomain(y,i):y,S="y2"===e?x.axis_y2_min:x.axis_y_min,w="y2"===e?x.axis_y2_max:x.axis_y_max,v=p.getYDomainMin(m),b=p.getYDomainMax(m),T="y2"===e?x.axis_y2_center:x.axis_y_center,A=p.hasType("bar",m)&&x.bar_zerobased||p.hasType("area",m)&&x.area_zerobased,P="y2"===e?x.axis_y2_inverted:x.axis_y_inverted,C=p.hasDataLabel()&&x.axis_rotated,L=p.hasDataLabel()&&!x.axis_rotated;return v=s(S)?S:s(w)?v<w?v:w-10:v,b=s(w)?w:s(S)?S<b?b:S+10:b,0===m.length?"y2"===e?p.y2.domain():p.y.domain():(isNaN(v)&&(v=0),isNaN(b)&&(b=v),v===b&&(v<0?b=0:v=0),h=v>=0&&b>=0,g=v<=0&&b<=0,(s(S)&&h||s(w)&&g)&&(A=!1),A&&(h&&(v=0),g&&(b=0)),r=o=.1*(a=Math.abs(b-v)),void 0!==T&&(b=T+(c=Math.max(Math.abs(v),Math.abs(b))),v=T-c),C?(d=p.getDataLabelLength(v,b,"width"),l=f(p.y.range()),r+=a*((u=[d[0]/l,d[1]/l])[1]/(1-u[0]-u[1])),o+=a*(u[0]/(1-u[0]-u[1]))):L&&(d=p.getDataLabelLength(v,b,"height"),r+=p.axis.convertPixelsToAxisPadding(d[1],a),o+=p.axis.convertPixelsToAxisPadding(d[0],a)),"y"===e&&_(x.axis_y_padding)&&(r=p.axis.getPadding(x.axis_y_padding,"top",r,a),o=p.axis.getPadding(x.axis_y_padding,"bottom",o,a)),"y2"===e&&_(x.axis_y2_padding)&&(r=p.axis.getPadding(x.axis_y2_padding,"top",r,a),o=p.axis.getPadding(x.axis_y2_padding,"bottom",o,a)),A&&(h&&(o=v),g&&(r=-b)),n=[v-o,b+r],P?n.reverse():n)},T.getXDomainMin=function(t){var e=this,i=e.config;return u(i.axis_x_min)?e.isTimeSeries()?this.parseDate(i.axis_x_min):i.axis_x_min:e.d3.min(t,function(t){return e.d3.min(t.values,function(t){return t.x})})},T.getXDomainMax=function(t){var e=this,i=e.config;return u(i.axis_x_max)?e.isTimeSeries()?this.parseDate(i.axis_x_max):i.axis_x_max:e.d3.max(t,function(t){return e.d3.max(t.values,function(t){return t.x})})},T.getXDomainPadding=function(t){var e,i,a,r,o=this.config,c=t[1]-t[0];return i=this.isCategorized()?0:this.hasType("bar")?(e=this.getMaxDataCount())>1?c/(e-1)/2:.5:.01*c,"object"===n(o.axis_x_padding)&&_(o.axis_x_padding)?(a=s(o.axis_x_padding.left)?o.axis_x_padding.left:i,r=s(o.axis_x_padding.right)?o.axis_x_padding.right:i):a=r="number"==typeof o.axis_x_padding?o.axis_x_padding:i,{left:a,right:r}},T.getXDomain=function(t){var e=this,i=[e.getXDomainMin(t),e.getXDomainMax(t)],n=i[0],a=i[1],r=e.getXDomainPadding(i),s=0,o=0;return n-a!=0||e.isCategorized()||(e.isTimeSeries()?(n=new Date(.5*n.getTime()),a=new Date(1.5*a.getTime())):(n=0===n?1:.5*n,a=0===a?-1:1.5*a)),(n||0===n)&&(s=e.isTimeSeries()?new Date(n.getTime()-r.left):n-r.left),(a||0===a)&&(o=e.isTimeSeries()?new Date(a.getTime()+r.right):a+r.right),[s,o]},T.updateXDomain=function(t,e,i,n,a){var r=this,s=r.config;return i&&(r.x.domain(a||r.d3.extent(r.getXDomain(t))),r.orgXDomain=r.x.domain(),s.zoom_enabled&&r.zoom.scale(r.x).updateScaleExtent(),r.subX.domain(r.x.domain()),r.brush&&r.brush.scale(r.subX)),e&&(r.x.domain(a||(!r.brush||r.brush.empty()?r.orgXDomain:r.brush.extent())),s.zoom_enabled&&r.zoom.scale(r.x).updateScaleExtent()),n&&r.x.domain(r.trimXDomain(r.x.orgDomain())),r.x.domain()},T.trimXDomain=function(t){var e=this.getZoomDomain(),i=e[0],n=e[1];return t[0]<=i&&(t[1]=+t[1]+(i-t[0]),t[0]=i),n<=t[1]&&(t[0]=+t[0]-(t[1]-n),t[1]=n),t},T.drag=function(t){var e,n,a,r,s,o,c,d,l=this,u=l.config,h=l.main,g=l.d3;l.hasArcType()||u.data_selection_enabled&&(u.zoom_enabled&&!l.zoom.altDomain||u.data_selection_multiple&&(e=l.dragStart[0],n=l.dragStart[1],a=t[0],r=t[1],s=Math.min(e,a),o=Math.max(e,a),c=u.data_selection_grouped?l.margin.top:Math.min(n,r),d=u.data_selection_grouped?l.height:Math.max(n,r),h.select("."+i.dragarea).attr("x",s).attr("y",c).attr("width",o-s).attr("height",d-c),h.selectAll("."+i.shapes).selectAll("."+i.shape).filter(function(t){return u.data_selection_isselectable(t)}).each(function(t,e){var n,a,r,u,h,f,p=g.select(this),_=p.classed(i.SELECTED),x=p.classed(i.INCLUDED),y=!1;if(p.classed(i.circle))n=1*p.attr("cx"),a=1*p.attr("cy"),h=l.togglePoint,y=s<n&&n<o&&c<a&&a<d;else{if(!p.classed(i.bar))return;n=(f=S(this)).x,a=f.y,r=f.width,u=f.height,h=l.togglePath,y=!(o<n||n+r<s||d<a||a+u<c)}y^x&&(p.classed(i.INCLUDED,!x),p.classed(i.SELECTED,!_),h.call(l,!_,p,t,e))})))},T.dragstart=function(t){var e=this,n=e.config;e.hasArcType()||n.data_selection_enabled&&(e.dragStart=t,e.main.select("."+i.chart).append("rect").attr("class",i.dragarea).style("opacity",.1),e.dragging=!0)},T.dragend=function(){var t=this,e=t.config;t.hasArcType()||e.data_selection_enabled&&(t.main.select("."+i.dragarea).transition().duration(100).style("opacity",0).remove(),t.main.selectAll("."+i.shape).classed(i.INCLUDED,!1),t.dragging=!1)},T.getYFormat=function(t){var e=this,i=t&&!e.hasType("gauge")?e.defaultArcValueFormat:e.yFormat,n=t&&!e.hasType("gauge")?e.defaultArcValueFormat:e.y2Format;return function(t,a,r){return("y2"===e.axis.getId(r)?n:i).call(e,t,a)}},T.yFormat=function(t){var e=this.config;return(e.axis_y_tick_format?e.axis_y_tick_format:this.defaultValueFormat)(t)},T.y2Format=function(t){var e=this.config;return(e.axis_y2_tick_format?e.axis_y2_tick_format:this.defaultValueFormat)(t)},T.defaultValueFormat=function(t){return s(t)?+t:""},T.defaultArcValueFormat=function(t,e){return(100*e).toFixed(1)+"%"},T.dataLabelFormat=function(t){var e=this.config.data_labels,i=function(t){return s(t)?+t:""};return"function"==typeof e.format?e.format:"object"===n(e.format)?e.format[t]?!0===e.format[t]?i:e.format[t]:function(){return""}:i},T.initGrid=function(){var t=this,e=t.config,n=t.d3;t.grid=t.main.append("g").attr("clip-path",t.clipPathForGrid).attr("class",i.grid),e.grid_x_show&&t.grid.append("g").attr("class",i.xgrids),e.grid_y_show&&t.grid.append("g").attr("class",i.ygrids),e.grid_focus_show&&t.grid.append("g").attr("class",i.xgridFocus).append("line").attr("class",i.xgridFocus),t.xgrid=n.selectAll([]),e.grid_lines_front||t.initGridLines()},T.initGridLines=function(){var t=this,e=t.d3;t.gridLines=t.main.append("g").attr("clip-path",t.clipPathForGrid).attr("class",i.grid+" "+i.gridLines),t.gridLines.append("g").attr("class",i.xgridLines),t.gridLines.append("g").attr("class",i.ygridLines),t.xgridLines=e.selectAll([])},T.updateXGrid=function(t){var e=this,n=e.config,a=e.d3,r=e.generateGridData(n.grid_x_type,e.x),s=e.isCategorized()?e.xAxis.tickOffset():0;e.xgridAttr=n.axis_rotated?{x1:0,x2:e.width,y1:function(t){return e.x(t)-s},y2:function(t){return e.x(t)-s}}:{x1:function(t){return e.x(t)+s},x2:function(t){return e.x(t)+s},y1:0,y2:e.height},e.xgrid=e.main.select("."+i.xgrids).selectAll("."+i.xgrid).data(r),e.xgrid.enter().append("line").attr("class",i.xgrid),t||e.xgrid.attr(e.xgridAttr).style("opacity",function(){return+a.select(this).attr(n.axis_rotated?"y1":"x1")===(n.axis_rotated?e.height:0)?0:1}),e.xgrid.exit().remove()},T.updateYGrid=function(){var t=this,e=t.config,n=t.yAxis.tickValues()||t.y.ticks(e.grid_y_ticks);t.ygrid=t.main.select("."+i.ygrids).selectAll("."+i.ygrid).data(n),t.ygrid.enter().append("line").attr("class",i.ygrid),t.ygrid.attr("x1",e.axis_rotated?t.y:0).attr("x2",e.axis_rotated?t.y:t.width).attr("y1",e.axis_rotated?0:t.y).attr("y2",e.axis_rotated?t.height:t.y),t.ygrid.exit().remove(),t.smoothLines(t.ygrid,"grid")},T.gridTextAnchor=function(t){return t.position?t.position:"end"},T.gridTextDx=function(t){return"start"===t.position?4:"middle"===t.position?0:-4},T.xGridTextX=function(t){return"start"===t.position?-this.height:"middle"===t.position?-this.height/2:0},T.yGridTextX=function(t){return"start"===t.position?0:"middle"===t.position?this.width/2:this.width},T.updateGrid=function(t){var e,n,a,r=this,s=r.main,o=r.config;r.grid.style("visibility",r.hasArcType()?"hidden":"visible"),s.select("line."+i.xgridFocus).style("visibility","hidden"),o.grid_x_show&&r.updateXGrid(),r.xgridLines=s.select("."+i.xgridLines).selectAll("."+i.xgridLine).data(o.grid_x_lines),(e=r.xgridLines.enter().append("g").attr("class",function(t){return i.xgridLine+(t.class?" "+t.class:"")})).append("line").style("opacity",0),e.append("text").attr("text-anchor",r.gridTextAnchor).attr("transform",o.axis_rotated?"":"rotate(-90)").attr("dx",r.gridTextDx).attr("dy",-5).style("opacity",0),r.xgridLines.exit().transition().duration(t).style("opacity",0).remove(),o.grid_y_show&&r.updateYGrid(),r.ygridLines=s.select("."+i.ygridLines).selectAll("."+i.ygridLine).data(o.grid_y_lines),(n=r.ygridLines.enter().append("g").attr("class",function(t){return i.ygridLine+(t.class?" "+t.class:"")})).append("line").style("opacity",0),n.append("text").attr("text-anchor",r.gridTextAnchor).attr("transform",o.axis_rotated?"rotate(-90)":"").attr("dx",r.gridTextDx).attr("dy",-5).style("opacity",0),a=r.yv.bind(r),r.ygridLines.select("line").transition().duration(t).attr("x1",o.axis_rotated?a:0).attr("x2",o.axis_rotated?a:r.width).attr("y1",o.axis_rotated?0:a).attr("y2",o.axis_rotated?r.height:a).style("opacity",1),r.ygridLines.select("text").transition().duration(t).attr("x",o.axis_rotated?r.xGridTextX.bind(r):r.yGridTextX.bind(r)).attr("y",a).text(function(t){return t.text}).style("opacity",1),r.ygridLines.exit().transition().duration(t).style("opacity",0).remove()},T.redrawGrid=function(t){var e=this,i=e.config,n=e.xv.bind(e),a=e.xgridLines.select("line"),r=e.xgridLines.select("text");return[(t?a.transition():a).attr("x1",i.axis_rotated?0:n).attr("x2",i.axis_rotated?e.width:n).attr("y1",i.axis_rotated?n:0).attr("y2",i.axis_rotated?n:e.height).style("opacity",1),(t?r.transition():r).attr("x",i.axis_rotated?e.yGridTextX.bind(e):e.xGridTextX.bind(e)).attr("y",n).text(function(t){return t.text}).style("opacity",1)]},T.showXGridFocus=function(t){var e=this,n=e.config,a=t.filter(function(t){return t&&s(t.value)}),r=e.main.selectAll("line."+i.xgridFocus),o=e.xx.bind(e);n.tooltip_show&&(e.hasType("scatter")||e.hasArcType()||(r.style("visibility","visible").data([a[0]]).attr(n.axis_rotated?"y1":"x1",o).attr(n.axis_rotated?"y2":"x2",o),e.smoothLines(r,"grid")))},T.hideXGridFocus=function(){this.main.select("line."+i.xgridFocus).style("visibility","hidden")},T.updateXgridFocus=function(){var t=this.config;this.main.select("line."+i.xgridFocus).attr("x1",t.axis_rotated?0:-10).attr("x2",t.axis_rotated?this.width:-10).attr("y1",t.axis_rotated?-10:0).attr("y2",t.axis_rotated?-10:this.height)},T.generateGridData=function(t,e){var n,a,r,s,o=[],c=this.main.select("."+i.axisX).selectAll(".tick").size();if("year"===t)for(a=(n=this.getXDomain())[0].getFullYear(),r=n[1].getFullYear(),s=a;s<=r;s++)o.push(new Date(s+"-01-01 00:00:00"));else(o=e.ticks(10)).length>c&&(o=o.filter(function(t){return(""+t).indexOf(".")<0}));return o},T.getGridFilterToRemove=function(t){return t?function(e){var i=!1;return[].concat(t).forEach(function(t){("value"in t&&e.value===t.value||"class"in t&&e.class===t.class)&&(i=!0)}),i}:function(){return!0}},T.removeGridLines=function(t,e){var n=this.config,a=this.getGridFilterToRemove(t),r=function(t){return!a(t)},s=e?i.xgridLines:i.ygridLines,o=e?i.xgridLine:i.ygridLine;this.main.select("."+s).selectAll("."+o).filter(a).transition().duration(n.transition_duration).style("opacity",0).remove(),e?n.grid_x_lines=n.grid_x_lines.filter(r):n.grid_y_lines=n.grid_y_lines.filter(r)},T.initEventRect=function(){this.main.select("."+i.chart).append("g").attr("class",i.eventRects).style("fill-opacity",0)},T.redrawEventRect=function(){var t,e,n=this,a=n.config,r=n.isMultipleX(),s=n.main.select("."+i.eventRects).style("cursor",a.zoom_enabled?a.axis_rotated?"ns-resize":"ew-resize":null).classed(i.eventRectsMultiple,r).classed(i.eventRectsSingle,!r);s.selectAll("."+i.eventRect).remove(),n.eventRect=s.selectAll("."+i.eventRect),r?(t=n.eventRect.data([0]),n.generateEventRectsForMultipleXs(t.enter()),n.updateEventRect(t)):(e=n.getMaxDataCountTarget(n.data.targets),s.datum(e?e.values:[]),n.eventRect=s.selectAll("."+i.eventRect),t=n.eventRect.data(function(t){return t}),n.generateEventRectsForSingleX(t.enter()),n.updateEventRect(t),t.exit().remove())},T.updateEventRect=function(t){var e,i,n,a,r,s,o=this,c=o.config;t=t||o.eventRect.data(function(t){return t}),o.isMultipleX()?(e=0,i=0,n=o.width,a=o.height):(!o.isCustomX()&&!o.isTimeSeries()||o.isCategorized()?(r=o.getEventRectWidth(),s=function(t){return o.x(t.x)-r/2}):(o.updateXs(),r=function(t){var e=o.getPrevX(t.index),i=o.getNextX(t.index);return null===e&&null===i?c.axis_rotated?o.height:o.width:(null===e&&(e=o.x.domain()[0]),null===i&&(i=o.x.domain()[1]),Math.max(0,(o.x(i)-o.x(e))/2))},s=function(t){var e=o.getPrevX(t.index),i=o.getNextX(t.index),n=o.data.xs[t.id][t.index];return null===e&&null===i?0:(null===e&&(e=o.x.domain()[0]),(o.x(n)+o.x(e))/2)}),e=c.axis_rotated?0:s,i=c.axis_rotated?s:0,n=c.axis_rotated?o.width:r,a=c.axis_rotated?r:o.height),t.attr("class",o.classEvent.bind(o)).attr("x",e).attr("y",i).attr("width",n).attr("height",a)},T.generateEventRectsForSingleX=function(t){var e=this,n=e.d3,a=e.config;t.append("rect").attr("class",e.classEvent.bind(e)).style("cursor",a.data_selection_enabled&&a.data_selection_grouped?"pointer":null).on("mouseover",function(t){var n=t.index;e.dragging||e.flowing||e.hasArcType()||(a.point_focus_expand_enabled&&e.expandCircles(n,null,!0),e.expandBars(n,null,!0),e.main.selectAll("."+i.shape+"-"+n).each(function(t){a.data_onmouseover.call(e.api,t)}))}).on("mouseout",function(t){var n=t.index;e.config&&(e.hasArcType()||(e.hideXGridFocus(),e.hideTooltip(),e.unexpandCircles(),e.unexpandBars(),e.main.selectAll("."+i.shape+"-"+n).each(function(t){a.data_onmouseout.call(e.api,t)})))}).on("mousemove",function(t){var r,s=t.index,o=e.svg.select("."+i.eventRect+"-"+s);e.dragging||e.flowing||e.hasArcType()||(e.isStepType(t)&&"step-after"===e.config.line_step_type&&n.mouse(this)[0]<e.x(e.getXValue(t.id,s))&&(s-=1),r=e.filterTargetsToShow(e.data.targets).map(function(t){return e.addName(e.getValueOnIndex(t.values,s))}),a.tooltip_grouped&&(e.showTooltip(r,this),e.showXGridFocus(r)),(!a.tooltip_grouped||a.data_selection_enabled&&!a.data_selection_grouped)&&e.main.selectAll("."+i.shape+"-"+s).each(function(){n.select(this).classed(i.EXPANDED,!0),a.data_selection_enabled&&o.style("cursor",a.data_selection_grouped?"pointer":null),a.tooltip_grouped||(e.hideXGridFocus(),e.hideTooltip(),a.data_selection_grouped||(e.unexpandCircles(s),e.unexpandBars(s)))}).filter(function(t){return e.isWithinShape(this,t)}).each(function(t){a.data_selection_enabled&&(a.data_selection_grouped||a.data_selection_isselectable(t))&&o.style("cursor","pointer"),a.tooltip_grouped||(e.showTooltip([t],this),e.showXGridFocus([t]),a.point_focus_expand_enabled&&e.expandCircles(s,t.id,!0),e.expandBars(s,t.id,!0))}))}).on("click",function(t){var r=t.index;!e.hasArcType()&&e.toggleShape&&(e.cancelClick?e.cancelClick=!1:(e.isStepType(t)&&"step-after"===a.line_step_type&&n.mouse(this)[0]<e.x(e.getXValue(t.id,r))&&(r-=1),e.main.selectAll("."+i.shape+"-"+r).each(function(t){(a.data_selection_grouped||e.isWithinShape(this,t))&&(e.toggleShape(this,t,r),e.config.data_onclick.call(e.api,t,this))})))}).call(a.data_selection_draggable&&e.drag?n.behavior.drag().origin(Object).on("drag",function(){e.drag(n.mouse(this))}).on("dragstart",function(){e.dragstart(n.mouse(this))}).on("dragend",function(){e.dragend()}):function(){})},T.generateEventRectsForMultipleXs=function(t){var e=this,n=e.d3,a=e.config;function r(){e.svg.select("."+i.eventRect).style("cursor",null),e.hideXGridFocus(),e.hideTooltip(),e.unexpandCircles(),e.unexpandBars()}t.append("rect").attr("x",0).attr("y",0).attr("width",e.width).attr("height",e.height).attr("class",i.eventRect).on("mouseout",function(){e.config&&(e.hasArcType()||r())}).on("mousemove",function(){var t,s,o,c=e.filterTargetsToShow(e.data.targets);e.dragging||e.hasArcType(c)||(t=n.mouse(this),s=e.findClosestFromTargets(c,t),!e.mouseover||s&&s.id===e.mouseover.id||(a.data_onmouseout.call(e.api,e.mouseover),e.mouseover=void 0),s?(o=(e.isScatterType(s)||!a.tooltip_grouped?[s]:e.filterByX(c,s.x)).map(function(t){return e.addName(t)}),e.showTooltip(o,this),a.point_focus_expand_enabled&&e.expandCircles(s.index,s.id,!0),e.expandBars(s.index,s.id,!0),e.showXGridFocus(o),(e.isBarType(s.id)||e.dist(s,t)<a.point_sensitivity)&&(e.svg.select("."+i.eventRect).style("cursor","pointer"),e.mouseover||(a.data_onmouseover.call(e.api,s),e.mouseover=s))):r())}).on("click",function(){var t,r,s=e.filterTargetsToShow(e.data.targets);e.hasArcType(s)||(t=n.mouse(this),(r=e.findClosestFromTargets(s,t))&&(e.isBarType(r.id)||e.dist(r,t)<a.point_sensitivity)&&e.main.selectAll("."+i.shapes+e.getTargetSelectorSuffix(r.id)).selectAll("."+i.shape+"-"+r.index).each(function(){(a.data_selection_grouped||e.isWithinShape(this,r))&&(e.toggleShape(this,r,r.index),e.config.data_onclick.call(e.api,r,this))}))}).call(a.data_selection_draggable&&e.drag?n.behavior.drag().origin(Object).on("drag",function(){e.drag(n.mouse(this))}).on("dragstart",function(){e.dragstart(n.mouse(this))}).on("dragend",function(){e.dragend()}):function(){})},T.dispatchEvent=function(t,e,n){var a="."+i.eventRect+(this.isMultipleX()?"":"-"+e),r=this.main.select(a).node(),s=r.getBoundingClientRect(),o=s.left+(n?n[0]:0),c=s.top+(n?n[1]:0),d=document.createEvent("MouseEvents");d.initMouseEvent(t,!0,!0,window,0,o,c,o,c,!1,!1,!1,!1,0,null),r.dispatchEvent(d)},T.initLegend=function(){var t=this;if(t.legendItemTextBox={},t.legendHasRendered=!1,t.legend=t.svg.append("g").attr("transform",t.getTranslate("legend")),!t.config.legend_show)return t.legend.style("visibility","hidden"),void(t.hiddenLegendIds=t.mapToIds(t.data.targets));t.updateLegendWithDefaults()},T.updateLegendWithDefaults=function(){this.updateLegend(this.mapToIds(this.data.targets),{withTransform:!1,withTransitionForTransform:!1,withTransition:!1})},T.updateSizeForLegend=function(t,e){var i=this,n=i.config,a={top:i.isLegendTop?i.getCurrentPaddingTop()+n.legend_inset_y****:i.currentHeight-t-i.getCurrentPaddingBottom()-n.legend_inset_y,left:i.isLegendLeft?i.getCurrentPaddingLeft()+n.legend_inset_x+.5:i.currentWidth-e-i.getCurrentPaddingRight()-n.legend_inset_x+.5};i.margin3={top:i.isLegendRight?0:i.isLegendInset?a.top:i.currentHeight-t,right:NaN,bottom:0,left:i.isLegendRight?i.currentWidth-e:i.isLegendInset?a.left:0}},T.transformLegend=function(t){(t?this.legend.transition():this.legend).attr("transform",this.getTranslate("legend"))},T.updateLegendStep=function(t){this.legendStep=t},T.updateLegendItemWidth=function(t){this.legendItemWidth=t},T.updateLegendItemHeight=function(t){this.legendItemHeight=t},T.getLegendWidth=function(){var t=this;return t.config.legend_show?t.isLegendRight||t.isLegendInset?t.legendItemWidth*(t.legendStep+1):t.currentWidth:0},T.getLegendHeight=function(){var t=this,e=0;return t.config.legend_show&&(e=t.isLegendRight?t.currentHeight:Math.max(20,t.legendItemHeight)*(t.legendStep+1)),e},T.opacityForLegend=function(t){return t.classed(i.legendItemHidden)?null:1},T.opacityForUnfocusedLegend=function(t){return t.classed(i.legendItemHidden)?null:.3},T.toggleFocusLegend=function(t,e){var n=this;t=n.mapToTargetIds(t),n.legend.selectAll("."+i.legendItem).filter(function(e){return t.indexOf(e)>=0}).classed(i.legendItemFocused,e).transition().duration(100).style("opacity",function(){return(e?n.opacityForLegend:n.opacityForUnfocusedLegend).call(n,n.d3.select(this))})},T.revertLegend=function(){var t=this,e=t.d3;t.legend.selectAll("."+i.legendItem).classed(i.legendItemFocused,!1).transition().duration(100).style("opacity",function(){return t.opacityForLegend(e.select(this))})},T.showLegend=function(t){var e=this,i=e.config;i.legend_show||(i.legend_show=!0,e.legend.style("visibility","visible"),e.legendHasRendered||e.updateLegendWithDefaults()),e.removeHiddenLegendIds(t),e.legend.selectAll(e.selectorLegends(t)).style("visibility","visible").transition().style("opacity",function(){return e.opacityForLegend(e.d3.select(this))})},T.hideLegend=function(t){var e=this,i=e.config;i.legend_show&&p(t)&&(i.legend_show=!1,e.legend.style("visibility","hidden")),e.addHiddenLegendIds(t),e.legend.selectAll(e.selectorLegends(t)).style("opacity",0).style("visibility","hidden")},T.clearLegendItemTextBoxCache=function(){this.legendItemTextBox={}},T.updateLegend=function(t,e,n){var a,r,s,o,c,d,l,h,g,f,p,_,y,m,S,w,v=this,b=v.config,T=4,A=10,P=0,C=0,L=10,V=b.legend_item_tile_width+5,G=0,E={},O={},I={},R=[0],k={},D=0;function F(e,n,a){var r,s,o,c,d=0===a,l=a===t.length-1,u=(o=e,c=n,v.legendItemTextBox[c]||(v.legendItemTextBox[c]=v.getTextRect(o.textContent,i.legendItem,o)),v.legendItemTextBox[c]),h=u.width+V+(!l||v.isLegendRight||v.isLegendInset?A:0)+b.legend_padding,g=u.height+T,f=v.isLegendRight||v.isLegendInset?g:h,p=v.isLegendRight||v.isLegendInset?v.getLegendHeight():v.getLegendWidth();function _(t,e){e||(r=(p-G-f)/2)<L&&(r=(p-f)/2,G=0,D++),k[t]=D,R[D]=v.isLegendInset?10:r,E[t]=G,G+=f}d&&(G=0,D=0,P=0,C=0),!b.legend_show||v.isLegendToShow(n)?(O[n]=h,I[n]=g,(!P||h>=P)&&(P=h),(!C||g>=C)&&(C=g),s=v.isLegendRight||v.isLegendInset?C:P,b.legend_equally?(Object.keys(O).forEach(function(t){O[t]=P}),Object.keys(I).forEach(function(t){I[t]=C}),(r=(p-s*t.length)/2)<L?(G=0,D=0,t.forEach(function(t){_(t)})):_(n,!0)):_(n)):O[n]=I[n]=k[n]=E[n]=0}t=t.filter(function(t){return!u(b.data_names[t])||null!==b.data_names[t]}),p=x(e=e||{},"withTransition",!0),_=x(e,"withTransitionForTransform",!0),v.isLegendInset&&(D=b.legend_inset_step?b.legend_inset_step:t.length,v.updateLegendStep(D)),v.isLegendRight?(a=function(t){return P*k[t]},o=function(t){return R[k[t]]+E[t]}):v.isLegendInset?(a=function(t){return P*k[t]+10},o=function(t){return R[k[t]]+E[t]}):(a=function(t){return R[k[t]]+E[t]},o=function(t){return C*k[t]}),r=function(t,e){return a(t,e)+4+b.legend_item_tile_width},c=function(t,e){return o(t,e)+9},s=function(t,e){return a(t,e)},d=function(t,e){return o(t,e)-5},l=function(t,e){return a(t,e)-2},h=function(t,e){return a(t,e)-2+b.legend_item_tile_width},g=function(t,e){return o(t,e)+4},(f=v.legend.selectAll("."+i.legendItem).data(t).enter().append("g").attr("class",function(t){return v.generateClass(i.legendItem,t)}).style("visibility",function(t){return v.isLegendToShow(t)?"visible":"hidden"}).style("cursor","pointer").on("click",function(t){b.legend_item_onclick?b.legend_item_onclick.call(v,t):v.d3.event.altKey?(v.api.hide(),v.api.show(t)):(v.api.toggle(t),v.isTargetToShow(t)?v.api.focus(t):v.api.revert())}).on("mouseover",function(t){b.legend_item_onmouseover?b.legend_item_onmouseover.call(v,t):(v.d3.select(this).classed(i.legendItemFocused,!0),!v.transiting&&v.isTargetToShow(t)&&v.api.focus(t))}).on("mouseout",function(t){b.legend_item_onmouseout?b.legend_item_onmouseout.call(v,t):(v.d3.select(this).classed(i.legendItemFocused,!1),v.api.revert())})).append("text").text(function(t){return u(b.data_names[t])?b.data_names[t]:t}).each(function(t,e){F(this,t,e)}).style("pointer-events","none").attr("x",v.isLegendRight||v.isLegendInset?r:-200).attr("y",v.isLegendRight||v.isLegendInset?-200:c),f.append("rect").attr("class",i.legendItemEvent).style("fill-opacity",0).attr("x",v.isLegendRight||v.isLegendInset?s:-200).attr("y",v.isLegendRight||v.isLegendInset?-200:d),f.append("line").attr("class",i.legendItemTile).style("stroke",v.color).style("pointer-events","none").attr("x1",v.isLegendRight||v.isLegendInset?l:-200).attr("y1",v.isLegendRight||v.isLegendInset?-200:g).attr("x2",v.isLegendRight||v.isLegendInset?h:-200).attr("y2",v.isLegendRight||v.isLegendInset?-200:g).attr("stroke-width",b.legend_item_tile_height),w=v.legend.select("."+i.legendBackground+" rect"),v.isLegendInset&&P>0&&0===w.size()&&(w=v.legend.insert("g","."+i.legendItem).attr("class",i.legendBackground).append("rect")),y=v.legend.selectAll("text").data(t).text(function(t){return u(b.data_names[t])?b.data_names[t]:t}).each(function(t,e){F(this,t,e)}),(p?y.transition():y).attr("x",r).attr("y",c),m=v.legend.selectAll("rect."+i.legendItemEvent).data(t),(p?m.transition():m).attr("width",function(t){return O[t]}).attr("height",function(t){return I[t]}).attr("x",s).attr("y",d),S=v.legend.selectAll("line."+i.legendItemTile).data(t),(p?S.transition():S).style("stroke",v.levelColor?function(t){return v.levelColor(v.cache[t].values[0].value)}:v.color).attr("x1",l).attr("y1",g).attr("x2",h).attr("y2",g),w&&(p?w.transition():w).attr("height",v.getLegendHeight()-12).attr("width",P*(D+1)+10),v.legend.selectAll("."+i.legendItem).classed(i.legendItemHidden,function(t){return!v.isTargetToShow(t)}),v.updateLegendItemWidth(P),v.updateLegendItemHeight(C),v.updateLegendStep(D),v.updateSizes(),v.updateScales(),v.updateSvgSize(),v.transformAll(_,n),v.legendHasRendered=!0},T.initRegion=function(){this.region=this.main.append("g").attr("clip-path",this.clipPath).attr("class",i.regions)},T.updateRegion=function(t){var e=this,n=e.config;e.region.style("visibility",e.hasArcType()?"hidden":"visible"),e.mainRegion=e.main.select("."+i.regions).selectAll("."+i.region).data(n.regions),e.mainRegion.enter().append("g").append("rect").style("fill-opacity",0),e.mainRegion.attr("class",e.classRegion.bind(e)),e.mainRegion.exit().transition().duration(t).style("opacity",0).remove()},T.redrawRegion=function(t){var e=this,i=e.mainRegion.selectAll("rect").each(function(){var t=e.d3.select(this.parentNode).datum();e.d3.select(this).datum(t)}),n=e.regionX.bind(e),a=e.regionY.bind(e),r=e.regionWidth.bind(e),o=e.regionHeight.bind(e);return[(t?i.transition():i).attr("x",n).attr("y",a).attr("width",r).attr("height",o).style("fill-opacity",function(t){return s(t.opacity)?t.opacity:.1})]},T.regionX=function(t){var e=this,i=e.config,n="y"===t.axis?e.y:e.y2;return"y"===t.axis||"y2"===t.axis?i.axis_rotated&&"start"in t?n(t.start):0:i.axis_rotated?0:"start"in t?e.x(e.isTimeSeries()?e.parseDate(t.start):t.start):0},T.regionY=function(t){var e=this,i=e.config,n="y"===t.axis?e.y:e.y2;return"y"===t.axis||"y2"===t.axis?i.axis_rotated?0:"end"in t?n(t.end):0:i.axis_rotated&&"start"in t?e.x(e.isTimeSeries()?e.parseDate(t.start):t.start):0},T.regionWidth=function(t){var e,i=this,n=i.config,a=i.regionX(t),r="y"===t.axis?i.y:i.y2;return(e="y"===t.axis||"y2"===t.axis?n.axis_rotated&&"end"in t?r(t.end):i.width:n.axis_rotated?i.width:"end"in t?i.x(i.isTimeSeries()?i.parseDate(t.end):t.end):i.width)<a?0:e-a},T.regionHeight=function(t){var e,i=this,n=i.config,a=this.regionY(t),r="y"===t.axis?i.y:i.y2;return(e="y"===t.axis||"y2"===t.axis?n.axis_rotated?i.height:"start"in t?r(t.start):i.height:n.axis_rotated&&"end"in t?i.x(i.isTimeSeries()?i.parseDate(t.end):t.end):i.height)<a?0:e-a},T.isRegionOnX=function(t){return!t.axis||"x"===t.axis},T.getScale=function(t,e,i){return(i?this.d3.time.scale():this.d3.scale.linear()).range([t,e])},T.getX=function(t,e,i,n){var a,r=this.getScale(t,e,this.isTimeSeries()),s=i?r.domain(i):r;for(a in this.isCategorized()?(n=n||function(){return 0},r=function(t,e){var i=s(t)+n(t);return e?i:Math.ceil(i)}):r=function(t,e){var i=s(t);return e?i:Math.ceil(i)},s)r[a]=s[a];return r.orgDomain=function(){return s.domain()},this.isCategorized()&&(r.domain=function(t){return arguments.length?(s.domain(t),r):[(t=this.orgDomain())[0],t[1]+1]}),r},T.getY=function(t,e,i){var n=this.getScale(t,e,this.isTimeSeriesY());return i&&n.domain(i),n},T.getYScale=function(t){return"y2"===this.axis.getId(t)?this.y2:this.y},T.getSubYScale=function(t){return"y2"===this.axis.getId(t)?this.subY2:this.subY},T.updateScales=function(){var t=this,e=t.config,i=!t.x;t.xMin=e.axis_rotated?1:0,t.xMax=e.axis_rotated?t.height:t.width,t.yMin=e.axis_rotated?0:t.height,t.yMax=e.axis_rotated?t.width:1,t.subXMin=t.xMin,t.subXMax=t.xMax,t.subYMin=e.axis_rotated?0:t.height2,t.subYMax=e.axis_rotated?t.width2:1,t.x=t.getX(t.xMin,t.xMax,i?void 0:t.x.orgDomain(),function(){return t.xAxis.tickOffset()}),t.y=t.getY(t.yMin,t.yMax,i?e.axis_y_default:t.y.domain()),t.y2=t.getY(t.yMin,t.yMax,i?e.axis_y2_default:t.y2.domain()),t.subX=t.getX(t.xMin,t.xMax,t.orgXDomain,function(e){return e%1?0:t.subXAxis.tickOffset()}),t.subY=t.getY(t.subYMin,t.subYMax,i?e.axis_y_default:t.subY.domain()),t.subY2=t.getY(t.subYMin,t.subYMax,i?e.axis_y2_default:t.subY2.domain()),t.xAxisTickFormat=t.axis.getXAxisTickFormat(),t.xAxisTickValues=t.axis.getXAxisTickValues(),t.yAxisTickValues=t.axis.getYAxisTickValues(),t.y2AxisTickValues=t.axis.getY2AxisTickValues(),t.xAxis=t.axis.getXAxis(t.x,t.xOrient,t.xAxisTickFormat,t.xAxisTickValues,e.axis_x_tick_outer),t.subXAxis=t.axis.getXAxis(t.subX,t.subXOrient,t.xAxisTickFormat,t.xAxisTickValues,e.axis_x_tick_outer),t.yAxis=t.axis.getYAxis(t.y,t.yOrient,e.axis_y_tick_format,t.yAxisTickValues,e.axis_y_tick_outer),t.y2Axis=t.axis.getYAxis(t.y2,t.y2Orient,e.axis_y2_tick_format,t.y2AxisTickValues,e.axis_y2_tick_outer),i||(t.brush&&t.brush.scale(t.subX),e.zoom_enabled&&t.zoom.scale(t.x)),t.updateArc&&t.updateArc()},T.selectPoint=function(t,e,n){var a=this,r=a.config,s=(r.axis_rotated?a.circleY:a.circleX).bind(a),o=(r.axis_rotated?a.circleX:a.circleY).bind(a),c=a.pointSelectR.bind(a);r.data_onselected.call(a.api,e,t.node()),a.main.select("."+i.selectedCircles+a.getTargetSelectorSuffix(e.id)).selectAll("."+i.selectedCircle+"-"+n).data([e]).enter().append("circle").attr("class",function(){return a.generateClass(i.selectedCircle,n)}).attr("cx",s).attr("cy",o).attr("stroke",function(){return a.color(e)}).attr("r",function(t){return 1.4*a.pointSelectR(t)}).transition().duration(100).attr("r",c)},T.unselectPoint=function(t,e,n){this.config.data_onunselected.call(this.api,e,t.node()),this.main.select("."+i.selectedCircles+this.getTargetSelectorSuffix(e.id)).selectAll("."+i.selectedCircle+"-"+n).transition().duration(100).attr("r",0).remove()},T.togglePoint=function(t,e,i,n){t?this.selectPoint(e,i,n):this.unselectPoint(e,i,n)},T.selectPath=function(t,e){var i=this;i.config.data_onselected.call(i,e,t.node()),i.config.interaction_brighten&&t.transition().duration(100).style("fill",function(){return i.d3.rgb(i.color(e)).brighter(.75)})},T.unselectPath=function(t,e){var i=this;i.config.data_onunselected.call(i,e,t.node()),i.config.interaction_brighten&&t.transition().duration(100).style("fill",function(){return i.color(e)})},T.togglePath=function(t,e,i,n){t?this.selectPath(e,i,n):this.unselectPath(e,i,n)},T.getToggle=function(t,e){var i;return"circle"===t.nodeName?i=this.isStepType(e)?function(){}:this.togglePoint:"path"===t.nodeName&&(i=this.togglePath),i},T.toggleShape=function(t,e,n){var a=this,r=a.d3,s=a.config,o=r.select(t),c=o.classed(i.SELECTED),d=a.getToggle(t,e).bind(a);s.data_selection_enabled&&s.data_selection_isselectable(e)&&(s.data_selection_multiple||a.main.selectAll("."+i.shapes+(s.data_selection_grouped?a.getTargetSelectorSuffix(e.id):"")).selectAll("."+i.shape).each(function(t,e){var n=r.select(this);n.classed(i.SELECTED)&&d(!1,n.classed(i.SELECTED,!1),t,e)}),o.classed(i.SELECTED,!c),d(!c,o,e,n))},T.initBar=function(){this.main.select("."+i.chart).append("g").attr("class",i.chartBars)},T.updateTargetsForBar=function(t){var e=this,n=e.config,a=e.classChartBar.bind(e),r=e.classBars.bind(e),s=e.classFocus.bind(e);e.main.select("."+i.chartBars).selectAll("."+i.chartBar).data(t).attr("class",function(t){return a(t)+s(t)}).enter().append("g").attr("class",a).style("pointer-events","none").append("g").attr("class",r).style("cursor",function(t){return n.data_selection_isselectable(t)?"pointer":null})},T.updateBar=function(t){var e=this,n=e.barData.bind(e),a=e.classBar.bind(e),r=e.initialOpacity.bind(e),s=function(t){return e.color(t.id)};e.mainBar=e.main.selectAll("."+i.bars).selectAll("."+i.bar).data(n),e.mainBar.enter().append("path").attr("class",a).style("stroke",s).style("fill",s),e.mainBar.style("opacity",r),e.mainBar.exit().transition().duration(t).remove()},T.redrawBar=function(t,e){return[(e?this.mainBar.transition(Math.random().toString()):this.mainBar).attr("d",t).style("stroke",this.color).style("fill",this.color).style("opacity",1)]},T.getBarW=function(t,e){var i=this.config,n="number"==typeof i.bar_width?i.bar_width:e?t.tickInterval()*i.bar_width_ratio/e:0;return i.bar_width_max&&n>i.bar_width_max?i.bar_width_max:n},T.getBars=function(t,e){return(e?this.main.selectAll("."+i.bars+this.getTargetSelectorSuffix(e)):this.main).selectAll("."+i.bar+(s(t)?"-"+t:""))},T.expandBars=function(t,e,n){n&&this.unexpandBars(),this.getBars(t,e).classed(i.EXPANDED,!0)},T.unexpandBars=function(t){this.getBars(t).classed(i.EXPANDED,!1)},T.generateDrawBar=function(t,e){var i=this.config,n=this.generateGetBarPoints(t,e);return function(t,e){var a=n(t,e),r=i.axis_rotated?1:0,s=i.axis_rotated?0:1;return"M "+a[0][r]+","+a[0][s]+" L"+a[1][r]+","+a[1][s]+" L"+a[2][r]+","+a[2][s]+" L"+a[3][r]+","+a[3][s]+" z"}},T.generateGetBarPoints=function(t,e){var i=this,n=e?i.subXAxis:i.xAxis,a=t.__max__+1,r=i.getBarW(n,a),s=i.getShapeX(r,a,t,!!e),o=i.getShapeY(!!e),c=i.getShapeOffset(i.isBarType,t,!!e),d=r*(i.config.bar_space/2),l=e?i.getSubYScale:i.getYScale;return function(t,e){var n=l.call(i,t.id)(0),a=c(t,e)||n,u=s(t),h=o(t);return i.config.axis_rotated&&(0<t.value&&h<n||t.value<0&&n<h)&&(h=n),[[u+d,a],[u+d,h-(n-a)],[u+r-d,h-(n-a)],[u+r-d,a]]}},T.isWithinBar=function(t){var e=this.d3.mouse(t),i=t.getBoundingClientRect(),n=t.pathSegList.getItem(0),a=t.pathSegList.getItem(1),r=Math.min(n.x,a.x),s=Math.min(n.y,a.y),o=r+i.width+2,c=s+i.height+2,d=s-2;return r-2<e[0]&&e[0]<o&&d<e[1]&&e[1]<c},T.getShapeIndices=function(t){var e,i,n=this.config,a={},r=0;return this.filterTargetsToShow(this.data.targets.filter(t,this)).forEach(function(t){for(e=0;e<n.data_groups.length;e++)if(!(n.data_groups[e].indexOf(t.id)<0))for(i=0;i<n.data_groups[e].length;i++)if(n.data_groups[e][i]in a){a[t.id]=a[n.data_groups[e][i]];break}l(a[t.id])&&(a[t.id]=r++)}),a.__max__=r-1,a},T.getShapeX=function(t,e,i,n){var a=n?this.subX:this.x;return function(n){var r=n.id in i?i[n.id]:0;return n.x||0===n.x?a(n.x)-t*(e/2-r):0}},T.getShapeY=function(t){var e=this;return function(i){return(t?e.getSubYScale(i.id):e.getYScale(i.id))(i.value)}},T.getShapeOffset=function(t,e,i){var n=this,a=n.orderTargets(n.filterTargetsToShow(n.data.targets.filter(t,n))),r=a.map(function(t){return t.id});return function(t,s){var o=i?n.getSubYScale(t.id):n.getYScale(t.id),c=o(0),d=c;return a.forEach(function(i){var a=n.isStepType(t)?n.convertValuesToStep(i.values):i.values;i.id!==t.id&&e[i.id]===e[t.id]&&r.indexOf(i.id)<r.indexOf(t.id)&&(void 0!==a[s]&&+a[s].x==+t.x||(s=-1,a.forEach(function(e,i){e.x===t.x&&(s=i)})),s in a&&a[s].value*t.value>=0&&(d+=o(a[s].value)-c))}),d}},T.isWithinShape=function(t,e){var n,a=this,r=a.d3.select(t);return a.isTargetToShow(e.id)?"circle"===t.nodeName?n=a.isStepType(e)?a.isWithinStep(t,a.getYScale(e.id)(e.value)):a.isWithinCircle(t,1.5*a.pointSelectR(e)):"path"===t.nodeName&&(n=!r.classed(i.bar)||a.isWithinBar(t)):n=!1,n},T.getInterpolate=function(t){var e=this,i=e.isInterpolationType(e.config.spline_interpolation_type)?e.config.spline_interpolation_type:"cardinal";return e.isSplineType(t)?i:e.isStepType(t)?e.config.line_step_type:"linear"},T.initLine=function(){this.main.select("."+i.chart).append("g").attr("class",i.chartLines)},T.updateTargetsForLine=function(t){var e,n=this,a=n.config,r=n.classChartLine.bind(n),s=n.classLines.bind(n),o=n.classAreas.bind(n),c=n.classCircles.bind(n),d=n.classFocus.bind(n);(e=n.main.select("."+i.chartLines).selectAll("."+i.chartLine).data(t).attr("class",function(t){return r(t)+d(t)}).enter().append("g").attr("class",r).style("opacity",0).style("pointer-events","none")).append("g").attr("class",s),e.append("g").attr("class",o),e.append("g").attr("class",function(t){return n.generateClass(i.selectedCircles,t.id)}),e.append("g").attr("class",c).style("cursor",function(t){return a.data_selection_isselectable(t)?"pointer":null}),t.forEach(function(t){n.main.selectAll("."+i.selectedCircles+n.getTargetSelectorSuffix(t.id)).selectAll("."+i.selectedCircle).each(function(e){e.value=t.values[e.index].value})})},T.updateLine=function(t){var e=this;e.mainLine=e.main.selectAll("."+i.lines).selectAll("."+i.line).data(e.lineData.bind(e)),e.mainLine.enter().append("path").attr("class",e.classLine.bind(e)).style("stroke",e.color),e.mainLine.style("opacity",e.initialOpacity.bind(e)).style("shape-rendering",function(t){return e.isStepType(t)?"crispEdges":""}).attr("transform",null),e.mainLine.exit().transition().duration(t).style("opacity",0).remove()},T.redrawLine=function(t,e){return[(e?this.mainLine.transition(Math.random().toString()):this.mainLine).attr("d",t).style("stroke",this.color).style("opacity",1)]},T.generateDrawLine=function(t,e){var i=this,n=i.config,a=i.d3.svg.line(),r=i.generateGetLinePoints(t,e),s=e?i.getSubYScale:i.getYScale,o=function(t){return(e?i.subxx:i.xx).call(i,t)},c=function(t,e){return n.data_groups.length>0?r(t,e)[0][1]:s.call(i,t.id)(t.value)};return a=n.axis_rotated?a.x(c).y(o):a.x(o).y(c),n.line_connectNull||(a=a.defined(function(t){return null!=t.value})),function(t){var r,o=n.line_connectNull?i.filterRemoveNull(t.values):t.values,c=e?i.x:i.subX,d=s.call(i,t.id),l=0,u=0;return i.isLineType(t)?n.data_regions[t.id]?r=i.lineWithRegions(o,c,d,n.data_regions[t.id]):(i.isStepType(t)&&(o=i.convertValuesToStep(o)),r=a.interpolate(i.getInterpolate(t))(o)):(o[0]&&(l=c(o[0].x),u=d(o[0].value)),r=n.axis_rotated?"M "+u+" "+l:"M "+l+" "+u),r||"M 0 0"}},T.generateGetLinePoints=function(t,e){var i=this,n=i.config,a=t.__max__+1,r=i.getShapeX(0,a,t,!!e),s=i.getShapeY(!!e),o=i.getShapeOffset(i.isLineType,t,!!e),c=e?i.getSubYScale:i.getYScale;return function(t,e){var a=c.call(i,t.id)(0),d=o(t,e)||a,l=r(t),u=s(t);return n.axis_rotated&&(0<t.value&&u<a||t.value<0&&a<u)&&(u=a),[[l,u-(a-d)],[l,u-(a-d)],[l,u-(a-d)],[l,u-(a-d)]]}},T.lineWithRegions=function(t,e,i,n){var a,r,s,o,c,d,h,g,f,p,_,x=this,y=x.config,m="M",S=x.isCategorized()?.5:0,w=[];function v(t,e){var i;for(i=0;i<e.length;i++)if(e[i].start<t&&t<=e[i].end)return!0;return!1}if(u(n))for(a=0;a<n.length;a++)w[a]={},l(n[a].start)?w[a].start=t[0].x:w[a].start=x.isTimeSeries()?x.parseDate(n[a].start):n[a].start,l(n[a].end)?w[a].end=t[t.length-1].x:w[a].end=x.isTimeSeries()?x.parseDate(n[a].end):n[a].end;function b(t){return"M"+t[0][0]+" "+t[0][1]+" "+t[1][0]+" "+t[1][1]}for(p=y.axis_rotated?function(t){return i(t.value)}:function(t){return e(t.x)},_=y.axis_rotated?function(t){return e(t.x)}:function(t){return i(t.value)},s=x.isTimeSeries()?function(t,n,a,r){var s=t.x.getTime(),o=n.x-t.x,d=new Date(s+o*a),l=new Date(s+o*(a+r));return b(y.axis_rotated?[[i(c(a)),e(d)],[i(c(a+r)),e(l)]]:[[e(d),i(c(a))],[e(l),i(c(a+r))]])}:function(t,n,a,r){return b(y.axis_rotated?[[i(c(a),!0),e(o(a))],[i(c(a+r),!0),e(o(a+r))]]:[[e(o(a),!0),i(c(a))],[e(o(a+r),!0),i(c(a+r))]])},a=0;a<t.length;a++){if(l(w)||!v(t[a].x,w))m+=" "+p(t[a])+" "+_(t[a]);else for(o=x.getScale(t[a-1].x+S,t[a].x+S,x.isTimeSeries()),c=x.getScale(t[a-1].value,t[a].value),d=e(t[a].x)-e(t[a-1].x),h=i(t[a].value)-i(t[a-1].value),f=2*(g=2/Math.sqrt(Math.pow(d,2)+Math.pow(h,2))),r=g;r<=1;r+=f)m+=s(t[a-1],t[a],r,g);t[a].x}return m},T.updateArea=function(t){var e=this,n=e.d3;e.mainArea=e.main.selectAll("."+i.areas).selectAll("."+i.area).data(e.lineData.bind(e)),e.mainArea.enter().append("path").attr("class",e.classArea.bind(e)).style("fill",e.color).style("opacity",function(){return e.orgAreaOpacity=+n.select(this).style("opacity"),0}),e.mainArea.style("opacity",e.orgAreaOpacity),e.mainArea.exit().transition().duration(t).style("opacity",0).remove()},T.redrawArea=function(t,e){return[(e?this.mainArea.transition(Math.random().toString()):this.mainArea).attr("d",t).style("fill",this.color).style("opacity",this.orgAreaOpacity)]},T.generateDrawArea=function(t,e){var i=this,n=i.config,a=i.d3.svg.area(),r=i.generateGetAreaPoints(t,e),s=e?i.getSubYScale:i.getYScale,o=function(t){return(e?i.subxx:i.xx).call(i,t)},c=function(t,e){return n.data_groups.length>0?r(t,e)[0][1]:s.call(i,t.id)(i.getAreaBaseValue(t.id))},d=function(t,e){return n.data_groups.length>0?r(t,e)[1][1]:s.call(i,t.id)(t.value)};return a=n.axis_rotated?a.x0(c).x1(d).y(o):a.x(o).y0(n.area_above?0:c).y1(d),n.line_connectNull||(a=a.defined(function(t){return null!==t.value})),function(t){var e,r=n.line_connectNull?i.filterRemoveNull(t.values):t.values,s=0,o=0;return i.isAreaType(t)?(i.isStepType(t)&&(r=i.convertValuesToStep(r)),e=a.interpolate(i.getInterpolate(t))(r)):(r[0]&&(s=i.x(r[0].x),o=i.getYScale(t.id)(r[0].value)),e=n.axis_rotated?"M "+o+" "+s:"M "+s+" "+o),e||"M 0 0"}},T.getAreaBaseValue=function(){return 0},T.generateGetAreaPoints=function(t,e){var i=this,n=i.config,a=t.__max__+1,r=i.getShapeX(0,a,t,!!e),s=i.getShapeY(!!e),o=i.getShapeOffset(i.isAreaType,t,!!e),c=e?i.getSubYScale:i.getYScale;return function(t,e){var a=c.call(i,t.id)(0),d=o(t,e)||a,l=r(t),u=s(t);return n.axis_rotated&&(0<t.value&&u<a||t.value<0&&a<u)&&(u=a),[[l,d],[l,u-(a-d)],[l,u-(a-d)],[l,d]]}},T.updateCircle=function(){var t=this;t.mainCircle=t.main.selectAll("."+i.circles).selectAll("."+i.circle).data(t.lineOrScatterData.bind(t)),t.mainCircle.enter().append("circle").attr("class",t.classCircle.bind(t)).attr("r",t.pointR.bind(t)).style("fill",t.color),t.mainCircle.style("opacity",t.initialOpacityForCircle.bind(t)),t.mainCircle.exit().remove()},T.redrawCircle=function(t,e,n){var a=this.main.selectAll("."+i.selectedCircle);return[(n?this.mainCircle.transition(Math.random().toString()):this.mainCircle).style("opacity",this.opacityForCircle.bind(this)).style("fill",this.color).attr("cx",t).attr("cy",e),(n?a.transition(Math.random().toString()):a).attr("cx",t).attr("cy",e)]},T.circleX=function(t){return t.x||0===t.x?this.x(t.x):null},T.updateCircleY=function(){var t,e,i=this;i.config.data_groups.length>0?(t=i.getShapeIndices(i.isLineType),e=i.generateGetLinePoints(t),i.circleY=function(t,i){return e(t,i)[0][1]}):i.circleY=function(t){return i.getYScale(t.id)(t.value)}},T.getCircles=function(t,e){return(e?this.main.selectAll("."+i.circles+this.getTargetSelectorSuffix(e)):this.main).selectAll("."+i.circle+(s(t)?"-"+t:""))},T.expandCircles=function(t,e,n){var a=this.pointExpandedR.bind(this);n&&this.unexpandCircles(),this.getCircles(t,e).classed(i.EXPANDED,!0).attr("r",a)},T.unexpandCircles=function(t){var e=this,n=e.pointR.bind(e);e.getCircles(t).filter(function(){return e.d3.select(this).classed(i.EXPANDED)}).classed(i.EXPANDED,!1).attr("r",n)},T.pointR=function(t){var e=this.config;return this.isStepType(t)?0:o(e.point_r)?e.point_r(t):e.point_r},T.pointExpandedR=function(t){var e=this.config;return e.point_focus_expand_enabled?o(e.point_focus_expand_r)?e.point_focus_expand_r(t):e.point_focus_expand_r?e.point_focus_expand_r:1.75*this.pointR(t):this.pointR(t)},T.pointSelectR=function(t){var e=this.config;return o(e.point_select_r)?e.point_select_r(t):e.point_select_r?e.point_select_r:4*this.pointR(t)},T.isWithinCircle=function(t,e){var i=this.d3,n=i.mouse(t),a=i.select(t),r=+a.attr("cx"),s=+a.attr("cy");return Math.sqrt(Math.pow(r-n[0],2)+Math.pow(s-n[1],2))<e},T.isWithinStep=function(t,e){return Math.abs(e-this.d3.mouse(t)[1])<30},T.getCurrentWidth=function(){var t=this.config;return t.size_width?t.size_width:this.getParentWidth()},T.getCurrentHeight=function(){var t=this.config,e=t.size_height?t.size_height:this.getParentHeight();return e>0?e:320/(this.hasType("gauge")&&!t.gauge_fullCircle?2:1)},T.getCurrentPaddingTop=function(){var t=this.config,e=s(t.padding_top)?t.padding_top:0;return this.title&&this.title.node()&&(e+=this.getTitlePadding()),e},T.getCurrentPaddingBottom=function(){var t=this.config;return s(t.padding_bottom)?t.padding_bottom:0},T.getCurrentPaddingLeft=function(t){var e=this.config;return s(e.padding_left)?e.padding_left:e.axis_rotated?e.axis_x_show?Math.max(h(this.getAxisWidthByAxisId("x",t)),40):1:!e.axis_y_show||e.axis_y_inner?this.axis.getYAxisLabelPosition().isOuter?30:1:h(this.getAxisWidthByAxisId("y",t))},T.getCurrentPaddingRight=function(){var t=this,e=t.config,i=t.isLegendRight?t.getLegendWidth()+20:0;return s(e.padding_right)?e.padding_right+1:e.axis_rotated?10+i:!e.axis_y2_show||e.axis_y2_inner?2+i+(t.axis.getY2AxisLabelPosition().isOuter?20:0):h(t.getAxisWidthByAxisId("y2"))+i},T.getParentRectValue=function(t){for(var e,i=this.selectChart.node();i&&"BODY"!==i.tagName;){try{e=i.getBoundingClientRect()[t]}catch(n){"width"===t&&(e=i.offsetWidth)}if(e)break;i=i.parentNode}return e},T.getParentWidth=function(){return this.getParentRectValue("width")},T.getParentHeight=function(){var t=this.selectChart.style("height");return t.indexOf("px")>0?+t.replace("px",""):0},T.getSvgLeft=function(t){var e=this,n=e.config,a=n.axis_rotated||!n.axis_rotated&&!n.axis_y_inner,r=n.axis_rotated?i.axisX:i.axisY,s=e.main.select("."+r).node(),o=s&&a?s.getBoundingClientRect():{right:0},c=e.selectChart.node().getBoundingClientRect(),d=e.hasArcType(),l=o.right-c.left-(d?0:e.getCurrentPaddingLeft(t));return l>0?l:0},T.getAxisWidthByAxisId=function(t,e){var i=this.axis.getLabelPositionById(t);return this.axis.getMaxTickWidth(t,e)+(i.isInner?20:40)},T.getHorizontalAxisHeight=function(t){var e=this,i=e.config,n=30;return"x"!==t||i.axis_x_show?"x"===t&&i.axis_x_height?i.axis_x_height:"y"!==t||i.axis_y_show?"y2"!==t||i.axis_y2_show?("x"===t&&!i.axis_rotated&&i.axis_x_tick_rotate&&(n=30+e.axis.getMaxTickWidth(t)*Math.cos(Math.PI*(90-i.axis_x_tick_rotate)/180)),"y"===t&&i.axis_rotated&&i.axis_y_tick_rotate&&(n=30+e.axis.getMaxTickWidth(t)*Math.cos(Math.PI*(90-i.axis_y_tick_rotate)/180)),n+(e.axis.getLabelPositionById(t).isInner?0:10)+("y2"===t?-10:0)):e.rotated_padding_top:!i.legend_show||e.isLegendRight||e.isLegendInset?1:10:8},T.getEventRectWidth=function(){return Math.max(0,this.xAxis.tickInterval())},T.initBrush=function(){var t=this,e=t.d3;t.brush=e.svg.brush().on("brush",function(){t.redrawForBrush()}),t.brush.update=function(){return t.context&&t.context.select("."+i.brush).call(this),this},t.brush.scale=function(e){return t.config.axis_rotated?this.y(e):this.x(e)}},T.initSubchart=function(){var t=this,e=t.config,n=t.context=t.svg.append("g").attr("transform",t.getTranslate("context")),a=e.subchart_show?"visible":"hidden";n.style("visibility",a),n.append("g").attr("clip-path",t.clipPathForSubchart).attr("class",i.chart),n.select("."+i.chart).append("g").attr("class",i.chartBars),n.select("."+i.chart).append("g").attr("class",i.chartLines),n.append("g").attr("clip-path",t.clipPath).attr("class",i.brush).call(t.brush),t.axes.subx=n.append("g").attr("class",i.axisX).attr("transform",t.getTranslate("subx")).attr("clip-path",e.axis_rotated?"":t.clipPathForXAxis).style("visibility",e.subchart_axis_x_show?a:"hidden")},T.updateTargetsForSubchart=function(t){var e,n=this,a=n.context,r=n.config,s=n.classChartBar.bind(n),o=n.classBars.bind(n),c=n.classChartLine.bind(n),d=n.classLines.bind(n),l=n.classAreas.bind(n);r.subchart_show&&(a.select("."+i.chartBars).selectAll("."+i.chartBar).data(t).attr("class",s).enter().append("g").style("opacity",0).attr("class",s).append("g").attr("class",o),(e=a.select("."+i.chartLines).selectAll("."+i.chartLine).data(t).attr("class",c).enter().append("g").style("opacity",0).attr("class",c)).append("g").attr("class",d),e.append("g").attr("class",l),a.selectAll("."+i.brush+" rect").attr(r.axis_rotated?"width":"height",r.axis_rotated?n.width2:n.height2))},T.updateBarForSubchart=function(t){var e=this;e.contextBar=e.context.selectAll("."+i.bars).selectAll("."+i.bar).data(e.barData.bind(e)),e.contextBar.enter().append("path").attr("class",e.classBar.bind(e)).style("stroke","none").style("fill",e.color),e.contextBar.style("opacity",e.initialOpacity.bind(e)),e.contextBar.exit().transition().duration(t).style("opacity",0).remove()},T.redrawBarForSubchart=function(t,e,i){(e?this.contextBar.transition(Math.random().toString()).duration(i):this.contextBar).attr("d",t).style("opacity",1)},T.updateLineForSubchart=function(t){var e=this;e.contextLine=e.context.selectAll("."+i.lines).selectAll("."+i.line).data(e.lineData.bind(e)),e.contextLine.enter().append("path").attr("class",e.classLine.bind(e)).style("stroke",e.color),e.contextLine.style("opacity",e.initialOpacity.bind(e)),e.contextLine.exit().transition().duration(t).style("opacity",0).remove()},T.redrawLineForSubchart=function(t,e,i){(e?this.contextLine.transition(Math.random().toString()).duration(i):this.contextLine).attr("d",t).style("opacity",1)},T.updateAreaForSubchart=function(t){var e=this,n=e.d3;e.contextArea=e.context.selectAll("."+i.areas).selectAll("."+i.area).data(e.lineData.bind(e)),e.contextArea.enter().append("path").attr("class",e.classArea.bind(e)).style("fill",e.color).style("opacity",function(){return e.orgAreaOpacity=+n.select(this).style("opacity"),0}),e.contextArea.style("opacity",0),e.contextArea.exit().transition().duration(t).style("opacity",0).remove()},T.redrawAreaForSubchart=function(t,e,i){(e?this.contextArea.transition(Math.random().toString()).duration(i):this.contextArea).attr("d",t).style("fill",this.color).style("opacity",this.orgAreaOpacity)},T.redrawSubchart=function(t,e,i,n,a,r,s){var o,c,d,l=this,u=l.d3,h=l.config;l.context.style("visibility",h.subchart_show?"visible":"hidden"),h.subchart_show&&(u.event&&"zoom"===u.event.type&&l.brush.extent(l.x.orgDomain()).update(),t&&(l.brush.empty()||l.brush.extent(l.x.orgDomain()).update(),o=l.generateDrawArea(a,!0),c=l.generateDrawBar(r,!0),d=l.generateDrawLine(s,!0),l.updateBarForSubchart(i),l.updateLineForSubchart(i),l.updateAreaForSubchart(i),l.redrawBarForSubchart(c,i,i),l.redrawLineForSubchart(d,i,i),l.redrawAreaForSubchart(o,i,i)))},T.redrawForBrush=function(){var t=this,e=t.x;t.redraw({withTransition:!1,withY:t.config.zoom_rescale,withSubchart:!1,withUpdateXDomain:!0,withDimension:!1}),t.config.subchart_onbrush.call(t.api,e.orgDomain())},T.transformContext=function(t,e){var n;e&&e.axisSubX?n=e.axisSubX:(n=this.context.select("."+i.axisX),t&&(n=n.transition())),this.context.attr("transform",this.getTranslate("context")),n.attr("transform",this.getTranslate("subx"))},T.getDefaultExtent=function(){var t=this,e=t.config,i=o(e.axis_x_extent)?e.axis_x_extent(t.getXDomain(t.data.targets)):e.axis_x_extent;return t.isTimeSeries()&&(i=[t.parseDate(i[0]),t.parseDate(i[1])]),i},T.initText=function(){this.main.select("."+i.chart).append("g").attr("class",i.chartTexts),this.mainText=this.d3.selectAll([])},T.updateTargetsForText=function(t){var e=this,n=e.classChartText.bind(e),a=e.classTexts.bind(e),r=e.classFocus.bind(e);e.main.select("."+i.chartTexts).selectAll("."+i.chartText).data(t).attr("class",function(t){return n(t)+r(t)}).enter().append("g").attr("class",n).style("opacity",0).style("pointer-events","none").append("g").attr("class",a)},T.updateText=function(t){var e=this,n=e.config,a=e.barOrLineData.bind(e),r=e.classText.bind(e);e.mainText=e.main.selectAll("."+i.texts).selectAll("."+i.text).data(a),e.mainText.enter().append("text").attr("class",r).attr("text-anchor",function(t){return n.axis_rotated?t.value<0?"end":"start":"middle"}).style("stroke","none").style("fill",function(t){return e.color(t)}).style("fill-opacity",0),e.mainText.text(function(t,i,n){return e.dataLabelFormat(t.id)(t.value,t.id,i,n)}),e.mainText.exit().transition().duration(t).style("fill-opacity",0).remove()},T.redrawText=function(t,e,i,n){return[(n?this.mainText.transition():this.mainText).attr("x",t).attr("y",e).style("fill",this.color).style("fill-opacity",i?0:this.opacityForText.bind(this))]},T.getTextRect=function(t,e,i){var n,a=this.d3.select("body").append("div").classed("c3",!0),r=a.append("svg").style("visibility","hidden").style("position","fixed").style("top",0).style("left",0),s=this.d3.select(i).style("font");return r.selectAll(".dummy").data([t]).enter().append("text").classed(e||"",!0).style("font",s).text(t).each(function(){n=this.getBoundingClientRect()}),a.remove(),n},T.generateXYForText=function(t,e,i,n){var a=this,r=a.generateGetAreaPoints(t,!1),s=a.generateGetBarPoints(e,!1),o=a.generateGetLinePoints(i,!1),c=n?a.getXForText:a.getYForText;return function(t,e){var i=a.isAreaType(t)?r:a.isBarType(t)?s:o;return c.call(a,i(t,e),t,this)}},T.getXForText=function(t,e,i){var n,a,r=this,s=i.getBoundingClientRect();return r.config.axis_rotated?(a=r.isBarType(e)?4:6,n=t[2][1]+a*(e.value<0?-1:1)):n=r.hasType("bar")?(t[2][0]+t[0][0])/2:t[0][0],null===e.value&&(n>r.width?n=r.width-s.width:n<0&&(n=4)),n},T.getYForText=function(t,e,i){var n,a=this,r=i.getBoundingClientRect();return a.config.axis_rotated?n=(t[0][0]+t[2][0]+.6*r.height)/2:(n=t[2][1],e.value<0||0===e.value&&!a.hasPositiveValue?(n+=r.height,a.isBarType(e)&&a.isSafari()?n-=3:!a.isBarType(e)&&a.isChrome()&&(n+=3)):n+=a.isBarType(e)?-3:-6),null!==e.value||a.config.axis_rotated||(n<r.height?n=r.height:n>this.height&&(n=this.height-4)),n},T.initTitle=function(){this.title=this.svg.append("text").text(this.config.title_text).attr("class",this.CLASS.title)},T.redrawTitle=function(){var t=this;t.title.attr("x",t.xForTitle.bind(t)).attr("y",t.yForTitle.bind(t))},T.xForTitle=function(){var t=this,e=t.config,i=e.title_position||"left";return i.indexOf("right")>=0?t.currentWidth-t.getTextRect(t.title.node().textContent,t.CLASS.title,t.title.node()).width-e.title_padding.right:i.indexOf("center")>=0?(t.currentWidth-t.getTextRect(t.title.node().textContent,t.CLASS.title,t.title.node()).width)/2:e.title_padding.left},T.yForTitle=function(){var t=this;return t.config.title_padding.top+t.getTextRect(t.title.node().textContent,t.CLASS.title,t.title.node()).height},T.getTitlePadding=function(){return this.yForTitle()+this.config.title_padding.bottom},T.initTooltip=function(){var t,e=this,n=e.config;if(e.tooltip=e.selectChart.style("position","relative").append("div").attr("class",i.tooltipContainer).style("position","absolute").style("pointer-events","none").style("display","none"),n.tooltip_init_show){if(e.isTimeSeries()&&d(n.tooltip_init_x)){for(n.tooltip_init_x=e.parseDate(n.tooltip_init_x),t=0;t<e.data.targets[0].values.length&&e.data.targets[0].values[t].x-n.tooltip_init_x!=0;t++);n.tooltip_init_x=t}e.tooltip.html(n.tooltip_contents.call(e,e.data.targets.map(function(t){return e.addName(t.values[n.tooltip_init_x])}),e.axis.getXAxisTickFormat(),e.getYFormat(e.hasArcType()),e.color)),e.tooltip.style("top",n.tooltip_init_position.top).style("left",n.tooltip_init_position.left).style("display","block")}},T.getTooltipSortFunction=function(){var t=this,e=t.config;if(0!==e.data_groups.length&&void 0===e.tooltip_order){var i=t.orderTargets(t.data.targets).map(function(t){return t.id});return(t.isOrderAsc()||t.isOrderDesc())&&(i=i.reverse()),function(t,e){return i.indexOf(t.id)-i.indexOf(e.id)}}var n=e.tooltip_order;void 0===n&&(n=e.data_order);var a=function(t){return t?t.value:null};if(d(n)&&"asc"===n.toLowerCase())return function(t,e){return a(t)-a(e)};if(d(n)&&"desc"===n.toLowerCase())return function(t,e){return a(e)-a(t)};if(o(n)){var r=n;return void 0===e.tooltip_order&&(r=function(t,e){return n(t?{id:t.id,values:[t]}:null,e?{id:e.id,values:[e]}:null)}),r}return c(n)?function(t,e){return n.indexOf(t.id)-n.indexOf(e.id)}:void 0},T.getTooltipContent=function(t,e,i,n){var a,r,s,o,c,d,l=this,u=l.config,h=u.tooltip_format_title||e,g=u.tooltip_format_name||function(t){return t},f=u.tooltip_format_value||i,p=this.getTooltipSortFunction();for(p&&t.sort(p),r=0;r<t.length;r++)if(t[r]&&(t[r].value||0===t[r].value)&&(a||(s=m(h?h(t[r].x):t[r].x),a="<table class='"+l.CLASS.tooltip+"'>"+(s||0===s?"<tr><th colspan='2'>"+s+"</th></tr>":"")),void 0!==(o=m(f(t[r].value,t[r].ratio,t[r].id,t[r].index,t))))){if(null===t[r].name)continue;c=m(g(t[r].name,t[r].ratio,t[r].id,t[r].index)),d=l.levelColor?l.levelColor(t[r].value):n(t[r].id),a+="<tr class='"+l.CLASS.tooltipName+"-"+l.getTargetSelectorSuffix(t[r].id)+"'>",a+="<td class='name'><span style='background-color:"+d+"'></span>"+c+"</td>",a+="<td class='value'>"+o+"</td>",a+="</tr>"}return a+"</table>"},T.tooltipPosition=function(t,e,i,n){var a,r,s,o,c,d=this,l=d.config,u=d.d3,h=d.hasArcType(),g=u.mouse(n);return h?(r=(d.width-(d.isLegendRight?d.getLegendWidth():0))/2+g[0],o=(d.hasType("gauge")?d.height:d.height/2)+g[1]+20):(a=d.getSvgLeft(!0),l.axis_rotated?(s=(r=a+g[0]+100)+e,c=d.currentWidth-d.getCurrentPaddingRight(),o=d.x(t[0].x)+20):(s=(r=a+d.getCurrentPaddingLeft(!0)+d.x(t[0].x)+20)+e,c=a+d.currentWidth-d.getCurrentPaddingRight(),o=g[1]+15),s>c&&(r-=s-c+20),o+i>d.currentHeight&&(o-=i+30)),o<0&&(o=0),{top:o,left:r}},T.showTooltip=function(t,e){var i,n,a,r=this,o=r.config,c=r.hasArcType(),d=t.filter(function(t){return t&&s(t.value)}),l=o.tooltip_position||T.tooltipPosition;0!==d.length&&o.tooltip_show&&(r.tooltip.html(o.tooltip_contents.call(r,t,r.axis.getXAxisTickFormat(),r.getYFormat(c),r.color)).style("display","block"),i=r.tooltip.property("offsetWidth"),n=r.tooltip.property("offsetHeight"),a=l.call(this,d,i,n,e),r.tooltip.style("top",a.top+"px").style("left",a.left+"px"))},T.hideTooltip=function(){this.tooltip.style("display","none")},T.setTargetType=function(t,e){var i=this,n=i.config;i.mapToTargetIds(t).forEach(function(t){i.withoutFadeIn[t]=e===n.data_types[t],n.data_types[t]=e}),t||(n.data_type=e)},T.hasType=function(t,e){var i=this.config.data_types,n=!1;return(e=e||this.data.targets)&&e.length?e.forEach(function(e){var a=i[e.id];(a&&a.indexOf(t)>=0||!a&&"line"===t)&&(n=!0)}):Object.keys(i).length?Object.keys(i).forEach(function(e){i[e]===t&&(n=!0)}):n=this.config.data_type===t,n},T.hasArcType=function(t){return this.hasType("pie",t)||this.hasType("donut",t)||this.hasType("gauge",t)},T.isLineType=function(t){var e=this.config,i=d(t)?t:t.id;return!e.data_types[i]||["line","spline","area","area-spline","step","area-step"].indexOf(e.data_types[i])>=0},T.isStepType=function(t){var e=d(t)?t:t.id;return["step","area-step"].indexOf(this.config.data_types[e])>=0},T.isSplineType=function(t){var e=d(t)?t:t.id;return["spline","area-spline"].indexOf(this.config.data_types[e])>=0},T.isAreaType=function(t){var e=d(t)?t:t.id;return["area","area-spline","area-step"].indexOf(this.config.data_types[e])>=0},T.isBarType=function(t){var e=d(t)?t:t.id;return"bar"===this.config.data_types[e]},T.isScatterType=function(t){var e=d(t)?t:t.id;return"scatter"===this.config.data_types[e]},T.isPieType=function(t){var e=d(t)?t:t.id;return"pie"===this.config.data_types[e]},T.isGaugeType=function(t){var e=d(t)?t:t.id;return"gauge"===this.config.data_types[e]},T.isDonutType=function(t){var e=d(t)?t:t.id;return"donut"===this.config.data_types[e]},T.isArcType=function(t){return this.isPieType(t)||this.isDonutType(t)||this.isGaugeType(t)},T.lineData=function(t){return this.isLineType(t)?[t]:[]},T.arcData=function(t){return this.isArcType(t.data)?[t]:[]},T.barData=function(t){return this.isBarType(t)?t.values:[]},T.lineOrScatterData=function(t){return this.isLineType(t)||this.isScatterType(t)?t.values:[]},T.barOrLineData=function(t){return this.isBarType(t)||this.isLineType(t)?t.values:[]},T.isInterpolationType=function(t){return["linear","linear-closed","basis","basis-open","basis-closed","bundle","cardinal","cardinal-open","cardinal-closed","monotone"].indexOf(t)>=0},T.isSafari=function(){var t=window.navigator.userAgent;return t.indexOf("Safari")>=0&&t.indexOf("Chrome")<0},T.isChrome=function(){return window.navigator.userAgent.indexOf("Chrome")>=0},T.initZoom=function(){var t,e=this,i=e.d3,n=e.config;e.zoom=i.behavior.zoom().on("zoomstart",function(){t=i.event.sourceEvent,e.zoom.altDomain=i.event.sourceEvent.altKey?e.x.orgDomain():null,n.zoom_onzoomstart.call(e.api,i.event.sourceEvent)}).on("zoom",function(){e.redrawForZoom.call(e)}).on("zoomend",function(){var a=i.event.sourceEvent;a&&t.clientX===a.clientX&&t.clientY===a.clientY||(e.redrawEventRect(),e.updateZoom(),n.zoom_onzoomend.call(e.api,e.x.orgDomain()))}),e.zoom.scale=function(t){return n.axis_rotated?this.y(t):this.x(t)},e.zoom.orgScaleExtent=function(){var t=n.zoom_extent?n.zoom_extent:[1,10];return[t[0],Math.max(e.getMaxDataCount()/t[1],t[1])]},e.zoom.updateScaleExtent=function(){var t=f(e.x.orgDomain())/f(e.getZoomDomain()),i=this.orgScaleExtent();return this.scaleExtent([i[0]*t,i[1]*t]),this}},T.getZoomDomain=function(){var t=this.config,e=this.d3;return[e.min([this.orgXDomain[0],t.zoom_x_min]),e.max([this.orgXDomain[1],t.zoom_x_max])]},T.updateZoom=function(){var t=this.config.zoom_enabled?this.zoom:function(){};this.main.select("."+i.zoomRect).call(t).on("dblclick.zoom",null),this.main.selectAll("."+i.eventRect).call(t).on("dblclick.zoom",null)},T.redrawForZoom=function(){var t=this,e=t.d3,i=t.config,n=t.zoom,a=t.x;if(i.zoom_enabled&&0!==t.filterTargetsToShow(t.data.targets).length){if("mousemove"===e.event.sourceEvent.type&&n.altDomain)return a.domain(n.altDomain),void n.scale(a).updateScaleExtent();t.isCategorized()&&a.orgDomain()[0]===t.orgXDomain[0]&&a.domain([t.orgXDomain[0]-1e-10,a.orgDomain()[1]]),t.redraw({withTransition:!1,withY:i.zoom_rescale,withSubchart:!1,withEventRect:!1,withDimension:!1}),"mousemove"===e.event.sourceEvent.type&&(t.cancelClick=!0),i.zoom_onzoom.call(t.api,a.orgDomain())}},A});