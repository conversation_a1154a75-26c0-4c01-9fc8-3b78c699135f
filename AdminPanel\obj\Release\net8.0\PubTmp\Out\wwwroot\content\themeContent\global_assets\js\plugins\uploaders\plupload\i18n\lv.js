// Latvian (lv)
plupload.addI18n({"Stop Upload":"Apturēt augšupielādi","Upload URL might be wrong or doesn't exist.":"Augšupielādes saite neeksistē vai ir nepareiza.","tb":"terabaiti","Size":"Izmē<PERSON>","Close":"Aizvērt","You must specify either browse_button or drop_element.":"Jums ir jānorāda vainu browse_button vai drop_element.","Init error.":"Inicializācijas kļūda.","Add files to the upload queue and click the start button.":"Pievienojiet failus rindai un klikšķiniet uz pogas \"Sākt augšupielādi\".","List":"Saraksts","Filename":"Faila nosaukums","%s specified, but cannot be found.":"%s norādīts, bet nevar tikt atrasts.","Image format either wrong or not supported.":"Attēla formāts ir nepareizs vai arī netiek atbalstīts.","Status":"Statuss","HTTP Error.":"HTTP kļūda.","Start Upload":"Sākt augšupielādi","Error: File too large:":"Kļūda: Fails pārāk liels:","kb":"kilobaiti","Duplicate file error.":"Atkārtota faila kļūda","File size error.":"Faila izmēra kļūda.","N/A":"N/A","gb":"gigabaiti","Error: Invalid file extension:":"Kļūda: Nepareizs faila paplašinājums:","Select files":"Izvēlieties failus","%s already present in the queue.":"%s jau ir atrodams rindā.","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Izšķirtspēja ārpus robežām! <b>%s</b> atbalsta attēlus tikai līdz %wx%hpx","File: %s":"Fails: %s","b":"baiti","Uploaded %d/%d files":"Augšupielādēti %d/%d faili","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Iespējams ielādēt tikai %d failus vienā reizē. Atlikušie faili netika pievienoti","%d files queued":"%d faili pievienoti rindai","File: %s, size: %d, max file size: %d":"Fails: %s, izmērs: %d, max faila izmērs: %d","Thumbnails":"Ikonas","Drag files here.":"Ievelciet failus šeit","Runtime ran out of available memory.":"Pietrūkst izmantojamās atmiņas.","File count error.":"Failu skaita kļūda","File extension error.":"Faila paplašinājuma kļūda.","mb":"megabaiti","Add Files":"Pievienot failus"});