/* ------------------------------------------------------------------------------
 *
 *  # Material icons base
 *
 *  Base styles for Material icons. Include font import, class definition and other options
 *
 * ---------------------------------------------------------------------------- */


// Font definition
@font-face {
    font-family: 'material-icons';
    src:
        url('#{$mi-font-path}/material-icons.ttf?rnn6yx') format('truetype'),
        url('#{$mi-font-path}/material-icons.woff?rnn6yx') format('woff'),
        url('#{$mi-font-path}/material-icons.svg?rnn6yx#material-icons') format('svg');
    font-weight: normal;
    font-style: normal;
}

// Base Class Definition
[class^="#{$mi-css-prefix}-"],
[class*=" #{$mi-css-prefix}-"] {
    /* Use !important to prevent issues with browser extensions that change fonts */
    font-family: 'material-icons' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    min-width: 1em;
    display: inline-block;
    text-align: center;
    font-size: $icon-font-size;
    vertical-align: middle;
    position: relative;
    top: -1px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

// Icon Sizes. 4 additional sizes: .mi-2x -> .mi-5x
@for $i from 1 through 5 {
    .#{$mi-css-prefix}-#{$i}x {
        font-size: $i * $icon-font-size;
    }
}


//
// Rotated & Flipped Icons
//

// Rotation
.#{$mi-css-prefix}-rotate-90  { @include mi-icon-rotate(90deg, 1);  }
.#{$mi-css-prefix}-rotate-180 { @include mi-icon-rotate(180deg, 2); }
.#{$mi-css-prefix}-rotate-270 { @include mi-icon-rotate(270deg, 3); }

// Flip
.#{$mi-css-prefix}-flip-horizontal { @include mi-icon-flip(-1, 1, 0); }
.#{$mi-css-prefix}-flip-vertical   { @include mi-icon-flip(1, -1, 2); }
.#{$mi-css-prefix}-flip-horizontal.#{$mi-css-prefix}-flip-vertical { @include mi-icon-flip(-1, -1, 2); }
