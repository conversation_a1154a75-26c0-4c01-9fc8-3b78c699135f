/*! jQuery Fancytree Plugin - 2.28.0 - 2018-03-02T20:59:49Z
  * https://github.com/mar10/fancytree
  * Copyright (c) 2018 <PERSON>; Licensed MIT
 */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {
		// AMD. Register as an anonymous module.
		define( [
			"jquery",
			"jquery-ui/ui/widgets/mouse",
			"jquery-ui/ui/widgets/draggable",
			"jquery-ui/ui/widgets/droppable",
			"jquery-ui/ui/effects/effect-blind",
			"jquery-ui/ui/data",
			"jquery-ui/ui/effect",
			"jquery-ui/ui/focusable",
			"jquery-ui/ui/keycode",
			"jquery-ui/ui/position",
			"jquery-ui/ui/scroll-parent",
			"jquery-ui/ui/tabbable",
			"jquery-ui/ui/unique-id",
			"jquery-ui/ui/widget"
		], factory );
	} else if ( typeof module === "object" && module.exports ) {
		// Node/CommonJS
		module.exports = factory(require("jquery"));
	} else {
		// Browser globals
		factory( jQuery );
	}
}(function( $ ) {

!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree.ui-deps"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree.ui-deps"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function t(t,n){t||(n=n?": "+n:"",e.error("Fancytree assertion failed"+n))}function n(e,t){var n,i,r=window.console?window.console[e]:null;if(r)try{r.apply(window.console,t)}catch(e){for(i="",n=0;n<t.length;n++)i+=t[n];r(i)}}function i(e,t,n,i,r){return function(){var n=t[e],o=i[e],s=t.ext[r],a=function(){return n.apply(t,arguments)},l=function(e){return n.apply(t,e)};return function(){var e=t._local,n=t._super,i=t._superApply;try{return t._local=s,t._super=a,t._superApply=l,o.apply(t,arguments)}finally{t._local=e,t._super=n,t._superApply=i}}}()}function r(t,n,r,o){for(var s in r)"function"==typeof r[s]?"function"==typeof t[s]?t[s]=i(s,t,0,r,o):"_"===s.charAt(0)?t.ext[o][s]=i(s,t,0,r,o):e.error("Could not override tree."+s+". Use prefix '_' to create tree."+o+"._"+s):"options"!==s&&(t.ext[o][s]=r[s])}function o(t,n){return void 0===t?e.Deferred(function(){this.resolve()}).promise():e.Deferred(function(){this.resolveWith(t,n)}).promise()}function s(t,n){return void 0===t?e.Deferred(function(){this.reject()}).promise():e.Deferred(function(){this.rejectWith(t,n)}).promise()}function a(e,t){return function(){e.resolveWith(t)}}function l(t){var n=e.extend({},t.data()),i=n.json;return delete n.fancytree,delete n.uiFancytree,i&&(delete n.json,n=e.extend(n,i)),n}function d(e){return(""+e).replace(b,function(e){return x[e]})}function c(e){return e=e.toLowerCase(),function(t){return t.title.toLowerCase().indexOf(e)>=0}}function u(n,i){var r,o,s,a;for(this.parent=n,this.tree=n.tree,this.ul=null,this.li=null,this.statusNodeType=null,this._isLoading=!1,this._error=null,this.data={},r=0,o=E.length;r<o;r++)this[s=E[r]]=i[s];null==this.unselectableIgnore&&null==this.unselectableStatus||(this.unselectable=!0),i.hideCheckbox&&e.error("'hideCheckbox' node option was removed in v2.23.0: use 'checkbox: false'"),i.data&&e.extend(this.data,i.data);for(s in i)T[s]||e.isFunction(i[s])||A[s]||(this.data[s]=i[s]);null==this.key?this.tree.options.defaultKey?(this.key=this.tree.options.defaultKey(this),t(this.key,"defaultKey() must return a unique key")):this.key="_"+g._nextNodeKey++:this.key=""+this.key,i.active&&(t(null===this.tree.activeNode,"only one active node allowed"),this.tree.activeNode=this),i.selected&&(this.tree.lastSelectedNode=this),(a=i.children)?a.length?this._setChildren(a):this.children=this.lazy?[]:null:this.children=null,this.tree._callHook("treeRegisterNode",this.tree,!0,this)}function h(t){this.widget=t,this.$div=t.element,this.options=t.options,this.options&&(e.isFunction(this.options.lazyload)&&!e.isFunction(this.options.lazyLoad)&&(this.options.lazyLoad=function(){return g.warn("The 'lazyload' event is deprecated since 2014-02-25. Use 'lazyLoad' (with uppercase L) instead."),t.options.lazyload.apply(this,arguments)}),e.isFunction(this.options.loaderror)&&e.error("The 'loaderror' event was renamed since 2014-07-03. Use 'loadError' (with uppercase E) instead."),void 0!==this.options.fx&&g.warn("The 'fx' option was replaced by 'toggleEffect' since 2014-11-30."),void 0!==this.options.removeNode&&e.error("The 'removeNode' event was replaced by 'modifyChild' since 2.20 (2016-09-10).")),this.ext={},this.types={},this.columns={},this.data=l(this.$div),this._id=e.ui.fancytree._nextId++,this._ns=".fancytree-"+this._id,this.activeNode=null,this.focusNode=null,this._hasFocus=null,this._tempCache={},this._lastMousedownNode=null,this._enableUpdate=!0,this.lastSelectedNode=null,this.systemFocusElement=null,this.lastQuicksearchTerm="",this.lastQuicksearchTime=0,this.statusClassPropName="span",this.ariaPropName="li",this.nodeContainerAttrName="li",this.$div.find(">ul.fancytree-container").remove();var n,i={tree:this};this.rootNode=new u(i,{title:"root",key:"root_"+this._id,children:null,expanded:!0}),this.rootNode.parent=null,n=e("<ul>",{class:"ui-fancytree fancytree-container fancytree-plain"}).appendTo(this.$div),this.$container=n,this.rootNode.ul=n[0],null==this.options.debugLevel&&(this.options.debugLevel=g.debugLevel)}{if(!e.ui||!e.ui.fancytree){var f,p,g=null,v=new RegExp(/\.|\//),y=/[&<>"'\/]/g,b=/[<>"'\/]/g,m="$recursive_request",x={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},k={16:!0,17:!0,18:!0},N={8:"backspace",9:"tab",10:"return",13:"return",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},_={0:"",1:"left",2:"middle",3:"right"},C="active expanded focus folder lazy radiogroup selected unselectable unselectableIgnore".split(" "),S={},w="columns types".split(" "),E="checkbox expanded extraClasses folder icon iconTooltip key lazy partsel radiogroup refKey selected statusNodeType title tooltip type unselectable unselectableIgnore unselectableStatus".split(" "),T={},L={},A={active:!0,children:!0,data:!0,focus:!0};for(f=0;f<C.length;f++)S[C[f]]=!0;for(f=0;f<E.length;f++)p=E[f],T[p]=!0,p!==p.toLowerCase()&&(L[p.toLowerCase()]=p);return t(e.ui,"Fancytree requires jQuery UI (http://jqueryui.com)"),Date.now||(Date.now=function(){return(new Date).getTime()}),u.prototype={_findDirectChild:function(e){var t,n,i=this.children;if(i)if("string"==typeof e){for(t=0,n=i.length;t<n;t++)if(i[t].key===e)return i[t]}else{if("number"==typeof e)return this.children[e];if(e.parent===this)return e}return null},_setChildren:function(e){t(e&&(!this.children||0===this.children.length),"only init supported"),this.children=[];for(var n=0,i=e.length;n<i;n++)this.children.push(new u(this,e[n]))},addChildren:function(n,i){var r,o,s,a=this.getFirstChild(),l=this.getLastChild(),d=null,c=[];for(e.isPlainObject(n)&&(n=[n]),this.children||(this.children=[]),r=0,o=n.length;r<o;r++)c.push(new u(this,n[r]));if(d=c[0],null==i?this.children=this.children.concat(c):(i=this._findDirectChild(i),t((s=e.inArray(i,this.children))>=0,"insertBefore must be an existing child"),this.children.splice.apply(this.children,[s,0].concat(c))),a&&!i){for(r=0,o=c.length;r<o;r++)c[r].render();a!==this.getFirstChild()&&a.renderStatus(),l!==this.getLastChild()&&l.renderStatus()}else(!this.parent||this.parent.ul||this.tr)&&this.render();return 3===this.tree.options.selectMode&&this.fixSelection3FromEndNodes(),this.triggerModifyChild("add",1===c.length?c[0]:null),d},addClass:function(e){return this.toggleClass(e,!0)},addNode:function(e,n){switch(void 0!==n&&"over"!==n||(n="child"),n){case"after":return this.getParent().addChildren(e,this.getNextSibling());case"before":return this.getParent().addChildren(e,this);case"firstChild":var i=this.children?this.children[0]:null;return this.addChildren(e,i);case"child":case"over":return this.addChildren(e)}t(!1,"Invalid mode: "+n)},addPagingNode:function(t,n){var i,r;n=n||"child";{if(!1!==t)return t=e.extend({title:this.tree.options.strings.moreData,statusNodeType:"paging",icon:!1},t),this.partload=!0,this.addNode(t,n);for(i=this.children.length-1;i>=0;i--)"paging"===(r=this.children[i]).statusNodeType&&this.removeChild(r);this.partload=!1}},appendSibling:function(e){return this.addNode(e,"after")},applyPatch:function(t){if(null===t)return this.remove(),o(this);var n,i,r={children:!0,expanded:!0,parent:!0};for(n in t)i=t[n],r[n]||e.isFunction(i)||(T[n]?this[n]=i:this.data[n]=i);return t.hasOwnProperty("children")&&(this.removeChildren(),t.children&&this._setChildren(t.children)),this.isVisible()&&(this.renderTitle(),this.renderStatus()),t.hasOwnProperty("expanded")?this.setExpanded(t.expanded):o(this)},collapseSiblings:function(){return this.tree._callHook("nodeCollapseSiblings",this)},copyTo:function(e,t,n){return e.addNode(this.toDict(!0,n),t)},countChildren:function(e){var t,n,i,r=this.children;if(!r)return 0;if(i=r.length,!1!==e)for(t=0,n=i;t<n;t++)i+=r[t].countChildren();return i},debug:function(e){this.tree.options.debugLevel>=4&&(Array.prototype.unshift.call(arguments,this.toString()),n("log",arguments))},discard:function(){return this.warn("FancytreeNode.discard() is deprecated since 2014-02-16. Use .resetLazy() instead."),this.resetLazy()},discardMarkup:function(e){var t=e?"nodeRemoveMarkup":"nodeRemoveChildMarkup";this.tree._callHook(t,this)},error:function(e){this.options.debugLevel>=1&&(Array.prototype.unshift.call(arguments,this.toString()),n("error",arguments))},findAll:function(t){t=e.isFunction(t)?t:c(t);var n=[];return this.visit(function(e){t(e)&&n.push(e)}),n},findFirst:function(t){t=e.isFunction(t)?t:c(t);var n=null;return this.visit(function(e){if(t(e))return n=e,!1}),n},_changeSelectStatusAttrs:function(e){var n=!1,i=this.tree.options,r=g.evalOption("unselectable",this,this,i,!1),o=g.evalOption("unselectableStatus",this,this,i,void 0);switch(r&&null!=o&&(e=o),e){case!1:n=this.selected||this.partsel,this.selected=!1,this.partsel=!1;break;case!0:n=!this.selected||!this.partsel,this.selected=!0,this.partsel=!0;break;case void 0:n=this.selected||!this.partsel,this.selected=!1,this.partsel=!0;break;default:t(!1,"invalid state: "+e)}return n&&this.renderStatus(),n},fixSelection3AfterClick:function(e){var t=this.isSelected();this.visit(function(e){e._changeSelectStatusAttrs(t)}),this.fixSelection3FromEndNodes(e)},fixSelection3FromEndNodes:function(e){function n(e){var t,r,o,s,a,l,d,c,u=e.children;if(u&&u.length){for(l=!0,d=!1,t=0,r=u.length;t<r;t++)s=n(o=u[t]),g.evalOption("unselectableIgnore",o,o,i,!1)||(!1!==s&&(d=!0),!0!==s&&(l=!1));a=!!l||!!d&&void 0}else a=null==(c=g.evalOption("unselectableStatus",e,e,i,void 0))?!!e.selected:!!c;return e._changeSelectStatusAttrs(a),a}var i=this.tree.options;t(3===i.selectMode,"expected selectMode 3"),n(this),this.visitParents(function(e){var t,n,r,o,s,a=e.children,l=!0,d=!1;for(t=0,n=a.length;t<n;t++)r=a[t],g.evalOption("unselectableIgnore",r,r,i,!1)||(((o=null==(s=g.evalOption("unselectableStatus",r,r,i,void 0))?!!r.selected:!!s)||r.partsel)&&(d=!0),o||(l=!1));o=!!l||!!d&&void 0,e._changeSelectStatusAttrs(o)})},fromDict:function(t){for(var n in t)T[n]?this[n]=t[n]:"data"===n?e.extend(this.data,t.data):e.isFunction(t[n])||A[n]||(this.data[n]=t[n]);t.children&&(this.removeChildren(),this.addChildren(t.children)),this.renderTitle()},getChildren:function(){if(void 0!==this.hasChildren())return this.children},getFirstChild:function(){return this.children?this.children[0]:null},getIndex:function(){return e.inArray(this,this.parent.children)},getIndexHier:function(t,n){t=t||".";var i,r=[];return e.each(this.getParentList(!1,!0),function(e,t){i=""+(t.getIndex()+1),n&&(i=("0000000"+i).substr(-n)),r.push(i)}),r.join(t)},getKeyPath:function(e){var t=[],n=this.tree.options.keyPathSeparator;return this.visitParents(function(e){e.parent&&t.unshift(e.key)},!e),n+t.join(n)},getLastChild:function(){return this.children?this.children[this.children.length-1]:null},getLevel:function(){for(var e=0,t=this.parent;t;)e++,t=t.parent;return e},getNextSibling:function(){if(this.parent){var e,t,n=this.parent.children;for(e=0,t=n.length-1;e<t;e++)if(n[e]===this)return n[e+1]}return null},getParent:function(){return this.parent},getParentList:function(e,t){for(var n=[],i=t?this:this.parent;i;)(e||i.parent)&&n.unshift(i),i=i.parent;return n},getPrevSibling:function(){if(this.parent){var e,t,n=this.parent.children;for(e=1,t=n.length;e<t;e++)if(n[e]===this)return n[e-1]}return null},getSelectedNodes:function(e){var t=[];return this.visit(function(n){if(n.selected&&(t.push(n),!0===e))return"skip"}),t},hasChildren:function(){if(this.lazy){if(null==this.children)return;if(0===this.children.length)return!1;if(1===this.children.length&&this.children[0].isStatusNode())return;return!0}return!(!this.children||!this.children.length)},hasFocus:function(){return this.tree.hasFocus()&&this.tree.focusNode===this},info:function(e){this.tree.options.debugLevel>=3&&(Array.prototype.unshift.call(arguments,this.toString()),n("info",arguments))},isActive:function(){return this.tree.activeNode===this},isBelowOf:function(e){return this.getIndexHier(".",5)>e.getIndexHier(".",5)},isChildOf:function(e){return this.parent&&this.parent===e},isDescendantOf:function(t){if(!t||t.tree!==this.tree)return!1;for(var n=this.parent;n;){if(n===t)return!0;n===n.parent&&e.error("Recursive parent link: "+n),n=n.parent}return!1},isExpanded:function(){return!!this.expanded},isFirstSibling:function(){var e=this.parent;return!e||e.children[0]===this},isFolder:function(){return!!this.folder},isLastSibling:function(){var e=this.parent;return!e||e.children[e.children.length-1]===this},isLazy:function(){return!!this.lazy},isLoaded:function(){return!this.lazy||void 0!==this.hasChildren()},isLoading:function(){return!!this._isLoading},isRoot:function(){return this.isRootNode()},isPartsel:function(){return!this.selected&&!!this.partsel},isPartload:function(){return!!this.partload},isRootNode:function(){return this.tree.rootNode===this},isSelected:function(){return!!this.selected},isStatusNode:function(){return!!this.statusNodeType},isPagingNode:function(){return"paging"===this.statusNodeType},isTopLevel:function(){return this.tree.rootNode===this.parent},isUndefined:function(){return void 0===this.hasChildren()},isVisible:function(){var e,t,n=this.getParentList(!1,!1);for(e=0,t=n.length;e<t;e++)if(!n[e].expanded)return!1;return!0},lazyLoad:function(e){return this.warn("FancytreeNode.lazyLoad() is deprecated since 2014-02-16. Use .load() instead."),this.load(e)},load:function(e){var n,i,r=this,s=this.isExpanded();return t(this.isLazy(),"load() requires a lazy node"),e||this.isUndefined()?(this.isLoaded()&&this.resetLazy(),!1===(i=this.tree._triggerNodeEvent("lazyLoad",this))?o(this):(t("boolean"!=typeof i,"lazyLoad event must return source in data.result"),n=this.tree._callHook("nodeLoadChildren",this,i),s?(this.expanded=!0,n.always(function(){r.render()})):n.always(function(){r.renderStatus()}),n)):o(this)},makeVisible:function(t){var n,i=this,r=[],o=new e.Deferred,s=this.getParentList(!1,!1),a=s.length,l=!(t&&!0===t.noAnimation),d=!(t&&!1===t.scrollIntoView);for(n=a-1;n>=0;n--)r.push(s[n].setExpanded(!0,t));return e.when.apply(e,r).done(function(){d?i.scrollIntoView(l).done(function(){o.resolve()}):o.resolve()}),o.promise()},moveTo:function(n,i,r){void 0===i||"over"===i?i="child":"firstChild"===i&&(n.children&&n.children.length?(i="before",n=n.children[0]):i="child");var o,s=this.parent,a="child"===i?n:n.parent;if(this!==n){if(this.parent?a.isDescendantOf(this)&&e.error("Cannot move a node to its own descendant"):e.error("Cannot move system root"),a!==s&&s.triggerModifyChild("remove",this),1===this.parent.children.length){if(this.parent===a)return;this.parent.children=this.parent.lazy?[]:null,this.parent.expanded=!1}else t((o=e.inArray(this,this.parent.children))>=0,"invalid source parent"),this.parent.children.splice(o,1);if(this.parent=a,a.hasChildren())switch(i){case"child":a.children.push(this);break;case"before":t((o=e.inArray(n,a.children))>=0,"invalid target parent"),a.children.splice(o,0,this);break;case"after":t((o=e.inArray(n,a.children))>=0,"invalid target parent"),a.children.splice(o+1,0,this);break;default:e.error("Invalid mode "+i)}else a.children=[this];r&&n.visit(r,!0),a===s?a.triggerModifyChild("move",this):a.triggerModifyChild("add",this),this.tree!==n.tree&&(this.warn("Cross-tree moveTo is experimantal!"),this.visit(function(e){e.tree=n.tree},!0)),s.isDescendantOf(a)||s.render(),a.isDescendantOf(s)||a===s||a.render()}},navigate:function(t,n){function i(i){if(i){try{i.makeVisible({scrollIntoView:!1})}catch(e){}return e(i.span).is(":visible")?!1===n?i.setFocus():i.setActive():(i.debug("Navigate: skipping hidden node"),void i.navigate(t,n))}}var r,s,a,l=e.ui.keyCode,d=null;switch(t){case l.BACKSPACE:this.parent&&this.parent.parent&&(a=i(this.parent));break;case l.HOME:this.tree.visit(function(t){if(e(t.span).is(":visible"))return a=i(t),!1});break;case l.END:this.tree.visit(function(t){e(t.span).is(":visible")&&(a=t)}),a&&(a=i(a));break;case l.LEFT:this.expanded?(this.setExpanded(!1),a=i(this)):this.parent&&this.parent.parent&&(a=i(this.parent));break;case l.RIGHT:this.expanded||!this.children&&!this.lazy?this.children&&this.children.length&&(a=i(this.children[0])):(this.setExpanded(),a=i(this));break;case l.UP:for(d=this.getPrevSibling();d&&!e(d.span).is(":visible");)d=d.getPrevSibling();for(;d&&d.expanded&&d.children&&d.children.length;)d=d.children[d.children.length-1];!d&&this.parent&&this.parent.parent&&(d=this.parent),a=i(d);break;case l.DOWN:if(this.expanded&&this.children&&this.children.length)d=this.children[0];else for(r=(s=this.getParentList(!1,!0)).length-1;r>=0;r--){for(d=s[r].getNextSibling();d&&!e(d.span).is(":visible");)d=d.getNextSibling();if(d)break}a=i(d);break;default:!1}return a||o()},remove:function(){return this.parent.removeChild(this)},removeChild:function(e){return this.tree._callHook("nodeRemoveChild",this,e)},removeChildren:function(){return this.tree._callHook("nodeRemoveChildren",this)},removeClass:function(e){return this.toggleClass(e,!1)},render:function(e,t){return this.tree._callHook("nodeRender",this,e,t)},renderTitle:function(){return this.tree._callHook("nodeRenderTitle",this)},renderStatus:function(){return this.tree._callHook("nodeRenderStatus",this)},replaceWith:function(n){var i,r=this.parent,o=e.inArray(this,r.children),s=this;return t(this.isPagingNode(),"replaceWith() currently requires a paging status node"),(i=this.tree._callHook("nodeLoadChildren",this,n)).done(function(e){var t=s.children;for(f=0;f<t.length;f++)t[f].parent=r;r.children.splice.apply(r.children,[o+1,0].concat(t)),s.children=null,s.remove(),r.render()}).fail(function(){s.setExpanded()}),i},resetLazy:function(){this.removeChildren(),this.expanded=!1,this.lazy=!0,this.children=void 0,this.renderStatus()},scheduleAction:function(t,n){this.tree.timer&&clearTimeout(this.tree.timer),this.tree.timer=null;var i=this;switch(t){case"cancel":break;case"expand":this.tree.timer=setTimeout(function(){i.tree.debug("setTimeout: trigger expand"),i.setExpanded(!0)},n);break;case"activate":this.tree.timer=setTimeout(function(){i.tree.debug("setTimeout: trigger activate"),i.setActive(!0)},n);break;default:e.error("Invalid mode "+t)}},scrollIntoView:function(n,i){void 0!==i&&function(e){return!(!e.tree||void 0===e.statusNodeType)}(i)&&(this.warn("scrollIntoView() with 'topNode' option is deprecated since 2014-05-08. Use 'options.topNode' instead."),i={topNode:i});var r,s,a,l=e.extend({effects:!0===n?{duration:200,queue:!1}:n,scrollOfs:this.tree.options.scrollOfs,scrollParent:this.tree.options.scrollParent||this.tree.$container,topNode:null},i),d=new e.Deferred,c=this,u=e(this.span).height(),h=e(l.scrollParent),f=l.scrollOfs.top||0,p=l.scrollOfs.bottom||0,g=h.height(),v=h.scrollTop(),y=h,b=h[0]===window,m=l.topNode||null,x=null;return e(this.span).is(":visible")?(b?(s=e(this.span).offset().top,r=m&&m.span?e(m.span).offset().top:0,y=e("html,body")):(t(h[0]!==document&&h[0]!==document.body,"scrollParent should be a simple element or `window`, not document or body."),a=h.offset().top,s=e(this.span).offset().top-a+v,r=m?e(m.span).offset().top-a+v:0,g-=Math.max(0,h.innerHeight()-h[0].clientHeight)),s<v+f?x=s-f:s+u>v+g-p&&(x=s+u-g+p,m&&(t(m.isRootNode()||e(m.span).is(":visible"),"topNode must be visible"),r<x&&(x=r-f))),null!==x?l.effects?(l.effects.complete=function(){d.resolveWith(c)},y.stop(!0).animate({scrollTop:x},l.effects)):(y[0].scrollTop=x,d.resolveWith(this)):d.resolveWith(this),d.promise()):(this.warn("scrollIntoView(): node is invisible."),o())},setActive:function(e,t){return this.tree._callHook("nodeSetActive",this,e,t)},setExpanded:function(e,t){return this.tree._callHook("nodeSetExpanded",this,e,t)},setFocus:function(e){return this.tree._callHook("nodeSetFocus",this,e)},setSelected:function(e,t){return this.tree._callHook("nodeSetSelected",this,e,t)},setStatus:function(e,t,n){return this.tree._callHook("nodeSetStatus",this,e,t,n)},setTitle:function(e){this.title=e,this.renderTitle(),this.triggerModify("rename")},sortChildren:function(e,t){var n,i,r=this.children;if(r){if(e=e||function(e,t){var n=e.title.toLowerCase(),i=t.title.toLowerCase();return n===i?0:n>i?1:-1},r.sort(e),t)for(n=0,i=r.length;n<i;n++)r[n].children&&r[n].sortChildren(e,"$norender$");"$norender$"!==t&&this.render(),this.triggerModifyChild("sort")}},toDict:function(t,n){var i,r,o,s={},a=this;if(e.each(E,function(e,t){(a[t]||!1===a[t])&&(s[t]=a[t])}),e.isEmptyObject(this.data)||(s.data=e.extend({},this.data),e.isEmptyObject(s.data)&&delete s.data),n&&n(s,a),t&&this.hasChildren())for(s.children=[],i=0,r=this.children.length;i<r;i++)(o=this.children[i]).isStatusNode()||s.children.push(o.toDict(!0,n));return s},toggleClass:function(t,n){var i,r,o=t.match(/\S+/g)||[],s=0,a=!1,l=this[this.tree.statusClassPropName],d=" "+(this.extraClasses||"")+" ";for(l&&e(l).toggleClass(t,n);i=o[s++];)if(r=d.indexOf(" "+i+" ")>=0,n=void 0===n?!r:!!n)r||(d+=i+" ",a=!0);else for(;d.indexOf(" "+i+" ")>-1;)d=d.replace(" "+i+" "," ");return this.extraClasses=e.trim(d),a},toggleExpanded:function(){return this.tree._callHook("nodeToggleExpanded",this)},toggleSelected:function(){return this.tree._callHook("nodeToggleSelected",this)},toString:function(){return"FancytreeNode@"+this.key+"[title='"+this.title+"']"},triggerModifyChild:function(t,n,i){var r,o=this.tree.options.modifyChild;o&&(n&&n.parent!==this&&e.error("childNode "+n+" is not a child of "+this),r={node:this,tree:this.tree,operation:t,childNode:n||null},i&&e.extend(r,i),o({type:"modifyChild"},r))},triggerModify:function(e,t){this.parent.triggerModifyChild(e,this,t)},visit:function(e,t){var n,i,r=!0,o=this.children;if(!0===t&&(!1===(r=e(this))||"skip"===r))return r;if(o)for(n=0,i=o.length;n<i&&!1!==(r=o[n].visit(e,!0));n++);return r},visitAndLoad:function(t,n,i){var r,s,a,l=this;return!t||!0!==n||!1!==(s=t(l))&&"skip"!==s?l.children||l.lazy?(r=new e.Deferred,a=[],l.load().done(function(){for(var n=0,i=l.children.length;n<i;n++){if(!1===(s=l.children[n].visitAndLoad(t,!0,!0))){r.reject();break}"skip"!==s&&a.push(s)}e.when.apply(this,a).then(function(){r.resolve()})}),r.promise()):o():i?s:o()},visitParents:function(e,t){if(t&&!1===e(this))return!1;for(var n=this.parent;n;){if(!1===e(n))return!1;n=n.parent}return!0},visitSiblings:function(e,t){var n,i,r,o=this.parent.children;for(n=0,i=o.length;n<i;n++)if(r=o[n],(t||r!==this)&&!1===e(r))return!1;return!0},warn:function(e){this.tree.options.debugLevel>=2&&(Array.prototype.unshift.call(arguments,this.toString()),n("warn",arguments))}},h.prototype={_makeHookContext:function(t,n,i){var r,o;return void 0!==t.node?(n&&t.originalEvent!==n&&e.error("invalid args"),r=t):t.tree?r={node:t,tree:o=t.tree,widget:o.widget,options:o.widget.options,originalEvent:n,typeInfo:o.types[t.type]||{}}:t.widget?r={node:null,tree:t,widget:t.widget,options:t.widget.options,originalEvent:n}:e.error("invalid args"),i&&e.extend(r,i),r},_callHook:function(t,n,i){var r=this._makeHookContext(n),o=this[t],s=Array.prototype.slice.call(arguments,2);return e.isFunction(o)||e.error("_callHook('"+t+"') is not a function"),s.unshift(r),o.apply(this,s)},_setExpiringValue:function(e,t,n){this._tempCache[e]={value:t,expire:Date.now()+(+n||50)}},_getExpiringValue:function(e){var t=this._tempCache[e];return t&&t.expire>Date.now()?t.value:(delete this._tempCache[e],null)},_requireExtension:function(n,i,r,o){r=!!r;var s=this._local.name,a=this.options.extensions,l=e.inArray(n,a)<e.inArray(s,a),d=i&&null==this.ext[n],c=!d&&null!=r&&r!==l;return t(s&&s!==n,"invalid or same name"),!d&&!c||(o||(d||i?(o="'"+s+"' extension requires '"+n+"'",c&&(o+=" to be registered "+(r?"before":"after")+" itself")):o="If used together, `"+n+"` must be registered "+(r?"before":"after")+" `"+s+"`"),e.error(o),!1)},activateKey:function(e){var t=this.getNodeByKey(e);return t?t.setActive():this.activeNode&&this.activeNode.setActive(!1),t},addPagingNode:function(e,t){return this.rootNode.addPagingNode(e,t)},applyPatch:function(n){var i,r,o,s,l,d,c=n.length,u=[];for(r=0;r<c;r++)t(2===(o=n[r]).length,"patchList must be an array of length-2-arrays"),s=o[0],l=o[1],(d=null===s?this.rootNode:this.getNodeByKey(s))?(i=new e.Deferred,u.push(i),d.applyPatch(l).always(a(i,d))):this.warn("could not find node with key '"+s+"'");return e.when.apply(e,u).promise()},clear:function(e){this._callHook("treeClear",this)},count:function(){return this.rootNode.countChildren()},debug:function(e){this.options.debugLevel>=4&&(Array.prototype.unshift.call(arguments,this.toString()),n("log",arguments))},enableUpdate:function(e){return e=!1!==e,!!this._enableUpdate==!!e?e:(this._enableUpdate=e,e?(this.debug("enableUpdate(true): redraw "),this.render()):this.debug("enableUpdate(false)..."),!e)},findAll:function(e){return this.rootNode.findAll(e)},findFirst:function(e){return this.rootNode.findFirst(e)},findNextNode:function(t,n,i){t="string"==typeof t?function(e){var t=new RegExp("^"+e,"i");return function(e){return t.test(e.title)}}(t):t;var r=null,o=(n=n||this.getFirstChild()).parent.children,s=null,a=function(e,t,n){var i,r,o=e.children,s=o.length,l=o[t];if(l&&!1===n(l))return!1;if(l&&l.children&&l.expanded&&!1===a(l,0,n))return!1;for(i=t+1;i<s;i++)if(!1===a(e,i,n))return!1;return(r=e.parent)?a(r,r.children.indexOf(e)+1,n):a(e,0,n)};return a(n.parent,o.indexOf(n),function(i){if(i===r)return!1;r=r||i;{if(e(i.span).is(":visible"))return(!t(i)||(s=i)===n)&&void 0;i.debug("quicksearch: skipping hidden node")}}),s},generateFormElements:function(t,n,i){function r(t){d.append(e("<input>",{type:"checkbox",name:s,value:t.key,checked:!0}))}i=i||{};var o,s="string"==typeof t?t:"ft_"+this._id+"[]",a="string"==typeof n?n:"ft_"+this._id+"_active",l="fancytree_result_"+this._id,d=e("#"+l),c=3===this.options.selectMode&&!1!==i.stopOnParents;d.length?d.empty():d=e("<div>",{id:l}).hide().insertAfter(this.$container),!1!==n&&this.activeNode&&d.append(e("<input>",{type:"radio",name:a,value:this.activeNode.key,checked:!0})),i.filter?this.visit(function(e){var t=i.filter(e);if("skip"===t)return t;!1!==t&&r(e)}):!1!==t&&(o=this.getSelectedNodes(c),e.each(o,function(e,t){r(t)}))},getActiveNode:function(){return this.activeNode},getFirstChild:function(){return this.rootNode.getFirstChild()},getFocusNode:function(){return this.focusNode},getNodeByKey:function(e,t){var n,i;return!t&&(n=document.getElementById(this.options.idPrefix+e))?n.ftnode?n.ftnode:null:(t=t||this.rootNode,i=null,t.visit(function(t){if(t.key===e)return i=t,!1},!0),i)},getRootNode:function(){return this.rootNode},getSelectedNodes:function(e){return this.rootNode.getSelectedNodes(e)},hasFocus:function(){return!!this._hasFocus},info:function(e){this.options.debugLevel>=3&&(Array.prototype.unshift.call(arguments,this.toString()),n("info",arguments))},loadKeyPath:function(t,n){var i,r,o,s=this,a=new e.Deferred,l=this.getRootNode(),d=this.options.keyPathSeparator,c=[],u=e.extend({},n);for("function"==typeof n?i=n:n&&n.callback&&(i=n.callback),u.callback=function(e,t,n){i&&i.call(e,t,n),a.notifyWith(e,[{node:t,status:n}])},null==u.matchKey&&(u.matchKey=function(e,t){return e.key===t}),e.isArray(t)||(t=[t]),r=0;r<t.length;r++)(o=t[r]).charAt(0)===d&&(o=o.substr(1)),c.push(o.split(d));return setTimeout(function(){s._loadKeyPathImpl(a,u,l,c).done(function(){a.resolve()})},0),a.promise()},_loadKeyPathImpl:function(t,n,i,r){function o(e,t){var i,r,o=e.children;if(o)for(i=0,r=o.length;i<r;i++)if(n.matchKey(o[i],t))return o[i];return null}function s(e,t,i){n.callback(v,t,"loading"),t.load().done(function(){v._loadKeyPathImpl.call(v,e,n,t,i).always(a(e,v))}).fail(function(i){v.warn("loadKeyPath: error loading lazy "+t),n.callback(v,u,"error"),e.rejectWith(v)})}var l,d,c,u,h,f,p,g,v=this;for(h={},d=0;d<r.length;d++)for(p=r[d],f=i;p.length;){if(c=p.shift(),!(u=o(f,c))){this.warn("loadKeyPath: key not found: "+c+" (parent: "+f+")"),n.callback(this,c,"error");break}if(0===p.length){n.callback(this,u,"ok");break}if(u.lazy&&void 0===u.hasChildren()){n.callback(this,u,"loaded"),h[c=u.key]?h[c].pathSegList.push(p):h[c]={parent:u,pathSegList:[p]};break}n.callback(this,u,"loaded"),f=u}l=[];for(var y in h){var b=h[y];g=new e.Deferred,l.push(g),s(g,b.parent,b.pathSegList)}return e.when.apply(e,l).promise()},reactivate:function(e){var t,n=this.activeNode;return n?(this.activeNode=null,t=n.setActive(!0,{noFocus:!0}),e&&n.setFocus(),t):o()},reload:function(e){return this._callHook("treeClear",this),this._callHook("treeLoad",this,e)},render:function(e,t){return this.rootNode.render(e,t)},selectAll:function(e){this.visit(function(t){t.setSelected(e)})},setFocus:function(e){return this._callHook("treeSetFocus",this,e)},toDict:function(e,t){var n=this.rootNode.toDict(!0,t);return e?n:n.children},toString:function(){return"Fancytree@"+this._id},_triggerNodeEvent:function(e,t,n,i){var r=this._makeHookContext(t,n,i),o=this.widget._trigger(e,n,r);return!1!==o&&void 0!==r.result?r.result:o},_triggerTreeEvent:function(e,t,n){var i=this._makeHookContext(this,t,n),r=this.widget._trigger(e,t,i);return!1!==r&&void 0!==i.result?i.result:r},visit:function(e){return this.rootNode.visit(e,!1)},visitRows:function(e,t){if(t&&t.reverse)return delete t.reverse,this._visitRowsUp(e,t);var n,i,r,o=0,s=!1===t.includeSelf,a=!!t.includeHidden,l=t.start||this.rootNode.children[0];for(i=l.parent;i;){for(n=(r=i.children).indexOf(l)+o;n<r.length;n++){if(l=r[n],!s&&!1===e(l))return!1;if(s=!1,l.children&&l.children.length&&(a||l.expanded)&&!1===l.visit(function(t){return!1!==e(t)&&(a||!t.children||t.expanded?void 0:"skip")},!1))return!1}l=i,i=i.parent,o=1}return!0},_visitRowsUp:function(t,n){for(var i,r,o=!!n.includeHidden,s=n.start||this.rootNode.children[0];;){if(r=s.parent,(i=r.children)[0]===s)s=r,i=r.children;else for(s=i[i.indexOf(s)-1];(o||s.expanded)&&s.children&&s.children.length;)r=s,s=(i=s.children)[i.length-1];if((o||e(s.span).is(":visible"))&&!1===t(s))return!1}},warn:function(e){this.options.debugLevel>=2&&(Array.prototype.unshift.call(arguments,this.toString()),n("warn",arguments))}},e.extend(h.prototype,{nodeClick:function(e){var t,n,i=e.targetType,r=e.node;if("expander"===i){if(r.isLoading())return void r.debug("Got 2nd click while loading: ignored");this._callHook("nodeToggleExpanded",e)}else if("checkbox"===i)this._callHook("nodeToggleSelected",e),e.options.focusOnSelect&&this._callHook("nodeSetFocus",e,!0);else{if(n=!1,t=!0,r.folder)switch(e.options.clickFolderMode){case 2:n=!0,t=!1;break;case 3:t=!0,n=!0}t&&(this.nodeSetFocus(e),this._callHook("nodeSetActive",e,!0)),n&&this._callHook("nodeToggleExpanded",e)}},nodeCollapseSiblings:function(e,t){var n,i,r,o=e.node;if(o.parent)for(i=0,r=(n=o.parent.children).length;i<r;i++)n[i]!==o&&n[i].expanded&&this._callHook("nodeSetExpanded",n[i],!1,t)},nodeDblclick:function(e){"title"===e.targetType&&4===e.options.clickFolderMode&&this._callHook("nodeToggleExpanded",e),"title"===e.targetType&&e.originalEvent.preventDefault()},nodeKeydown:function(t){var n,i,r,o=t.originalEvent,s=t.node,a=t.tree,l=t.options,d=o.which,c=String.fromCharCode(d),u=!(o.altKey||o.ctrlKey||o.metaKey||o.shiftKey),h=e(o.target),f=!0,p=!(o.ctrlKey||!l.autoActivate);if(s||(r=this.getActiveNode()||this.getFirstChild())&&(r.setFocus(),(s=t.node=this.focusNode).debug("Keydown force focus on active node")),l.quicksearch&&u&&/\w/.test(c)&&!N[d]&&!h.is(":input:enabled"))return(i=Date.now())-a.lastQuicksearchTime>500&&(a.lastQuicksearchTerm=""),a.lastQuicksearchTime=i,a.lastQuicksearchTerm+=c,(n=a.findNextNode(a.lastQuicksearchTerm,a.getActiveNode()))&&n.setActive(),void o.preventDefault();switch(g.eventToString(o)){case"+":case"=":a.nodeSetExpanded(t,!0);break;case"-":a.nodeSetExpanded(t,!1);break;case"space":s.isPagingNode()?a._triggerNodeEvent("clickPaging",t,o):g.evalOption("checkbox",s,s,l,!1)?a.nodeToggleSelected(t):a.nodeSetActive(t,!0);break;case"return":a.nodeSetActive(t,!0);break;case"home":case"end":case"backspace":case"left":case"right":case"up":case"down":s.navigate(o.which,p);break;default:f=!1}f&&o.preventDefault()},nodeLoadChildren:function(n,i){var r,o,s,a=n.tree,l=n.node,d=Date.now();if(e.isFunction(i)&&(i=i.call(a,{type:"source"},n),t(!e.isFunction(i),"source callback must not return another function")),i.url&&(l._requestId&&l.warn("Recursive load request #"+d+" while #"+l._requestId+" is pending."),r=e.extend({},n.options.ajax,i),l._requestId=d,r.debugDelay?(o=r.debugDelay,e.isArray(o)&&(o=o[0]+Math.random()*(o[1]-o[0])),l.warn("nodeLoadChildren waiting debugDelay "+Math.round(o)+" ms ..."),r.debugDelay=!1,s=e.Deferred(function(t){setTimeout(function(){e.ajax(r).done(function(){t.resolveWith(this,arguments)}).fail(function(){t.rejectWith(this,arguments)})},o)})):s=e.ajax(r),i=new e.Deferred,s.done(function(t,r,o){var s,c;if("json"!==this.dataType&&"jsonp"!==this.dataType||"string"!=typeof t||e.error("Ajax request returned a string (did you get the JSON dataType wrong?)."),l._requestId&&l._requestId>d)i.rejectWith(this,[m]);else{if(n.options.postProcess){try{c=a._triggerNodeEvent("postProcess",n,n.originalEvent,{response:t,error:null,dataType:this.dataType})}catch(e){c={error:e,message:""+e,details:"postProcess failed"}}if(c.error)return s=e.isPlainObject(c.error)?c.error:{message:c.error},s=a._makeHookContext(l,null,s),void i.rejectWith(this,[s]);t=e.isArray(c)?c:t}else t&&t.hasOwnProperty("d")&&n.options.enableAspx&&(t="string"==typeof t.d?e.parseJSON(t.d):t.d);i.resolveWith(this,[t])}}).fail(function(e,t,n){var r=a._makeHookContext(l,null,{error:e,args:Array.prototype.slice.call(arguments),message:n,details:e.status+": "+n});i.rejectWith(this,[r])})),e.isFunction(i.then)&&e.isFunction(i.catch)&&(s=i,i=new e.Deferred,s.then(function(e){i.resolve(e)},function(e){i.reject(e)})),e.isFunction(i.promise))a.nodeSetStatus(n,"loading"),i.done(function(e){a.nodeSetStatus(n,"ok"),l._requestId=null}).fail(function(e){var t;e!==m?(e.node&&e.error&&e.message?t=e:"[object Object]"===(t=a._makeHookContext(l,null,{error:e,args:Array.prototype.slice.call(arguments),message:e?e.message||e.toString():""})).message&&(t.message=""),l.warn("Load children failed ("+t.message+")",t),!1!==a._triggerNodeEvent("loadError",t,null)&&a.nodeSetStatus(n,"error",t.message,t.details)):l.warn("Ignored response for obsolete load request #"+d+" (expected #"+l._requestId+")")});else if(n.options.postProcess){var c=a._triggerNodeEvent("postProcess",n,n.originalEvent,{response:i,error:null,dataType:typeof i});i=e.isArray(c)?c:i}return e.when(i).done(function(n){var i;e.isPlainObject(n)&&(t(l.isRootNode(),"source may only be an object for root nodes (expecting an array of child objects otherwise)"),t(e.isArray(n.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),i=n,n=n.children,delete i.children,e.each(w,function(e,t){void 0!==i[t]&&(a[t]=i[t],delete i[t])}),e.extend(a.data,i)),t(e.isArray(n),"expected array of children"),l._setChildren(n),a._triggerNodeEvent("loadChildren",l)})},nodeLoadKeyPath:function(e,t){},nodeRemoveChild:function(n,i){var r,o=n.node,s=e.extend({},n,{node:i}),a=o.children;if(1===a.length)return t(i===a[0],"invalid single child"),this.nodeRemoveChildren(n);this.activeNode&&(i===this.activeNode||this.activeNode.isDescendantOf(i))&&this.activeNode.setActive(!1),this.focusNode&&(i===this.focusNode||this.focusNode.isDescendantOf(i))&&(this.focusNode=null),this.nodeRemoveMarkup(s),this.nodeRemoveChildren(s),t((r=e.inArray(i,a))>=0,"invalid child"),o.triggerModifyChild("remove",i),i.visit(function(e){e.parent=null},!0),this._callHook("treeRegisterNode",this,!1,i),a.splice(r,1)},nodeRemoveChildMarkup:function(t){var n=t.node;n.ul&&(n.isRootNode()?e(n.ul).empty():(e(n.ul).remove(),n.ul=null),n.visit(function(e){e.li=e.ul=null}))},nodeRemoveChildren:function(t){var n=t.tree,i=t.node;i.children&&(this.activeNode&&this.activeNode.isDescendantOf(i)&&this.activeNode.setActive(!1),this.focusNode&&this.focusNode.isDescendantOf(i)&&(this.focusNode=null),this.nodeRemoveChildMarkup(t),e.extend({},t),i.triggerModifyChild("remove",null),i.visit(function(e){e.parent=null,n._callHook("treeRegisterNode",n,!1,e)}),i.lazy?i.children=[]:i.children=null,i.isRootNode()||(i.expanded=!1),this.nodeRenderStatus(t))},nodeRemoveMarkup:function(t){var n=t.node;n.li&&(e(n.li).remove(),n.li=null),this.nodeRemoveChildMarkup(t)},nodeRender:function(n,i,r,o,s){var a,l,d,c,u,h,f,p=n.node,g=n.tree,v=n.options,y=v.aria,b=!1,m=p.parent,x=!m,k=p.children,N=null;if(!1!==g._enableUpdate&&(x||m.ul)){if(t(x||m.ul,"parent UL must exist"),x||(p.li&&(i||p.li.parentNode!==p.parent.ul)&&(p.li.parentNode===p.parent.ul?N=p.li.nextSibling:this.debug("Unlinking "+p+" (must be child of "+p.parent+")"),this.nodeRemoveMarkup(n)),p.li?this.nodeRenderStatus(n):(b=!0,p.li=document.createElement("li"),p.li.ftnode=p,p.key&&v.generateIds&&(p.li.id=v.idPrefix+p.key),p.span=document.createElement("span"),p.span.className="fancytree-node",y&&!p.tr&&e(p.li).attr("role","treeitem"),p.li.appendChild(p.span),this.nodeRenderTitle(n),v.createNode&&v.createNode.call(g,{type:"createNode"},n)),v.renderNode&&v.renderNode.call(g,{type:"renderNode"},n)),k){if(x||p.expanded||!0===r){for(p.ul||(p.ul=document.createElement("ul"),(!0!==o||s)&&p.expanded||(p.ul.style.display="none"),y&&e(p.ul).attr("role","group"),p.li?p.li.appendChild(p.ul):p.tree.$div.append(p.ul)),c=0,u=k.length;c<u;c++)f=e.extend({},n,{node:k[c]}),this.nodeRender(f,i,r,!1,!0);for(a=p.ul.firstChild;a;)(d=a.ftnode)&&d.parent!==p?(p.debug("_fixParent: remove missing "+d,a),h=a.nextSibling,a.parentNode.removeChild(a),a=h):a=a.nextSibling;for(a=p.ul.firstChild,c=0,u=k.length-1;c<u;c++)(l=k[c])!==(d=a.ftnode)?p.ul.insertBefore(l.li,d.li):a=a.nextSibling}}else p.ul&&(this.warn("remove child markup for "+p),this.nodeRemoveChildMarkup(n));x||b&&m.ul.insertBefore(p.li,N)}},nodeRenderTitle:function(t,n){var i,r,o,s,a,l,c,u=t.node,h=t.tree,f=t.options,p=f.aria,y=u.getLevel(),b=[];void 0!==n&&(u.title=n),u.span&&!1!==h._enableUpdate&&(a=p&&!1!==u.hasChildren()?" role='button'":"",y<f.minExpandLevel?(u.lazy||(u.expanded=!0),y>1&&b.push("<span "+a+" class='fancytree-expander fancytree-expander-fixed'></span>")):b.push("<span "+a+" class='fancytree-expander'></span>"),(i=g.evalOption("checkbox",u,u,f,!1))&&!u.isStatusNode()&&(a=p?" role='checkbox'":"",r="fancytree-checkbox",("radio"===i||u.parent&&u.parent.radiogroup)&&(r+=" fancytree-radio"),b.push("<span "+a+" class='"+r+"'></span>")),void 0!==u.data.iconClass&&(u.icon?e.error("'iconClass' node option is deprecated since v2.14.0: use 'icon' only instead"):(u.warn("'iconClass' node option is deprecated since v2.14.0: use 'icon' instead"),u.icon=u.data.iconClass)),!1!==(o=g.evalOption("icon",u,u,f,!0))&&(a=p?" role='presentation'":"",c=(c=g.evalOption("iconTooltip",u,u,f,null))?" title='"+d(c)+"'":"","string"==typeof o?v.test(o)?(o="/"===o.charAt(0)?o:(f.imagePath||"")+o,b.push("<img src='"+o+"' class='fancytree-icon'"+c+" alt='' />")):b.push("<span "+a+" class='fancytree-custom-icon "+o+"'"+c+"></span>"):o.text?b.push("<span "+a+" class='fancytree-custom-icon "+(o.addClass||"")+"'"+c+">"+g.escapeHtml(o.text)+"</span>"):o.html?b.push("<span "+a+" class='fancytree-custom-icon "+(o.addClass||"")+"'"+c+">"+o.html+"</span>"):b.push("<span "+a+" class='fancytree-icon'"+c+"></span>")),s="",f.renderTitle&&(s=f.renderTitle.call(h,{type:"renderTitle"},t)||""),s||(!0===(l=g.evalOption("tooltip",u,u,f,null))&&(l=u.title),s="<span class='fancytree-title'"+(l=l?" title='"+d(l)+"'":"")+(f.titlesTabbable?" tabindex='0'":"")+">"+(f.escapeTitles?g.escapeHtml(u.title):u.title)+"</span>"),b.push(s),u.span.innerHTML=b.join(""),this.nodeRenderStatus(t),f.enhanceTitle&&(t.$title=e(">span.fancytree-title",u.span),s=f.enhanceTitle.call(h,{type:"enhanceTitle"},t)||""))},nodeRenderStatus:function(t){var n,i=t.node,r=t.tree,o=t.options,s=i.hasChildren(),a=i.isLastSibling(),l=o.aria,d=o._classNames,c=[],u=i[r.statusClassPropName];u&&!1!==r._enableUpdate&&(l&&(n=e(i.tr||i.li)),c.push(d.node),r.activeNode===i&&c.push(d.active),r.focusNode===i&&c.push(d.focused),i.expanded&&c.push(d.expanded),l&&(!1!==s?n.attr("aria-expanded",Boolean(i.expanded)):n.removeAttr("aria-expanded")),i.folder&&c.push(d.folder),!1!==s&&c.push(d.hasChildren),a&&c.push(d.lastsib),i.lazy&&null==i.children&&c.push(d.lazy),i.partload&&c.push(d.partload),i.partsel&&c.push(d.partsel),g.evalOption("unselectable",i,i,o,!1)&&c.push(d.unselectable),i._isLoading&&c.push(d.loading),i._error&&c.push(d.error),i.statusNodeType&&c.push(d.statusNodePrefix+i.statusNodeType),i.selected?(c.push(d.selected),l&&n.attr("aria-selected",!0)):l&&n.attr("aria-selected",!1),i.extraClasses&&c.push(i.extraClasses),!1===s?c.push(d.combinedExpanderPrefix+"n"+(a?"l":"")):c.push(d.combinedExpanderPrefix+(i.expanded?"e":"c")+(i.lazy&&null==i.children?"d":"")+(a?"l":"")),c.push(d.combinedIconPrefix+(i.expanded?"e":"c")+(i.folder?"f":"")),u.className=c.join(" "),i.li&&e(i.li).toggleClass(d.lastsib,a))},nodeSetActive:function(n,i,r){r=r||{};var a,l=n.node,d=n.tree,c=n.options,u=!0===r.noEvents,h=!0===r.noFocus;return i=!1!==i,l===d.activeNode===i?o(l):i&&!u&&!1===this._triggerNodeEvent("beforeActivate",l,n.originalEvent)?s(l,["rejected"]):(i?(d.activeNode&&(t(d.activeNode!==l,"node was active (inconsistency)"),a=e.extend({},n,{node:d.activeNode}),d.nodeSetActive(a,!1),t(null===d.activeNode,"deactivate was out of sync?")),c.activeVisible&&l.makeVisible({scrollIntoView:h&&null==d.focusNode}),d.activeNode=l,d.nodeRenderStatus(n),h||d.nodeSetFocus(n),u||d._triggerNodeEvent("activate",l,n.originalEvent)):(t(d.activeNode===l,"node was not active (inconsistency)"),d.activeNode=null,this.nodeRenderStatus(n),u||n.tree._triggerNodeEvent("deactivate",l,n.originalEvent)),o(l))},nodeSetExpanded:function(t,n,i){i=i||{};var r,a,l,d,c,u,h=t.node,f=t.tree,p=t.options,g=!0===i.noAnimation,v=!0===i.noEvents;if(n=!1!==n,h.expanded&&n||!h.expanded&&!n)return o(h);if(n&&!h.lazy&&!h.hasChildren())return o(h);if(!n&&h.getLevel()<p.minExpandLevel)return s(h,["locked"]);if(!v&&!1===this._triggerNodeEvent("beforeExpand",h,t.originalEvent))return s(h,["rejected"]);if(g||h.isVisible()||(g=i.noAnimation=!0),a=new e.Deferred,n&&!h.expanded&&p.autoCollapse){c=h.getParentList(!1,!0),u=p.autoCollapse;try{for(p.autoCollapse=!1,l=0,d=c.length;l<d;l++)this._callHook("nodeCollapseSiblings",c[l],i)}finally{p.autoCollapse=u}}return a.done(function(){var e=h.getLastChild();n&&p.autoScroll&&!g&&e?e.scrollIntoView(!0,{topNode:h}).always(function(){v||t.tree._triggerNodeEvent(n?"expand":"collapse",t)}):v||t.tree._triggerNodeEvent(n?"expand":"collapse",t)}),r=function(i){var r,o,s=p._classNames,a=p.toggleEffect;if(h.expanded=n,f._callHook("nodeRender",t,!1,!1,!0),h.ul)if(r="none"!==h.ul.style.display,o=!!h.expanded,r===o)h.warn("nodeSetExpanded: UL.style.display already set");else{if(a&&!g)return e(h.li).addClass(s.animating),void e(h.ul).addClass(s.animating).toggle(a.effect,a.options,a.duration,function(){e(this).removeClass(s.animating),e(h.li).removeClass(s.animating),i()});h.ul.style.display=h.expanded||!parent?"":"none"}i()},n&&h.lazy&&void 0===h.hasChildren()?h.load().done(function(){a.notifyWith&&a.notifyWith(h,["loaded"]),r(function(){a.resolveWith(h)})}).fail(function(e){r(function(){a.rejectWith(h,["load failed ("+e+")"])})}):r(function(){a.resolveWith(h)}),a.promise()},nodeSetFocus:function(t,n){var i,r=t.tree,o=t.node,s=r.options,a=!!t.originalEvent&&e(t.originalEvent.target).is(":input");if(n=!1!==n,r.focusNode){if(r.focusNode===o&&n)return;i=e.extend({},t,{node:r.focusNode}),r.focusNode=null,this._triggerNodeEvent("blur",i),this._callHook("nodeRenderStatus",i)}n&&(this.hasFocus()||(o.debug("nodeSetFocus: forcing container focus"),this._callHook("treeSetFocus",t,!0,{calledByNode:!0})),o.makeVisible({scrollIntoView:!1}),r.focusNode=o,s.titlesTabbable?a||e(o.span).find(".fancytree-title").focus():0===e(document.activeElement).closest(".fancytree-container").length&&e(r.$container).focus(),s.aria&&e(r.$container).attr("aria-activedescendant",e(o.tr||o.li).uniqueId().attr("id")),this._triggerNodeEvent("focus",t),s.autoScroll&&o.scrollIntoView(),this._callHook("nodeRenderStatus",t))},nodeSetSelected:function(e,t,n){n=n||{};var i=e.node,r=e.tree,o=e.options,s=!0===n.noEvents,a=i.parent;if(t=!1!==t,!g.evalOption("unselectable",i,i,o,!1)){if(i._lastSelectIntent=t,!!i.selected===t&&(3!==o.selectMode||!i.partsel||t))return t;if(!s&&!1===this._triggerNodeEvent("beforeSelect",i,e.originalEvent))return!!i.selected;t&&1===o.selectMode?(r.lastSelectedNode&&r.lastSelectedNode.setSelected(!1),i.selected=t):3!==o.selectMode||!a||a.radiogroup||i.radiogroup?a&&a.radiogroup?i.visitSiblings(function(e){e._changeSelectStatusAttrs(t&&e===i)},!0):i.selected=t:(i.selected=t,i.fixSelection3AfterClick(n)),this.nodeRenderStatus(e),r.lastSelectedNode=t?i:null,s||r._triggerNodeEvent("select",e)}},nodeSetStatus:function(t,n,i,r){function o(t,n){var i=s.children?s.children[0]:null;return i&&i.isStatusNode()?(e.extend(i,t),i.statusNodeType=n,a._callHook("nodeRenderTitle",i)):(s._setChildren([t]),s.children[0].statusNodeType=n,a.render()),s.children[0]}var s=t.node,a=t.tree;switch(n){case"ok":!function(){var e=s.children?s.children[0]:null;if(e&&e.isStatusNode()){try{s.ul&&(s.ul.removeChild(e.li),e.li=null)}catch(e){}1===s.children.length?s.children=[]:s.children.shift()}}(),s._isLoading=!1,s._error=null,s.renderStatus();break;case"loading":s.parent||o({title:a.options.strings.loading+(i?" ("+i+")":""),checkbox:!1,tooltip:r},n),s._isLoading=!0,s._error=null,s.renderStatus();break;case"error":o({title:a.options.strings.loadError+(i?" ("+i+")":""),checkbox:!1,tooltip:r},n),s._isLoading=!1,s._error={message:i,details:r},s.renderStatus();break;case"nodata":o({title:a.options.strings.noData,checkbox:!1,tooltip:r},n),s._isLoading=!1,s._error=null,s.renderStatus();break;default:e.error("invalid node status "+n)}},nodeToggleExpanded:function(e){return this.nodeSetExpanded(e,!e.node.expanded)},nodeToggleSelected:function(e){var t=e.node,n=!t.selected;return t.partsel&&!t.selected&&!0===t._lastSelectIntent&&(n=!1,t.selected=!0),t._lastSelectIntent=n,this.nodeSetSelected(e,n)},treeClear:function(e){var t=e.tree;t.activeNode=null,t.focusNode=null,t.$div.find(">ul.fancytree-container").empty(),t.rootNode.children=null},treeCreate:function(e){},treeDestroy:function(e){this.$div.find(">ul.fancytree-container").remove(),this.$source&&this.$source.removeClass("fancytree-helper-hidden")},treeInit:function(t){var n=t.tree,i=n.options;n.$container.attr("tabindex",i.tabindex),e.each(w,function(e,t){void 0!==i[t]&&(n.info("Move option "+t+" to tree"),n[t]=i[t],delete i[t])}),i.rtl?n.$container.attr("DIR","RTL").addClass("fancytree-rtl"):n.$container.removeAttr("DIR").removeClass("fancytree-rtl"),i.aria&&(n.$container.attr("role","tree"),1!==i.selectMode&&n.$container.attr("aria-multiselectable",!0)),this.treeLoad(t)},treeLoad:function(n,i){var r,o,s,a=n.tree,d=n.widget.element,c=e.extend({},n,{node:this.rootNode});if(a.rootNode.children&&this.treeClear(n),i=i||this.options.source)"string"==typeof i&&e.error("Not implemented");else switch(o=d.data("type")||"html"){case"html":(s=d.find(">ul:first")).addClass("ui-fancytree-source fancytree-helper-hidden"),i=e.ui.fancytree.parseHtml(s),this.data=e.extend(this.data,l(s));break;case"json":i=e.parseJSON(d.text()),d.contents().filter(function(){return 3===this.nodeType}).remove(),e.isPlainObject(i)&&(t(e.isArray(i.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),r=i,i=i.children,delete r.children,e.each(w,function(e,t){void 0!==r[t]&&(a[t]=r[t],delete r[t])}),e.extend(a.data,r));break;default:e.error("Invalid data-type: "+o)}return this.nodeLoadChildren(c,i).done(function(){a.render(),3===n.options.selectMode&&a.rootNode.fixSelection3FromEndNodes(),a.activeNode&&a.options.activeVisible&&a.activeNode.makeVisible(),a._triggerTreeEvent("init",null,{status:!0})}).fail(function(){a.render(),a._triggerTreeEvent("init",null,{status:!1})})},treeRegisterNode:function(e,t,n){},treeSetFocus:function(t,n,i){var r;(n=!1!==n)!==this.hasFocus()&&(this._hasFocus=n,!n&&this.focusNode?this.focusNode.setFocus(!1):!n||i&&i.calledByNode||e(this.$container).focus(),this.$container.toggleClass("fancytree-treefocus",n),this._triggerTreeEvent(n?"focusTree":"blurTree"),n&&!this.activeNode&&(r=this._lastMousedownNode||this.getFirstChild())&&r.setFocus())},treeSetOption:function(t,n,i){var r=t.tree,o=!0,s=!1,a=!1;switch(n){case"aria":case"checkbox":case"icon":case"minExpandLevel":case"tabindex":s=!0,a=!0;break;case"escapeTitles":case"tooltip":a=!0;break;case"rtl":!1===i?r.$container.removeAttr("DIR").removeClass("fancytree-rtl"):r.$container.attr("DIR","RTL").addClass("fancytree-rtl"),a=!0;break;case"source":o=!1,r._callHook("treeLoad",r,i),a=!0}r.debug("set option "+n+"="+i+" <"+typeof i+">"),o&&(this.widget._super?this.widget._super.call(this.widget,n,i):e.Widget.prototype._setOption.call(this.widget,n,i)),s&&r._callHook("treeCreate",r),a&&r.render(!0,!1)}}),e.widget("ui.fancytree",{options:{activeVisible:!0,ajax:{type:"GET",cache:!1,dataType:"json"},aria:!0,autoActivate:!0,autoCollapse:!1,autoScroll:!1,checkbox:!1,clickFolderMode:4,debugLevel:null,disabled:!1,enableAspx:!0,escapeTitles:!1,extensions:[],toggleEffect:{effect:"blind",options:{direction:"vertical",scale:"box"},duration:200},generateIds:!1,icon:!0,idPrefix:"ft_",focusOnSelect:!1,keyboard:!0,keyPathSeparator:"/",minExpandLevel:1,quicksearch:!1,rtl:!1,scrollOfs:{top:0,bottom:0},scrollParent:null,selectMode:2,strings:{loading:"Loading...",loadError:"Load error!",moreData:"More...",noData:"No data."},tabindex:"0",titlesTabbable:!1,tooltip:!1,_classNames:{node:"fancytree-node",folder:"fancytree-folder",animating:"fancytree-animating",combinedExpanderPrefix:"fancytree-exp-",combinedIconPrefix:"fancytree-ico-",hasChildren:"fancytree-has-children",active:"fancytree-active",selected:"fancytree-selected",expanded:"fancytree-expanded",lazy:"fancytree-lazy",focused:"fancytree-focused",partload:"fancytree-partload",partsel:"fancytree-partsel",radio:"fancytree-radio",unselectable:"fancytree-unselectable",lastsib:"fancytree-lastsib",loading:"fancytree-loading",error:"fancytree-error",statusNodePrefix:"fancytree-statusnode-"},lazyLoad:null,postProcess:null},_create:function(){this.tree=new h(this),this.$source=this.source||"json"===this.element.data("type")?this.element:this.element.find(">ul:first");var n,i,o,s=this.options,a=s.extensions;this.tree;for(o=0;o<a.length;o++)i=a[o],(n=e.ui.fancytree._extensions[i])||e.error("Could not apply extension '"+i+"' (it is not registered, did you forget to include it?)"),this.tree.options[i]=e.extend(!0,{},n.options,this.tree.options[i]),t(void 0===this.tree.ext[i],"Extension name must not exist as Fancytree.ext attribute: '"+i+"'"),this.tree.ext[i]={},r(this.tree,0,n,i),n;void 0!==s.icons&&(!0!==s.icon?e.error("'icons' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'icons' tree option is deprecated since v2.14.0: use 'icon' instead"),s.icon=s.icons)),void 0!==s.iconClass&&(s.icon?e.error("'iconClass' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'iconClass' tree option is deprecated since v2.14.0: use 'icon' instead"),s.icon=s.iconClass)),void 0!==s.tabbable&&(s.tabindex=s.tabbable?"0":"-1",this.tree.warn("'tabbable' tree option is deprecated since v2.17.0: use 'tabindex='"+s.tabindex+"' instead")),this.tree._callHook("treeCreate",this.tree)},_init:function(){this.tree._callHook("treeInit",this.tree),this._bind()},_setOption:function(e,t){return this.tree._callHook("treeSetOption",this.tree,e,t)},destroy:function(){this._unbind(),this.tree._callHook("treeDestroy",this.tree),e.Widget.prototype.destroy.call(this)},_unbind:function(){var t=this.tree._ns;this.element.off(t),this.tree.$container.off(t),e(document).off(t)},_bind:function(){var t=this,n=this.options,i=this.tree,r=i._ns;this._unbind(),i.$container.on("focusin"+r+" focusout"+r,function(t){var n=g.getNode(t),r="focusin"===t.type;r&&i._getExpiringValue("focusin")?g.info("Ignored double focusin."):(i._setExpiringValue("focusin",!0,50),r&&!n&&(n=i._getExpiringValue("mouseDownNode"))&&g.info("Reconstruct mouse target for focusin from recent event."),n?i._callHook("nodeSetFocus",i._makeHookContext(n,t),r):i.tbody&&e(t.target).parents("table.fancytree-container > thead").length?i.debug("Ignore focus event outside table body.",t):i._callHook("treeSetFocus",i,r))}).on("selectstart"+r,"span.fancytree-title",function(e){e.preventDefault()}).on("keydown"+r,function(e){if(n.disabled||!1===n.keyboard)return!0;var t,r=i.focusNode,o=i._makeHookContext(r||i,e),s=i.phase;try{return i.phase="userEvent","preventNav"===(t=r?i._triggerNodeEvent("keydown",r,e):i._triggerTreeEvent("keydown",e))?t=!0:!1!==t&&(t=i._callHook("nodeKeydown",o)),t}finally{i.phase=s}}).on("mousedown"+r,function(e){var t=g.getEventTarget(e);i._lastMousedownNode=t?t.node:null,i._setExpiringValue("mouseDownNode",i._lastMousedownNode)}).on("click"+r+" dblclick"+r,function(e){if(n.disabled)return!0;var i,r=g.getEventTarget(e),o=r.node,s=t.tree,a=s.phase;if(!o)return!0;i=s._makeHookContext(o,e);try{switch(s.phase="userEvent",e.type){case"click":return i.targetType=r.type,o.isPagingNode()?!0===s._triggerNodeEvent("clickPaging",i,e):!1!==s._triggerNodeEvent("click",i,e)&&s._callHook("nodeClick",i);case"dblclick":return i.targetType=r.type,!1!==s._triggerNodeEvent("dblclick",i,e)&&s._callHook("nodeDblclick",i)}}finally{s.phase=a}})},getActiveNode:function(){return this.tree.activeNode},getNodeByKey:function(e){return this.tree.getNodeByKey(e)},getRootNode:function(){return this.tree.rootNode},getTree:function(){return this.tree}}),g=e.ui.fancytree,e.extend(e.ui.fancytree,{version:"2.28.0",buildType: "production",debugLevel: 3,_nextId:1,_nextNodeKey:1,_extensions:{},_FancytreeClass:h,_FancytreeNodeClass:u,jquerySupports:{positionMyOfs:function(t,n,i,r){var o,s,a,l=e.map(e.trim(t).split("."),function(e){return parseInt(e,10)}),d=e.map(Array.prototype.slice.call(arguments,1),function(e){return parseInt(e,10)});for(o=0;o<d.length;o++)if(s=l[o]||0,a=d[o]||0,s!==a)return s>a;return!0}(e.ui.version,1,9)},assert:function(e,n){return t(e,n)},createTree:function(t,n){return e(t).fancytree(n).fancytree("getTree")},debounce:function(e,t,n,i){var r;return 3===arguments.length&&"boolean"!=typeof n&&(i=n,n=!1),function(){var o=arguments;i=i||this,n&&!r&&t.apply(i,o),clearTimeout(r),r=setTimeout(function(){n||t.apply(i,o),r=null},e)}},debug:function(t){e.ui.fancytree.debugLevel>=4&&n("log",arguments)},error:function(t){e.ui.fancytree.debugLevel>=1&&n("error",arguments)},escapeHtml:function(e){return(""+e).replace(y,function(e){return x[e]})},fixPositionOptions:function(t){if((t.offset||(""+t.my+t.at).indexOf("%")>=0)&&e.error("expected new position syntax (but '%' is not supported)"),!e.ui.fancytree.jquerySupports.positionMyOfs){var n=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(t.my),i=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(t.at),r=(n[2]?+n[2]:0)+(i[2]?+i[2]:0),o=(n[4]?+n[4]:0)+(i[4]?+i[4]:0);t=e.extend({},t,{my:n[1]+" "+n[3],at:i[1]+" "+i[3]}),(r||o)&&(t.offset=r+" "+o)}return t},getEventTarget:function(t){var n,i=t&&t.target?t.target.className:"",r={node:this.getNode(t.target),type:void 0};return/\bfancytree-title\b/.test(i)?r.type="title":/\bfancytree-expander\b/.test(i)?r.type=!1===r.node.hasChildren()?"prefix":"expander":/\bfancytree-checkbox\b/.test(i)?r.type="checkbox":/\bfancytree(-custom)?-icon\b/.test(i)?r.type="icon":/\bfancytree-node\b/.test(i)?r.type="title":t&&t.target&&((n=e(t.target)).is("ul[role=group]")?(g.info("Ignoring click on outer UL."),r.node=null):n.closest(".fancytree-title").length?r.type="title":n.closest(".fancytree-checkbox").length?r.type="checkbox":n.closest(".fancytree-expander").length&&(r.type="expander")),r},getEventTargetType:function(e){return this.getEventTarget(e).type},getNode:function(t){if(t instanceof u)return t;for(t instanceof e?t=t[0]:void 0!==t.originalEvent&&(t=t.target);t;){if(t.ftnode)return t.ftnode;t=t.parentNode}return null},getTree:function(t){var n;return t instanceof h?t:(void 0===t&&(t=0),"number"==typeof t?t=e(".fancytree-container").eq(t):"string"==typeof t?t=e(t).eq(0):void 0!==t.selector?t=t.eq(0):void 0!==t.originalEvent&&(t=e(t.target)),t=t.closest(":ui-fancytree"),(n=t.data("ui-fancytree")||t.data("fancytree"))?n.tree:null)},evalOption:function(t,n,i,r,o){var s,a,l=n.tree,d=r[t],c=i[t];return e.isFunction(d)?(s={node:n,tree:l,widget:l.widget,options:l.widget.options,typeInfo:l.types[n.type]||{}},null==(a=d.call(l,{type:t},s))&&(a=c)):a=null!=c?c:d,null==a&&(a=o),a},setSpanIcon:function(t,n,i){var r=e(t);"string"==typeof i?r.attr("class",n+" "+i):(i.text?r.text(""+i.text):i.html&&(t.innerHTML=i.html),r.attr("class",n+" "+(i.addClass||"")))},eventToString:function(e){var t=e.which,n=e.type,i=[];return e.altKey&&i.push("alt"),e.ctrlKey&&i.push("ctrl"),e.metaKey&&i.push("meta"),e.shiftKey&&i.push("shift"),"click"===n||"dblclick"===n?i.push(_[e.button]+n):k[t]||i.push(N[t]||String.fromCharCode(t).toLowerCase()),i.join("+")},info:function(t){e.ui.fancytree.debugLevel>=3&&n("info",arguments)},keyEventToString:function(e){return this.warn("keyEventToString() is deprecated: use eventToString()"),this.eventToString(e)},overrideMethod:function(t,n,i){var r,o=t[n]||e.noop;t[n]=function(){try{return r=this._super,this._super=o,i.apply(this,arguments)}finally{this._super=r}}},parseHtml:function(t){var n,i,r,o,s,a,d,c,u=[];return t.find(">li").each(function(){var h,f,p=e(this),g=p.find(">span:first",this),v=g.length?null:p.find(">a:first"),y={tooltip:null,data:{}};for(g.length?y.title=g.html():v&&v.length?(y.title=v.html(),y.data.href=v.attr("href"),y.data.target=v.attr("target"),y.tooltip=v.attr("title")):(y.title=p.html(),(s=y.title.search(/<ul/i))>=0&&(y.title=y.title.substring(0,s))),y.title=e.trim(y.title),o=0,a=C.length;o<a;o++)y[C[o]]=void 0;for(n=this.className.split(" "),r=[],o=0,a=n.length;o<a;o++)i=n[o],S[i]?y[i]=!0:r.push(i);if(y.extraClasses=r.join(" "),(d=p.attr("title"))&&(y.tooltip=d),(d=p.attr("id"))&&(y.key=d),p.attr("hideCheckbox")&&(y.checkbox=!1),(h=l(p))&&!e.isEmptyObject(h)){for(f in L)h.hasOwnProperty(f)&&(h[L[f]]=h[f],delete h[f]);for(o=0,a=E.length;o<a;o++)d=E[o],null!=(c=h[d])&&(delete h[d],y[d]=c);e.extend(y.data,h)}(t=p.find(">ul:first")).length?y.children=e.ui.fancytree.parseHtml(t):y.children=y.lazy?void 0:null,u.push(y)}),u},registerExtension:function(n){t(null!=n.name,"extensions must have a `name` property."),t(null!=n.version,"extensions must have a `version` property."),e.ui.fancytree._extensions[n.name]=n},unescapeHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,0===t.childNodes.length?"":t.childNodes[0].nodeValue},warn:function(t){e.ui.fancytree.debugLevel>=2&&n("warn",arguments)}}),e.ui.fancytree}e.ui.fancytree.warn("Fancytree: ignored duplicate include")}});

/*! Extension 'jquery.fancytree.childcounter.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";return e.ui.fancytree._FancytreeClass.prototype.countSelected=function(e){this.options;return this.getSelectedNodes(e).length},e.ui.fancytree._FancytreeNodeClass.prototype.updateCounters=function(){var t=e("span.fancytree-childcounter",this.span),n=this.tree.options.childcounter,i=this.countChildren(n.deep);this.data.childCounter=i,!i&&n.hideZeros||this.isExpanded()&&n.hideExpanded?t.remove():(t.length||(t=e("<span class='fancytree-childcounter'/>").appendTo(e("span.fancytree-icon",this.span))),t.text(i)),!n.deep||this.isTopLevel()||this.isRoot()||this.parent.updateCounters()},e.ui.fancytree.prototype.widgetMethod1=function(e){this.tree;return e},e.ui.fancytree.registerExtension({name:"childcounter",version:"2.28.0",options:{deep:!0,hideZeros:!0,hideExpanded:!1},foo:42,_appendCounter:function(e){},treeInit:function(e){e.options,e.options.childcounter;this._superApply(arguments),this.$container.addClass("fancytree-ext-childcounter")},treeDestroy:function(e){this._superApply(arguments)},nodeRenderTitle:function(t,n){var i=t.node,o=t.options.childcounter,r=null==i.data.childCounter?i.countChildren(o.deep):+i.data.childCounter;this._super(t,n),!r&&o.hideZeros||i.isExpanded()&&o.hideExpanded||e("span.fancytree-icon",i.span).append(e("<span class='fancytree-childcounter'/>").text(r))},nodeSetExpanded:function(e,t,n){var i=e.tree;e.node;return this._superApply(arguments).always(function(){i.nodeRenderTitle(e)})}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.clones.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function t(t,n){t||(n=n?": "+n:"",e.error("Assertion failed"+n))}return e.ui.fancytree._FancytreeNodeClass.prototype.getCloneList=function(t){var n,r=this.tree,s=r.refMap[this.refKey]||null,i=r.keyMap;return s&&(n=this.key,t?s=e.map(s,function(e){return i[e]}):(s=e.map(s,function(e){return e===n?null:i[e]})).length<1&&(s=null)),s},e.ui.fancytree._FancytreeNodeClass.prototype.isClone=function(){var e=this.refKey||null,t=e&&this.tree.refMap[e]||null;return!!(t&&t.length>1)},e.ui.fancytree._FancytreeNodeClass.prototype.reRegister=function(t,n){t=null==t?null:""+t,n=null==n?null:""+n;var r=this.tree,s=this.key,i=this.refKey,o=r.keyMap,l=r.refMap,u=l[i]||null,a=!1;return null!=t&&t!==this.key&&(o[t]&&e.error("[ext-clones] reRegister("+t+"): already exists: "+this),delete o[s],o[t]=this,u&&(l[i]=e.map(u,function(e){return e===s?t:e})),this.key=t,a=!0),null!=n&&n!==this.refKey&&(u&&(1===u.length?delete l[i]:l[i]=e.map(u,function(e){return e===s?null:e})),l[n]?l[n].append(t):l[n]=[this.key],this.refKey=n,a=!0),a},e.ui.fancytree._FancytreeNodeClass.prototype.setRefKey=function(e){return this.reRegister(null,e)},e.ui.fancytree._FancytreeClass.prototype.getNodesByRef=function(t,n){var r=this.keyMap,s=this.refMap[t]||null;return s&&(s=n?e.map(s,function(e){var t=r[e];return t.isDescendantOf(n)?t:null}):e.map(s,function(e){return r[e]})).length<1&&(s=null),s},e.ui.fancytree._FancytreeClass.prototype.changeRefKey=function(e,t){var n,r=this.keyMap,s=this.refMap[e]||null;if(s){for(n=0;n<s.length;n++)r[s[n]].refKey=t;delete this.refMap[e],this.refMap[t]=s}},e.ui.fancytree.registerExtension({name:"clones",version:"2.28.0",options:{highlightActiveClones:!0,highlightClones:!1},treeCreate:function(e){this._superApply(arguments),e.tree.refMap={},e.tree.keyMap={}},treeInit:function(n){this.$container.addClass("fancytree-ext-clones"),t(null==n.options.defaultKey),n.options.defaultKey=function(t){return function(t){var n=e.map(t.getParentList(!1,!0),function(e){return e.refKey||e.key});return n=n.join("/"),"id_"+function(e,t,n){for(var r,s,i=3&e.length,o=e.length-i,l=n,u=3432918353,a=0;a<o;)s=255&e.charCodeAt(a)|(255&e.charCodeAt(++a))<<8|(255&e.charCodeAt(++a))<<16|(255&e.charCodeAt(++a))<<24,++a,l=27492+(65535&(r=5*(65535&(l=(l^=s=461845907*(65535&(s=(s=(65535&s)*u+(((s>>>16)*u&65535)<<16)&4294967295)<<15|s>>>17))+((461845907*(s>>>16)&65535)<<16)&4294967295)<<13|l>>>19))+((5*(l>>>16)&65535)<<16)&4294967295))+((58964+(r>>>16)&65535)<<16);switch(s=0,i){case 3:s^=(255&e.charCodeAt(a+2))<<16;case 2:s^=(255&e.charCodeAt(a+1))<<8;case 1:l^=s=461845907*(65535&(s=(s=(65535&(s^=255&e.charCodeAt(a)))*u+(((s>>>16)*u&65535)<<16)&4294967295)<<15|s>>>17))+((461845907*(s>>>16)&65535)<<16)&4294967295}return l^=e.length,l^=l>>>16,l=2246822507*(65535&l)+((2246822507*(l>>>16)&65535)<<16)&4294967295,l^=l>>>13,l=3266489909*(65535&l)+((3266489909*(l>>>16)&65535)<<16)&4294967295,l^=l>>>16,t?("0000000"+(l>>>0).toString(16)).substr(-8):l>>>0}(n,!0)}(t)},this._superApply(arguments)},treeClear:function(e){return e.tree.refMap={},e.tree.keyMap={},this._superApply(arguments)},treeRegisterNode:function(n,r,s){var i,o,l=n.tree,u=l.keyMap,a=l.refMap,c=s.key,f=s&&null!=s.refKey?""+s.refKey:null;return s.isStatusNode()?this._super(n,r,s):(r?(null!=u[s.key]&&e.error("clones.treeRegisterNode: node.key already exists: "+s),u[c]=s,f&&((i=a[f])?(i.push(c),2===i.length&&n.options.clones.highlightClones&&u[i[0]].renderStatus()):a[f]=[c])):(null==u[c]&&e.error("clones.treeRegisterNode: node.key not registered: "+s.key),delete u[c],f&&(i=a[f])&&((o=i.length)<=1?(t(1===o),t(i[0]===c),delete a[f]):(!function(e,t){var n;for(n=e.length-1;n>=0;n--)if(e[n]===t)return e.splice(n,1),!0}(i,c),2===o&&n.options.clones.highlightClones&&u[i[0]].renderStatus()))),this._super(n,r,s))},nodeRenderStatus:function(t){var n,r,s=t.node;return r=this._super(t),t.options.clones.highlightClones&&(n=e(s[t.tree.statusClassPropName])).length&&s.isClone()&&n.addClass("fancytree-clone"),r},nodeSetActive:function(t,n,r){var s,i=t.tree.statusClassPropName,o=t.node;return s=this._superApply(arguments),t.options.clones.highlightActiveClones&&o.isClone()&&e.each(o.getCloneList(!0),function(t,r){e(r[i]).toggleClass("fancytree-active-clone",!1!==n)}),s}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.dnd.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","jquery-ui/ui/widgets/draggable","jquery-ui/ui/widgets/droppable","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function r(r){var a=r.options.dnd||null,n=r.options.glyph||null;a&&(t||(e.ui.plugin.add("draggable","connectToFancytree",{start:function(r,t){var a=e(this).data("ui-draggable")||e(this).data("draggable"),n=t.helper.data("ftSourceNode")||null;if(n)return a.offset.click.top=-2,a.offset.click.left=16,n.tree.ext.dnd._onDragEvent("start",n,null,r,t,a)},drag:function(r,t){var a,n=e(this).data("ui-draggable")||e(this).data("draggable"),o=t.helper.data("ftSourceNode")||null,d=t.helper.data("ftTargetNode")||null,l=e.ui.fancytree.getNode(r.target),s=o&&o.tree.options.dnd;r.target&&!l&&e(r.target).closest("div.fancytree-drag-helper,#fancytree-drop-marker").length>0?(o||d||e.ui.fancytree).debug("Drag event over helper: ignored."):(t.helper.data("ftTargetNode",l),s&&s.updateHelper&&(a=o.tree._makeHookContext(o,r,{otherNode:l,ui:t,draggable:n,dropMarker:e("#fancytree-drop-marker")}),s.updateHelper.call(o.tree,o,a)),d&&d!==l&&d.tree.ext.dnd._onDragEvent("leave",d,o,r,t,n),l&&l.tree.options.dnd.dragDrop&&(l===d?l.tree.ext.dnd._onDragEvent("over",l,o,r,t,n):(l.tree.ext.dnd._onDragEvent("enter",l,o,r,t,n),l.tree.ext.dnd._onDragEvent("over",l,o,r,t,n))))},stop:function(r,t){var a=e(this).data("ui-draggable")||e(this).data("draggable"),n=t.helper.data("ftSourceNode")||null,o=t.helper.data("ftTargetNode")||null,d="mouseup"===r.type&&1===r.which;d||(n||o||e.ui.fancytree).debug("Drag was cancelled"),o&&(d&&o.tree.ext.dnd._onDragEvent("drop",o,n,r,t,a),o.tree.ext.dnd._onDragEvent("leave",o,n,r,t,a)),n&&n.tree.ext.dnd._onDragEvent("stop",n,null,r,t,a)}}),t=!0)),a&&a.dragStart&&r.widget.element.draggable(e.extend({addClasses:!1,appendTo:r.$container,containment:!1,delay:0,distance:4,revert:!1,scroll:!0,scrollSpeed:7,scrollSensitivity:10,connectToFancytree:!0,helper:function(r){var t,a,o,d=e.ui.fancytree.getNode(r.target);return d?(o=d.tree.options.dnd,a=e(d.span),(t=e("<div class='fancytree-drag-helper'><span class='fancytree-drag-helper-img' /></div>").css({zIndex:3,position:"relative"}).append(a.find("span.fancytree-title").clone())).data("ftSourceNode",d),n&&t.find(".fancytree-drag-helper-img").addClass(n.map._addClass+" "+n.map.dragHelper),o.initHelper&&o.initHelper.call(d.tree,d,{node:d,tree:d.tree,originalEvent:r,ui:{helper:t}}),t):"<div>ERROR?: helper requested but sourceNode not found</div>"},start:function(e,r){return!!r.helper.data("ftSourceNode")}},r.options.dnd.draggable)),a&&a.dragDrop&&r.widget.element.droppable(e.extend({addClasses:!1,tolerance:"intersect",greedy:!1},r.options.dnd.droppable))}var t=!1,a="fancytree-drop-accept",n="fancytree-drop-after",o="fancytree-drop-before",d="fancytree-drop-reject";return e.ui.fancytree.registerExtension({name:"dnd",version:"2.28.0",options:{autoExpandMS:1e3,draggable:null,droppable:null,focusOnClick:!1,preventVoidMoves:!0,preventRecursiveMoves:!0,smartRevert:!0,dropMarkerOffsetX:-24,dropMarkerInsertOffsetX:-16,dragStart:null,dragStop:null,initHelper:null,updateHelper:null,dragEnter:null,dragOver:null,dragExpand:null,dragDrop:null,dragLeave:null},treeInit:function(t){var a=t.tree;this._superApply(arguments),a.options.dnd.dragStart&&a.$container.on("mousedown",function(r){if(t.options.dnd.focusOnClick){var a=e.ui.fancytree.getNode(r);a&&a.debug("Re-enable focus that was prevented by jQuery UI draggable."),setTimeout(function(){e(r.target).closest(":tabbable").focus()},10)}}),r(a)},_setDndStatus:function(r,t,l,s,i){var p,g="center",u=this._local,c=this.options.dnd,f=this.options.glyph,v=r?e(r.span):null,h=e(t.span),y=h.find("span.fancytree-title");if(u.$dropMarker||(u.$dropMarker=e("<div id='fancytree-drop-marker'></div>").hide().css({"z-index":1e3}).prependTo(e(this.$div).parent()),f&&u.$dropMarker.addClass(f.map._addClass+" "+f.map.dropMarker)),"after"===s||"before"===s||"over"===s){switch(p=c.dropMarkerOffsetX||0,s){case"before":g="top",p+=c.dropMarkerInsertOffsetX||0;break;case"after":g="bottom",p+=c.dropMarkerInsertOffsetX||0}u.$dropMarker.toggleClass(n,"after"===s).toggleClass("fancytree-drop-over","over"===s).toggleClass(o,"before"===s).show().position(e.ui.fancytree.fixPositionOptions({my:"left"+function(e){return 0===e?"":e>0?"+"+e:""+e}(p)+" center",at:"left "+g,of:y}))}else u.$dropMarker.hide();v&&v.toggleClass(a,!0===i).toggleClass(d,!1===i),h.toggleClass("fancytree-drop-target","after"===s||"before"===s||"over"===s).toggleClass(n,"after"===s).toggleClass(o,"before"===s).toggleClass(a,!0===i).toggleClass(d,!1===i),l.toggleClass(a,!0===i).toggleClass(d,!1===i)},_onDragEvent:function(r,t,a,n,o,d){var l,s,i,p,g,u,c,f,v,h=this.options.dnd,y=this._makeHookContext(t,n,{otherNode:a,ui:o,draggable:d}),b=null,m=this,x=e(t.span);switch(h.smartRevert&&(d.options.revert="invalid"),r){case"start":t.isStatusNode()?b=!1:h.dragStart&&(b=h.dragStart(t,y)),!1===b?(this.debug("tree.dragStart() cancelled"),o.helper.trigger("mouseup").hide()):(h.smartRevert&&(p=t[y.tree.nodeContainerAttrName].getBoundingClientRect(),i=e(d.options.appendTo)[0].getBoundingClientRect(),d.originalPosition.left=Math.max(0,p.left-i.left),d.originalPosition.top=Math.max(0,p.top-i.top)),x.addClass("fancytree-drag-source"),e(document).on("keydown.fancytree-dnd,mousedown.fancytree-dnd",function(r){"keydown"===r.type&&r.which===e.ui.keyCode.ESCAPE?m.ext.dnd._cancelDrag():"mousedown"===r.type&&m.ext.dnd._cancelDrag()}));break;case"enter":b=!!(v=(!h.preventRecursiveMoves||!t.isDescendantOf(a))&&(h.dragEnter?h.dragEnter(t,y):null))&&(e.isArray(v)?{over:e.inArray("over",v)>=0,before:e.inArray("before",v)>=0,after:e.inArray("after",v)>=0}:{over:!0===v||"over"===v,before:!0===v||"before"===v,after:!0===v||"after"===v}),o.helper.data("enterResponse",b);break;case"over":f=null,!1===(c=o.helper.data("enterResponse"))||("string"==typeof c?f=c:(s=x.offset(),u={x:(g={x:n.pageX-s.left,y:n.pageY-s.top}).x/x.width(),y:g.y/x.height()},c.after&&u.y>.75?f="after":!c.over&&c.after&&u.y>.5?f="after":c.before&&u.y<=.25?f="before":!c.over&&c.before&&u.y<=.5?f="before":c.over&&(f="over"),h.preventVoidMoves&&(t===a?(this.debug("    drop over source node prevented"),f=null):"before"===f&&a&&t===a.getNextSibling()?(this.debug("    drop after source node prevented"),f=null):"after"===f&&a&&t===a.getPrevSibling()?(this.debug("    drop before source node prevented"),f=null):"over"===f&&a&&a.parent===t&&a.isLastSibling()&&(this.debug("    drop last child over own parent prevented"),f=null)),o.helper.data("hitMode",f))),"before"===f||"after"===f||!h.autoExpandMS||!1===t.hasChildren()||t.expanded||h.dragExpand&&!1===h.dragExpand(t,y)||t.scheduleAction("expand",h.autoExpandMS),f&&h.dragOver&&(y.hitMode=f,b=h.dragOver(t,y)),l=!1!==b&&null!==f,h.smartRevert&&(d.options.revert=!l),this._local._setDndStatus(a,t,o.helper,f,l);break;case"drop":(f=o.helper.data("hitMode"))&&h.dragDrop&&(y.hitMode=f,h.dragDrop(t,y));break;case"leave":t.scheduleAction("cancel"),o.helper.data("enterResponse",null),o.helper.data("hitMode",null),this._local._setDndStatus(a,t,o.helper,"out",void 0),h.dragLeave&&h.dragLeave(t,y);break;case"stop":x.removeClass("fancytree-drag-source"),e(document).off(".fancytree-dnd"),h.dragStop&&h.dragStop(t,y);break;default:e.error("Unsupported drag event: "+r)}return b},_cancelDrag:function(){var r=e.ui.ddmanager.current;r&&r.cancel()}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.dnd5.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function r(){v=null,y=null,h=null,b=null}function t(r,t){if(t.options.dnd5.scroll&&function(r,t){var a,o,n=r.options.dnd5,d=r.$scrollParent[0],s=n.scrollSensitivity,l=n.scrollSpeed,i=0;d!==document&&"HTML"!==d.tagName?(a=r.$scrollParent.offset(),o=d.scrollTop,a.top+d.offsetHeight-t.pageY<s?d.scrollHeight-r.$scrollParent.innerHeight()-o>0&&(d.scrollTop=i=o+l):o>0&&t.pageY-a.top<s&&(d.scrollTop=i=o-l)):(o=e(document).scrollTop())>0&&t.pageY-o<s?(i=o-l,e(document).scrollTop(i)):e(window).height()-(t.pageY-o)<s&&(i=o+l,e(document).scrollTop(i)),i&&r.debug("autoScroll: "+i+"px")}(t.tree,r),!t.node)return t.tree.warn("Ignore dragover for non-node"),m;var o,n,d,g=null,v=t.tree,y=v.options.dnd5,h=t.node,x=t.otherNode,D="center",E=e(h.span),S=E.find("span.fancytree-title");if(!1===b)return v.info("Ignore dragover, since dragenter returned false"),!1;if("string"==typeof b?e.error("assert failed: dragenter returned string"):(n=E.offset(),d=(r.pageY-n.top)/E.height(),b.after&&d>.75?g="after":!b.over&&b.after&&d>.5?g="after":b.before&&d<=.25?g="before":!b.over&&b.before&&d<=.5?g="before":b.over&&(g="over"),y.preventVoidMoves&&(h===x?(h.debug("Drop over source node prevented."),g=null):"before"===g&&x&&h===x.getNextSibling()?(h.debug("Drop after source node prevented."),g=null):"after"===g&&x&&h===x.getPrevSibling()?(h.debug("Drop before source node prevented."),g=null):"over"===g&&x&&x.parent===h&&x.isLastSibling()&&(h.debug("Drop last child over own parent prevented."),g=null))),t.hitMode=g,g&&y.dragOver&&(y.dragOver(h,t),g=t.hitMode),m=g,"after"===g||"before"===g||"over"===g){switch(o=y.dropMarkerOffsetX||0,g){case"before":D="top",o+=y.dropMarkerInsertOffsetX||0;break;case"after":D="bottom",o+=y.dropMarkerInsertOffsetX||0}u.toggleClass(l,"after"===g).toggleClass(f,"over"===g).toggleClass(i,"before"===g).show().position(a.fixPositionOptions({my:"left"+function(e){return 0===e?"":e>0?"+"+e:""+e}(o)+" center",at:"left "+D,of:S}))}else u.hide();return e(h.span).toggleClass(c,"after"===g||"before"===g||"over"===g).toggleClass(l,"after"===g).toggleClass(i,"before"===g).toggleClass(s,"over"===g).toggleClass(p,!1===g),g}var a=e.ui.fancytree,o=/Mac/.test(navigator.platform),n="fancytree-drag-source",d="fancytree-drag-remove",s="fancytree-drop-accept",l="fancytree-drop-after",i="fancytree-drop-before",f="fancytree-drop-over",p="fancytree-drop-reject",c="fancytree-drop-target",g="application/x-fancytree-node",u=null,v=null,y=null,h=null,b=null,m=null;return e.ui.fancytree.registerExtension({name:"dnd5",version:"2.28.0",options:{autoExpandMS:1500,dropMarkerInsertOffsetX:-16,dropMarkerOffsetX:-24,multiSource:!1,dragImage:null,dropEffect:null,dropEffectDefault:"move",preventForeignNodes:!1,preventNonNodes:!1,preventRecursiveMoves:!0,preventVoidMoves:!0,scroll:!0,scrollSensitivity:20,scrollSpeed:5,setTextTypeJson:!1,dragStart:null,dragDrag:e.noop,dragEnd:e.noop,dragEnter:null,dragOver:e.noop,dragExpand:e.noop,dragDrop:e.noop,dragLeave:e.noop},treeInit:function(l){var i,c,x,D=l.tree,E=l.options,S=E.glyph||null,N=E.dnd5,C=a.getNode;e.inArray("dnd",E.extensions)>=0&&e.error("Extensions 'dnd' and 'dnd5' are mutually exclusive."),N.dragStop&&e.error("dragStop is not used by ext-dnd5. Use dragEnd instead."),N.dragStart&&a.overrideMethod(l.options,"createNode",function(e,r){this._super.apply(this,arguments),r.node.span.draggable=!0}),this._superApply(arguments),this.$container.addClass("fancytree-ext-dnd5"),x=e("<span>").appendTo(this.$container),this.$scrollParent=x.scrollParent(),x.remove(),(u=e("#fancytree-drop-marker")).length||(u=e("<div id='fancytree-drop-marker'></div>").hide().css({"z-index":1e3,"pointer-events":"none"}).prependTo("body"),S&&a.setSpanIcon(u[0],S.map._addClass,S.map.dropMarker)),N.dragStart&&D.$container.on("dragstart drag dragend",function(t){var a,s=C(t),l=t.dataTransfer||t.originalEvent.dataTransfer,f={node:s,tree:D,options:D.options,originalEvent:t,dataTransfer:l,isCancelled:void 0},p=function(e,r){var t=r.options.dnd5,a=t.dropEffectDefault;if(t.dropEffect)return t.dropEffect(e,r);if(o){if(e.metaKey&&e.altKey)return"link";if(e.metaKey)return"move";if(e.altKey)return"copy"}else{if(e.ctrlKey)return"copy";if(e.shiftKey)return"move";if(e.altKey)return"link"}return a}(t,f),b="move"===p;switch(t.type){case"dragstart":v=s,!1===N.multiSource?y=[s]:!0===N.multiSource?(y=D.getSelectedNodes(),s.isSelected()||y.unshift(s)):y=N.multiSource(s,f),(h=e(e.map(y,function(e){return e.span}))).addClass(n),a=JSON.stringify(s.toDict());try{l.setData(g,a),l.setData("text/html",e(s.span).html()),l.setData("text/plain",s.title)}catch(e){D.warn("Could not set data (IE only accepts 'text') - "+e)}return N.setTextTypeJson?l.setData("text",a):l.setData("text",s.title),l.effectAllowed="all",c=null,N.dragImage?N.dragImage(s,f):(i=e(s.span).find(".fancytree-title"),y&&y.length>1&&(c=e("<span class='fancytree-childcounter'/>").text("+"+(y.length-1)).appendTo(i)),l.setDragImage&&l.setDragImage(i[0],-10,-10)),!1!==N.dragStart(s,f);case"drag":h.toggleClass(d,b),N.dragDrag(s,f);break;case"dragend":h.removeClass(n+" "+d),r(),f.isCancelled="none"===p,u.hide(),c&&(c.remove(),c=null),N.dragEnd(s,f)}}),N.dragEnter&&D.$container.on("dragenter dragover dragleave drop",function(a){var o,n,d,l=null,i=C(a),c=a.dataTransfer||a.originalEvent.dataTransfer,h={node:i,tree:D,options:D.options,hitMode:b,originalEvent:a,dataTransfer:c,otherNode:v||null,otherNodeList:y||null,otherNodeData:null,dropEffect:void 0,isCancelled:void 0};switch(a.type){case"dragenter":if(!i){D.debug("Ignore non-node "+a.type+": "+a.target.tagName+"."+a.target.className),b=!1;break}if(e(i.span).addClass(f).removeClass(s+" "+p),N.preventNonNodes&&!n){i.debug("Reject dropping a non-node."),b=!1;break}if(N.preventForeignNodes&&(!v||v.tree!==i.tree)){i.debug("Reject dropping a foreign node."),b=!1;break}setTimeout(function(){!N.autoExpandMS||!1===i.hasChildren()||i.expanded||N.dragExpand&&!1===N.dragExpand(i,h)||i.scheduleAction("expand",N.autoExpandMS)},0),u.show(),N.preventRecursiveMoves&&i.isDescendantOf(h.otherNode)?(i.debug("Reject dropping below own ancestor."),d=!1):d=function(r){var t;return!!r&&(t=e.isPlainObject(r)?{over:!!r.over,before:!!r.before,after:!!r.after}:e.isArray(r)?{over:e.inArray("over",r)>=0,before:e.inArray("before",r)>=0,after:e.inArray("after",r)>=0}:{over:!0===r||"over"===r,before:!0===r||"before"===r,after:!0===r||"after"===r},0!==Object.keys(t).length&&t)}(N.dragEnter(i,h)),b=d,l=d&&(d.over||d.before||d.after);break;case"dragover":l=!!(m=t(a,h));break;case"dragleave":if(!i){D.debug("Ignore non-node "+a.type+": "+a.target.tagName+"."+a.target.className);break}if(!e(i.span).hasClass(f)){i.debug("Ignore dragleave (multi)");break}e(i.span).removeClass(f+" "+s+" "+p),i.scheduleAction("cancel"),N.dragLeave(i,h),u.hide();break;case"drop":if(e.inArray(g,c.types)>=0&&(n=c.getData(g),D.info(a.type+": getData('application/x-fancytree-node'): '"+n+"'")),n||(n=c.getData("text"),D.info(a.type+": getData('text'): '"+n+"'")),n)try{void 0!==(o=JSON.parse(n)).title&&(h.otherNodeData=o)}catch(e){}D.debug(a.type+": nodeData: '"+n+"', otherNodeData: ",h.otherNodeData),e(i.span).removeClass(f+" "+s+" "+p),u.hide(),h.hitMode=m,h.dropEffect=c.dropEffect,h.isCancelled="none"===h.dropEffect,N.dragDrop(i,h),a.preventDefault(),r()}if(l)return a.preventDefault(),!1})}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.edit.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";var t=/Mac/.test(navigator.platform),i=e.ui.fancytree.escapeHtml,n=e.ui.fancytree.unescapeHtml;return e.ui.fancytree._FancytreeNodeClass.prototype.editStart=function(){var t,i=this,r=this.tree,s=r.ext.edit,a=r.options.edit,o=e(".fancytree-title",i.span),d={node:i,tree:r,options:r.options,isNew:e(i[r.statusClassPropName]).hasClass("fancytree-edit-new"),orgTitle:i.title,input:null,dirty:!1};if(!1===a.beforeEdit.call(i,{type:"beforeEdit"},d))return!1;e.ui.fancytree.assert(!s.currentNode,"recursive edit"),s.currentNode=this,s.eventData=d,r.widget._unbind(),e(document).on("mousedown.fancytree-edit",function(t){e(t.target).hasClass("fancytree-edit-input")||i.editEnd(!0,t)}),t=e("<input />",{class:"fancytree-edit-input",type:"text",value:r.options.escapeTitles?d.orgTitle:n(d.orgTitle)}),s.eventData.input=t,null!=a.adjustWidthOfs&&t.width(o.width()+a.adjustWidthOfs),null!=a.inputCss&&t.css(a.inputCss),o.html(t),t.focus().change(function(e){t.addClass("fancytree-edit-dirty")}).keydown(function(t){switch(t.which){case e.ui.keyCode.ESCAPE:i.editEnd(!1,t);break;case e.ui.keyCode.ENTER:return i.editEnd(!0,t),!1}t.stopPropagation()}).blur(function(e){return i.editEnd(!0,e)}),a.edit.call(i,{type:"edit"},d)},e.ui.fancytree._FancytreeNodeClass.prototype.editEnd=function(t,n){var r,s=this,a=this.tree,o=a.ext.edit,d=o.eventData,c=a.options.edit,l=e(".fancytree-title",s.span).find("input.fancytree-edit-input");return c.trim&&l.val(e.trim(l.val())),r=l.val(),d.dirty=r!==s.title,d.originalEvent=n,!1===t?d.save=!1:d.isNew?d.save=""!==r:d.save=d.dirty&&""!==r,!1!==c.beforeClose.call(s,{type:"beforeClose"},d)&&((!d.save||!1!==c.save.call(s,{type:"save"},d))&&(l.removeClass("fancytree-edit-dirty").off(),e(document).off(".fancytree-edit"),d.save?(s.setTitle(a.options.escapeTitles?r:i(r)),s.setFocus()):d.isNew?(s.remove(),s=d.node=null,o.relatedNode.setFocus()):(s.renderTitle(),s.setFocus()),o.eventData=null,o.currentNode=null,o.relatedNode=null,a.widget._bind(),e(a.$container).focus(),d.input=null,c.close.call(s,{type:"close"},d),!0))},e.ui.fancytree._FancytreeNodeClass.prototype.editCreateNode=function(t,i){var n,r=this.tree,s=this;t=t||"child",null==i?i={title:""}:"string"==typeof i?i={title:i}:e.ui.fancytree.assert(e.isPlainObject(i)),"child"!==t||this.isExpanded()||!1===this.hasChildren()?((n=this.addNode(i,t)).match=!0,e(n[r.statusClassPropName]).removeClass("fancytree-hide").addClass("fancytree-match"),n.makeVisible().done(function(){e(n[r.statusClassPropName]).addClass("fancytree-edit-new"),s.tree.ext.edit.relatedNode=s,n.editStart()})):this.setExpanded().done(function(){s.editCreateNode(t,i)})},e.ui.fancytree._FancytreeClass.prototype.isEditing=function(){return this.ext.edit?this.ext.edit.currentNode:null},e.ui.fancytree._FancytreeNodeClass.prototype.isEditing=function(){return!!this.tree.ext.edit&&this.tree.ext.edit.currentNode===this},e.ui.fancytree.registerExtension({name:"edit",version:"2.28.0",options:{adjustWidthOfs:4,allowEmpty:!1,inputCss:{minWidth:"3em"},triggerStart:["f2","mac+enter","shift+click"],trim:!0,beforeClose:e.noop,beforeEdit:e.noop,close:e.noop,edit:e.noop,save:e.noop},currentNode:null,treeInit:function(e){this._superApply(arguments),this.$container.addClass("fancytree-ext-edit")},nodeClick:function(t){return e.inArray("shift+click",t.options.edit.triggerStart)>=0&&t.originalEvent.shiftKey?(t.node.editStart(),!1):e.inArray("clickActive",t.options.edit.triggerStart)>=0&&t.node.isActive()&&!t.node.isEditing()&&e(t.originalEvent.target).hasClass("fancytree-title")?(t.node.editStart(),!1):this._superApply(arguments)},nodeDblclick:function(t){return e.inArray("dblclick",t.options.edit.triggerStart)>=0?(t.node.editStart(),!1):this._superApply(arguments)},nodeKeydown:function(i){switch(i.originalEvent.which){case 113:if(e.inArray("f2",i.options.edit.triggerStart)>=0)return i.node.editStart(),!1;break;case e.ui.keyCode.ENTER:if(e.inArray("mac+enter",i.options.edit.triggerStart)>=0&&t)return i.node.editStart(),!1}return this._superApply(arguments)}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.filter.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function t(e){return(e+"").replace(/([.?*+\^\$\[\]\\(){}|-])/g,"\\$1")}var i="__not_found__",n=e.ui.fancytree.escapeHtml;return e.ui.fancytree._FancytreeClass.prototype._applyFilterImpl=function(a,r,s){var l,o,d,c,u,h=0,p=this.options,f=p.escapeTitles,y=p.autoCollapse,g=e.extend({},p.filter,s),m="hide"===g.mode,x=!!g.leavesOnly&&!r;if("string"==typeof a){if(""===a)return this.warn("Fancytree passing an empty string as a filter is handled as clearFilter()."),void this.clearFilter();l=g.fuzzy?a.split("").reduce(function(e,t){return e+"[^"+t+"]*"+t}):t(a),d=new RegExp(".*"+l+".*","i"),c=new RegExp(t(a),"gi"),a=function(t){if(!t.title)return!1;var i=f?t.title:function(t){return t.indexOf(">")>=0?e("<div/>").html(t).text():t}(t.title),a=!!d.test(i);return a&&g.highlight&&(f?(u=i.replace(c,function(e){return"\ufff7"+e+"\ufff8"}),t.titleWithHighlight=n(u).replace(/\uFFF7/g,"<mark>").replace(/\uFFF8/g,"</mark>")):t.titleWithHighlight=i.replace(c,function(e){return"<mark>"+e+"</mark>"})),a}}return this.enableFilter=!0,this.lastFilterArgs=arguments,this.$div.addClass("fancytree-ext-filter"),m?this.$div.addClass("fancytree-ext-filter-hide"):this.$div.addClass("fancytree-ext-filter-dimm"),this.$div.toggleClass("fancytree-ext-filter-hide-expanders",!!g.hideExpanders),this.visit(function(e){delete e.match,delete e.titleWithHighlight,e.subMatchCount=0}),(o=this.getRootNode()._findDirectChild(i))&&o.remove(),p.autoCollapse=!1,this.visit(function(e){if(!x||null==e.children){var t=a(e),i=!1;if("skip"===t)return e.visit(function(e){e.match=!1},!0),"skip";t||!r&&"branch"!==t||!e.parent.match||(t=!0,i=!0),t&&(h++,e.match=!0,e.visitParents(function(e){e.subMatchCount+=1,!g.autoExpand||i||e.expanded||(e.setExpanded(!0,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),e._filterAutoExpanded=!0)}))}}),p.autoCollapse=y,0===h&&g.nodata&&m&&(o=g.nodata,e.isFunction(o)&&(o=o()),!0===o?o={}:"string"==typeof o&&(o={title:o}),o=e.extend({statusNodeType:"nodata",key:i,title:this.options.strings.noData},o),this.getRootNode().addNode(o).match=!0),this.render(),h},e.ui.fancytree._FancytreeClass.prototype.filterNodes=function(e,t){return"boolean"==typeof t&&(t={leavesOnly:t},this.warn("Fancytree.filterNodes() leavesOnly option is deprecated since 2.9.0 / 2015-04-19. Use opts.leavesOnly instead.")),this._applyFilterImpl(e,!1,t)},e.ui.fancytree._FancytreeClass.prototype.applyFilter=function(e){return this.warn("Fancytree.applyFilter() is deprecated since 2.1.0 / 2014-05-29. Use .filterNodes() instead."),this.filterNodes.apply(this,arguments)},e.ui.fancytree._FancytreeClass.prototype.filterBranches=function(e,t){return this._applyFilterImpl(e,!0,t)},e.ui.fancytree._FancytreeClass.prototype.clearFilter=function(){var t,n=this.getRootNode()._findDirectChild(i),a=this.options.escapeTitles,r=this.options.enhanceTitle;n&&n.remove(),this.visit(function(i){i.match&&i.span&&(t=e(i.span).find(">span.fancytree-title"),a?t.text(i.title):t.html(i.title),r&&r({type:"enhanceTitle"},{node:i,$title:t})),delete i.match,delete i.subMatchCount,delete i.titleWithHighlight,i.$subMatchBadge&&(i.$subMatchBadge.remove(),delete i.$subMatchBadge),i._filterAutoExpanded&&i.expanded&&i.setExpanded(!1,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),delete i._filterAutoExpanded}),this.enableFilter=!1,this.lastFilterArgs=null,this.$div.removeClass("fancytree-ext-filter fancytree-ext-filter-dimm fancytree-ext-filter-hide"),this.render()},e.ui.fancytree._FancytreeClass.prototype.isFilterActive=function(){return!!this.enableFilter},e.ui.fancytree._FancytreeNodeClass.prototype.isMatched=function(){return!(this.tree.enableFilter&&!this.match)},e.ui.fancytree.registerExtension({name:"filter",version:"2.28.0",options:{autoApply:!0,autoExpand:!1,counter:!0,fuzzy:!1,hideExpandedCounter:!0,hideExpanders:!1,highlight:!0,leavesOnly:!1,nodata:!0,mode:"dimm"},nodeLoadChildren:function(e,t){return this._superApply(arguments).done(function(){e.tree.enableFilter&&e.tree.lastFilterArgs&&e.options.filter.autoApply&&e.tree._applyFilterImpl.apply(e.tree,e.tree.lastFilterArgs)})},nodeSetExpanded:function(e,t,i){return delete e.node._filterAutoExpanded,!t&&e.options.filter.hideExpandedCounter&&e.node.$subMatchBadge&&e.node.$subMatchBadge.show(),this._superApply(arguments)},nodeRenderStatus:function(t){var i,n=t.node,a=t.tree,r=t.options.filter,s=e(n.span).find("span.fancytree-title"),l=e(n[a.statusClassPropName]),o=t.options.enhanceTitle,d=t.options.escapeTitles;return i=this._super(t),l.length&&a.enableFilter?(l.toggleClass("fancytree-match",!!n.match).toggleClass("fancytree-submatch",!!n.subMatchCount).toggleClass("fancytree-hide",!(n.match||n.subMatchCount)),!r.counter||!n.subMatchCount||n.isExpanded()&&r.hideExpandedCounter?n.$subMatchBadge&&n.$subMatchBadge.hide():(n.$subMatchBadge||(n.$subMatchBadge=e("<span class='fancytree-childcounter'/>"),e("span.fancytree-icon, span.fancytree-custom-icon",n.span).append(n.$subMatchBadge)),n.$subMatchBadge.show().text(n.subMatchCount)),!n.span||n.isEditing&&n.isEditing.call(n)||(n.titleWithHighlight?s.html(n.titleWithHighlight):d?s.text(n.title):s.html(n.title),o&&o({type:"enhanceTitle"},{node:n,$title:s})),i):i}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.glyph.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function r(r,n,a,o){var t=a.map,c=t[o],d=e(r),i=n+" "+(t._addClass||"");"string"==typeof c?d.attr("class",i+" "+c):c&&(c.text?r.textContent=""+c.text:c.html&&(r.innerHTML=c.html),d.attr("class",i+" "+(c.addClass||"")))}var n=e.ui.fancytree,a={awesome3:{_addClass:"",checkbox:"icon-check-empty",checkboxSelected:"icon-check",checkboxUnknown:"icon-check icon-muted",dragHelper:"icon-caret-right",dropMarker:"icon-caret-right",error:"icon-exclamation-sign",expanderClosed:"icon-caret-right",expanderLazy:"icon-angle-right",expanderOpen:"icon-caret-down",loading:"icon-refresh icon-spin",nodata:"icon-meh",noExpander:"",radio:"icon-circle-blank",radioSelected:"icon-circle",doc:"icon-file-alt",docOpen:"icon-file-alt",folder:"icon-folder-close-alt",folderOpen:"icon-folder-open-alt"},awesome4:{_addClass:"fa",checkbox:"fa-square-o",checkboxSelected:"fa-check-square-o",checkboxUnknown:"fa-square fancytree-helper-indeterminate-cb",dragHelper:"fa-arrow-right",dropMarker:"fa-long-arrow-right",error:"fa-warning",expanderClosed:"fa-caret-right",expanderLazy:"fa-angle-right",expanderOpen:"fa-caret-down",loading:"fa-spinner fa-pulse",nodata:"fa-meh-o",noExpander:"",radio:"fa-circle-thin",radioSelected:"fa-circle",doc:"fa-file-o",docOpen:"fa-file-o",folder:"fa-folder-o",folderOpen:"fa-folder-open-o"},awesome5:{_addClass:"",checkbox:"far fa-square",checkboxSelected:"far fa-check-square",checkboxUnknown:"fas fa-square fancytree-helper-indeterminate-cb",radio:"far fa-circle",radioSelected:"fas fa-circle",radioUnknown:"far fa-dot-circle",dragHelper:"fas fa-arrow-right",dropMarker:"fas fa-long-arrow-right",error:"fas fa-exclamation-triangle",expanderClosed:"fas fa-caret-right",expanderLazy:"fas fa-angle-right",expanderOpen:"fas fa-caret-down",loading:"fas fa-spinner fa-pulse",nodata:"far fa-meh",noExpander:"",doc:"far fa-file",docOpen:"far fa-file",folder:"far fa-folder",folderOpen:"far fa-folder-open"},bootstrap3:{_addClass:"glyphicon",checkbox:"glyphicon-unchecked",checkboxSelected:"glyphicon-check",checkboxUnknown:"glyphicon-expand fancytree-helper-indeterminate-cb",dragHelper:"glyphicon-play",dropMarker:"glyphicon-arrow-right",error:"glyphicon-warning-sign",expanderClosed:"glyphicon-menu-right",expanderLazy:"glyphicon-menu-right",expanderOpen:"glyphicon-menu-down",loading:"glyphicon-refresh fancytree-helper-spin",nodata:"glyphicon-info-sign",noExpander:"",radio:"glyphicon-remove-circle",radioSelected:"glyphicon-ok-circle",doc:"glyphicon-file",docOpen:"glyphicon-file",folder:"glyphicon-folder-close",folderOpen:"glyphicon-folder-open"},material:{_addClass:"material-icons",checkbox:{text:"check_box_outline_blank"},checkboxSelected:{text:"check_box"},checkboxUnknown:{text:"indeterminate_check_box"},dragHelper:{text:"play_arrow"},dropMarker:{text:"arrow-forward"},error:{text:"warning"},expanderClosed:{text:"chevron_right"},expanderLazy:{text:"last_page"},expanderOpen:{text:"expand_more"},loading:{text:"autorenew",addClass:"fancytree-helper-spin"},nodata:{text:"info"},noExpander:{text:""},radio:{text:"radio_button_unchecked"},radioSelected:{text:"radio_button_checked"},doc:{text:"insert_drive_file"},docOpen:{text:"insert_drive_file"},folder:{text:"folder"},folderOpen:{text:"folder_open"}}};return e.ui.fancytree.registerExtension({name:"glyph",version:"2.28.0",options:{preset:null,map:{}},treeInit:function(r){var o=r.tree,t=r.options.glyph;t.preset?(n.assert(!!a[t.preset],"Invalid value for `options.glyph.preset`: "+t.preset),t.map=e.extend({},a[t.preset],t.map)):o.warn("ext-glyph: missing `preset` option."),this._superApply(arguments),o.$container.addClass("fancytree-ext-glyph")},nodeRenderStatus:function(a){var o,t,c,d=a.node,i=e(d.span),l=a.options.glyph;return t=this._super(a),d.isRoot()?t:((c=i.children("span.fancytree-expander").get(0))&&r(c,"fancytree-expander",l,d.expanded&&d.hasChildren()?"expanderOpen":d.isUndefined()?"expanderLazy":d.hasChildren()?"expanderClosed":"noExpander"),(c=d.tr?e("td",d.tr).find("span.fancytree-checkbox").get(0):i.children("span.fancytree-checkbox").get(0))&&(o=n.evalOption("checkbox",d,d,l,!1),d.parent&&d.parent.radiogroup||"radio"===o?r(c,"fancytree-checkbox fancytree-radio",l,d.selected?"radioSelected":"radio"):r(c,"fancytree-checkbox",l,d.selected?"checkboxSelected":d.partsel?"checkboxUnknown":"checkbox")),(c=i.children("span.fancytree-icon").get(0))&&r(c,"fancytree-icon",l,d.statusNodeType?d.statusNodeType:d.folder?d.expanded&&d.hasChildren()?"folderOpen":"folder":d.expanded?"docOpen":"doc"),t)},nodeSetStatus:function(n,a,o,t){var c,d,i=n.options.glyph,l=n.node;return c=this._superApply(arguments),"error"!==a&&"loading"!==a&&"nodata"!==a||(l.parent?(d=e("span.fancytree-expander",l.span).get(0))&&r(d,"fancytree-expander",i,a):(d=e(".fancytree-statusnode-"+a,l[this.nodeContainerAttrName]).find("span.fancytree-icon").get(0))&&r(d,"fancytree-icon",i,a)),c}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.gridnav.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree","./jquery.fancytree.table"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree.table"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function t(t,n){var i,r=null,o=0;return t.children().each(function(){if(o>=n)return r=e(this),!1;i=e(this).prop("colspan"),o+=i||1}),r}function n(n,r){var o,a,u=n.closest("td"),s=null;switch(r){case i.LEFT:s=u.prev();break;case i.RIGHT:s=u.next();break;case i.UP:case i.DOWN:for(a=function(t,n){var i,r=n.get(0),o=0;return t.children().each(function(){if(this===r)return!1;i=e(this).prop("colspan"),o+=i||1}),o}(o=u.parent(),u);(o=r===i.UP?o.prev():o.next()).length&&(o.is(":hidden")||!(s=t(o,a))||!s.find(":input,a").length););}return s}var i=e.ui.keyCode,r={text:[i.UP,i.DOWN],checkbox:[i.UP,i.DOWN,i.LEFT,i.RIGHT],link:[i.UP,i.DOWN,i.LEFT,i.RIGHT],radiobutton:[i.UP,i.DOWN,i.LEFT,i.RIGHT],"select-one":[i.LEFT,i.RIGHT],"select-multiple":[i.LEFT,i.RIGHT]};return e.ui.fancytree.registerExtension({name:"gridnav",version:"2.28.0",options:{autofocusInput:!1,handleCursorKeys:!0},treeInit:function(t){this._requireExtension("table",!0,!0),this._superApply(arguments),this.$container.addClass("fancytree-ext-gridnav"),this.$container.on("focusin",function(n){var i,r=e.ui.fancytree.getNode(n.target);r&&!r.isActive()&&(i=t.tree._makeHookContext(r,n),t.tree._callHook("nodeSetActive",i,!0))})},nodeSetActive:function(t,n,i){var r=t.options.gridnav,o=t.node,a=t.originalEvent||{},u=e(a.target).is(":input");n=!1!==n,this._superApply(arguments),n&&(t.options.titlesTabbable?(u||(e(o.span).find("span.fancytree-title").focus(),o.setFocus()),t.tree.$container.attr("tabindex","-1")):r.autofocusInput&&!u&&e(o.tr||o.span).find(":input:enabled:first").focus())},nodeKeydown:function(t){var i,o,a,u=t.options.gridnav,s=t.originalEvent,c=e(s.target);return c.is(":input:enabled")?i=c.prop("type"):c.is("a")&&(i="link"),i&&u.handleCursorKeys?!((o=r[i])&&e.inArray(s.which,o)>=0&&(a=n(c,s.which))&&a.length&&(a.find(":input:enabled,a").focus(),1)):this._superApply(arguments)}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.persist.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function t(o,i,s,r,n){var c,d,l,u,f=!1,p=o.options.persist.expandOpts,y=[],v=[];for(s=s||[],n=n||e.Deferred(),c=0,l=s.length;c<l;c++)d=s[c],(u=o.getNodeByKey(d))?r&&u.isUndefined()?(f=!0,o.debug("_loadLazyNodes: "+u+" is lazy: loading..."),"expand"===r?y.push(u.setExpanded(!0,p)):y.push(u.load())):(o.debug("_loadLazyNodes: "+u+" already loaded."),u.setExpanded(!0,p)):(v.push(d),o.debug("_loadLazyNodes: "+u+" was not yet found."));return e.when.apply(e,y).always(function(){if(f&&v.length>0)t(o,i,v,r,n);else{if(v.length)for(o.warn("_loadLazyNodes: could not load those keys: ",v),c=0,l=v.length;c<l;c++)d=s[c],i._appendKey(a,s[c],!1);n.resolve()}}),n}var o=null,i=window.localStorage?{get:function(e){return window.localStorage.getItem(e)},set:function(e,t){window.localStorage.setItem(e,t)},remove:function(e){window.localStorage.removeItem(e)}}:null,s=window.sessionStorage?{get:function(e){return window.sessionStorage.getItem(e)},set:function(e,t){window.sessionStorage.setItem(e,t)},remove:function(e){window.sessionStorage.removeItem(e)}}:null,r=e.ui.fancytree.assert,n="active",a="expanded",c="selected";return"function"==typeof Cookies?o={get:Cookies.get,set:function(e,t){Cookies.set(e,t,this.options.persist.cookie)},remove:Cookies.remove}:e&&"function"==typeof e.cookie&&(o={get:e.cookie,set:function(t,o){e.cookie.set(t,o,this.options.persist.cookie)},remove:e.removeCookie}),e.ui.fancytree._FancytreeClass.prototype.clearPersistData=function(e){var t=this.ext.persist,o=t.cookiePrefix;(e=e||"active expanded focus selected").indexOf(n)>=0&&t._data(o+n,null),e.indexOf(a)>=0&&t._data(o+a,null),e.indexOf("focus")>=0&&t._data(o+"focus",null),e.indexOf(c)>=0&&t._data(o+c,null)},e.ui.fancytree._FancytreeClass.prototype.clearCookies=function(e){return this.warn("'tree.clearCookies()' is deprecated since v2.27.0: use 'clearPersistData()' instead."),this.clearPersistData(e)},e.ui.fancytree._FancytreeClass.prototype.getPersistData=function(){var e=this.ext.persist,t=e.cookiePrefix,o=e.cookieDelimiter,i={};return i.active=e._data(t+n),i[a]=(e._data(t+a)||"").split(o),i[c]=(e._data(t+c)||"").split(o),i.focus=e._data(t+"focus"),i},e.ui.fancytree.registerExtension({name:"persist",version:"2.28.0",options:{cookieDelimiter:"~",cookiePrefix:void 0,cookie:{raw:!1,expires:"",path:"",domain:"",secure:!1},expandLazy:!1,expandOpts:void 0,fireActivate:!0,overrideSource:!0,store:"auto",types:"active expanded focus selected"},_data:function(e,t){var o=this._local.store;if(void 0===t)return o.get.call(this,e);null===t?o.remove.call(this,e):o.set.call(this,e,t)},_appendKey:function(t,o,i){o=""+o;var s=this._local,r=this.options.persist.cookieDelimiter,n=s.cookiePrefix+t,a=s._data(n),c=a?a.split(r):[],d=e.inArray(o,c);d>=0&&c.splice(d,1),i&&c.push(o),s._data(n,c.join(r))},treeInit:function(d){var l=d.tree,u=d.options,f=this._local,p=this.options.persist;return f.cookiePrefix=p.cookiePrefix||"fancytree-"+l._id+"-",f.storeActive=p.types.indexOf(n)>=0,f.storeExpanded=p.types.indexOf(a)>=0,f.storeSelected=p.types.indexOf(c)>=0,f.storeFocus=p.types.indexOf("focus")>=0,f.store=null,"auto"===p.store&&(p.store=i?"local":"cookie"),e.isPlainObject(p.store)?f.store=p.store:"cookie"===p.store?f.store=o:"local"===p.store?f.store="local"===p.store?i:s:"session"===p.store&&(f.store="local"===p.store?i:s),r(f.store,"Need a valid store."),l.$div.on("fancytreeinit",function(o){if(!1!==l._triggerTreeEvent("beforeRestore",null,{})){var i,s,r,d,y=f._data(f.cookiePrefix+"focus"),v=!1===p.fireActivate;i=f._data(f.cookiePrefix+a),r=i&&i.split(p.cookieDelimiter),(f.storeExpanded?t(l,f,r,!!p.expandLazy&&"expand",null):(new e.Deferred).resolve()).done(function(){if(f.storeSelected){if(i=f._data(f.cookiePrefix+c))for(r=i.split(p.cookieDelimiter),s=0;s<r.length;s++)(d=l.getNodeByKey(r[s]))?(void 0===d.selected||p.overrideSource&&!1===d.selected)&&(d.selected=!0,d.renderStatus()):f._appendKey(c,r[s],!1);3===l.options.selectMode&&l.visit(function(e){if(e.selected)return e.fixSelection3AfterClick(),"skip"})}f.storeActive&&(!(i=f._data(f.cookiePrefix+n))||!u.persist.overrideSource&&l.activeNode||(d=l.getNodeByKey(i))&&(d.debug("persist: set active",i),d.setActive(!0,{noFocus:!0,noEvents:v}))),f.storeFocus&&y&&(d=l.getNodeByKey(y))&&(l.options.titlesTabbable?e(d.span).find(".fancytree-title").focus():e(l.$container).focus()),l._triggerTreeEvent("restore",null,{})})}}),this._superApply(arguments)},nodeSetActive:function(e,t,o){var i,s=this._local;return t=!1!==t,i=this._superApply(arguments),s.storeActive&&s._data(s.cookiePrefix+n,this.activeNode?this.activeNode.key:null),i},nodeSetExpanded:function(e,t,o){var i,s=e.node,r=this._local;return t=!1!==t,i=this._superApply(arguments),r.storeExpanded&&r._appendKey(a,s.key,t),i},nodeSetFocus:function(e,t){var o,i=this._local;return t=!1!==t,o=this._superApply(arguments),i.storeFocus&&i._data(i.cookiePrefix+"focus",this.focusNode?this.focusNode.key:null),o},nodeSetSelected:function(t,o,i){var s,r,n=t.tree,a=t.node,d=this._local;return o=!1!==o,s=this._superApply(arguments),d.storeSelected&&(3===n.options.selectMode?(r=(r=e.map(n.getSelectedNodes(!0),function(e){return e.key})).join(t.options.persist.cookieDelimiter),d._data(d.cookiePrefix+c,r)):d._appendKey(c,a.key,a.selected)),s}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.table.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function t(t,n){n=n||"",t||e.error("Assertion failed "+n)}function n(e,t){e.visit(function(e){var n=e.tr;if(n&&(n.style.display=e.hide||!t?"none":""),!e.expanded)return"skip"})}return e.ui.fancytree.registerExtension({name:"table",version:"2.28.0",options:{checkboxColumnIdx:null,indentation:16,nodeColumnIdx:0},treeInit:function(n){var r,o,d,i,s,a=n.tree,l=n.options,u=l.table,c=a.widget.element;if(null!=u.customStatus&&(null!=l.renderStatusColumns?e.error("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' only instead."):(a.warn("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' instead."),l.renderStatusColumns=u.customStatus)),l.renderStatusColumns&&!0===l.renderStatusColumns&&(l.renderStatusColumns=l.renderColumns),c.addClass("fancytree-container fancytree-ext-table"),(s=c.find(">tbody")).length||(c.find(">tr").length&&e.error("Expected table > tbody > tr. If you see this please open an issue."),s=e("<tbody>").appendTo(c)),a.tbody=s[0],o=e("thead >tr:last >th",c).length,(i=s.children("tr:first")).length)d=i.children("td").length,o&&d!==o&&(a.warn("Column count mismatch between thead ("+o+") and tbody ("+d+"): using tbody."),o=d),i=i.clone();else for(t(o>=1,"Need either <thead> or <tbody> with <td> elements to determine column count."),i=e("<tr />"),r=0;r<o;r++)i.append("<td />");i.find(">td").eq(u.nodeColumnIdx).html("<span class='fancytree-node' />"),l.aria&&(i.attr("role","row"),i.find("td").attr("role","gridcell")),a.rowFragment=document.createDocumentFragment(),a.rowFragment.appendChild(i.get(0)),s.empty(),a.statusClassPropName="tr",a.ariaPropName="tr",this.nodeContainerAttrName="tr",a.$container=c,this._superApply(arguments),e(a.rootNode.ul).remove(),a.rootNode.ul=null,this.$container.attr("tabindex",l.tabindex),l.aria&&a.$container.attr("role","treegrid").attr("aria-readonly",!0)},nodeRemoveChildMarkup:function(t){t.node.visit(function(t){t.tr&&(e(t.tr).remove(),t.tr=null)})},nodeRemoveMarkup:function(t){var n=t.node;n.tr&&(e(n.tr).remove(),n.tr=null),this.nodeRemoveChildMarkup(t)},nodeRender:function(r,o,d,i,s){var a,l,u,c,p,h,f,m,y=r.tree,C=r.node,v=r.options,x=!C.parent;if(!1!==y._enableUpdate){if(s||(r.hasCollapsedParents=C.parent&&!C.parent.expanded),!x)if(C.tr&&o&&this.nodeRemoveMarkup(r),C.tr)o?this.nodeRenderTitle(r):this.nodeRenderStatus(r);else{if(r.hasCollapsedParents&&!d)return;p=y.rowFragment.firstChild.cloneNode(!0),t(h=function(n){var r,o,d=n.parent,i=d?d.children:null;if(i&&i.length>1&&i[0]!==n)for(t((o=i[e.inArray(n,i)-1]).tr);o.children&&o.children.length&&(r=o.children[o.children.length-1]).tr;)o=r;else o=d;return o}(C)),!0===i&&s?p.style.display="none":d&&r.hasCollapsedParents&&(p.style.display="none"),h.tr?function(e,t){e.parentNode.insertBefore(t,e.nextSibling)}(h.tr,p):(t(!h.parent,"prev. row must have a tr, or be system root"),function(e,t){e.insertBefore(t,e.firstChild)}(y.tbody,p)),C.tr=p,C.key&&v.generateIds&&(C.tr.id=v.idPrefix+C.key),C.tr.ftnode=C,C.span=e("span.fancytree-node",C.tr).get(0),this.nodeRenderTitle(r),v.createNode&&v.createNode.call(y,{type:"createNode"},r)}if(v.renderNode&&v.renderNode.call(y,{type:"renderNode"},r),(a=C.children)&&(x||d||C.expanded))for(u=0,c=a.length;u<c;u++)(m=e.extend({},r,{node:a[u]})).hasCollapsedParents=m.hasCollapsedParents||!C.expanded,this.nodeRender(m,o,d,i,!0);a&&!s&&(f=C.tr||null,l=y.tbody.firstChild,C.visit(function(e){if(e.tr){if(e.parent.expanded||"none"===e.tr.style.display||(e.tr.style.display="none",n(e,!1)),e.tr.previousSibling!==f){C.debug("_fixOrder: mismatch at node: "+e);var t=f?f.nextSibling:l;y.tbody.insertBefore(e.tr,t)}f=e.tr}}))}},nodeRenderTitle:function(t,n){var r,o,d=t.node,i=t.options,s=d.isStatusNode();return o=this._super(t,n),d.isRootNode()?o:(i.checkbox&&!s&&null!=i.table.checkboxColumnIdx&&(r=e("span.fancytree-checkbox",d.span),e(d.tr).find("td").eq(+i.table.checkboxColumnIdx).html(r)),this.nodeRenderStatus(t),s?i.renderStatusColumns&&i.renderStatusColumns.call(t.tree,{type:"renderStatusColumns"},t):i.renderColumns&&i.renderColumns.call(t.tree,{type:"renderColumns"},t),o)},nodeRenderStatus:function(t){var n,r=t.node,o=t.options;this._super(t),e(r.tr).removeClass("fancytree-node"),n=(r.getLevel()-1)*o.table.indentation,e(r.span).css({paddingLeft:n+"px"})},nodeSetExpanded:function(t,r,o){function d(e){n(t.node,r),e?r&&t.options.autoScroll&&!o.noAnimation&&t.node.hasChildren()?t.node.getLastChild().scrollIntoView(!0,{topNode:t.node}).always(function(){o.noEvents||t.tree._triggerNodeEvent(r?"expand":"collapse",t),i.resolveWith(t.node)}):(o.noEvents||t.tree._triggerNodeEvent(r?"expand":"collapse",t),i.resolveWith(t.node)):(o.noEvents||t.tree._triggerNodeEvent(r?"expand":"collapse",t),i.rejectWith(t.node))}if(r=!1!==r,t.node.expanded&&r||!t.node.expanded&&!r)return this._superApply(arguments);var i=new e.Deferred,s=e.extend({},o,{noEvents:!0,noAnimation:!0});return o=o||{},this._super(t,r,s).done(function(){d(!0)}).fail(function(){d(!1)}),i.promise()},nodeSetStatus:function(t,n,r,o){if("ok"===n){var d=t.node,i=d.children?d.children[0]:null;i&&i.isStatusNode()&&e(i.tr).remove()}return this._superApply(arguments)},treeClear:function(e){return this.nodeRemoveChildMarkup(this._makeHookContext(this.rootNode)),this._superApply(arguments)},treeDestroy:function(e){return this.$container.find("tbody").empty(),this.$source&&this.$source.removeClass("fancytree-helper-hidden"),this._superApply(arguments)}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.themeroller.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";return e.ui.fancytree.registerExtension({name:"themeroller",version:"2.28.0",options:{activeClass:"ui-state-active",addClass:"ui-corner-all",focusClass:"ui-state-focus",hoverClass:"ui-state-hover",selectedClass:"ui-state-highlight"},treeInit:function(s){var t=s.widget.element,a=s.options.themeroller;this._superApply(arguments),"TABLE"===t[0].nodeName?(t.addClass("ui-widget ui-corner-all"),t.find(">thead tr").addClass("ui-widget-header"),t.find(">tbody").addClass("ui-widget-conent")):t.addClass("ui-widget ui-widget-content ui-corner-all"),t.delegate(".fancytree-node","mouseenter mouseleave",function(s){var t=e.ui.fancytree.getNode(s.target),i="mouseenter"===s.type;e(t.tr?t.tr:t.span).toggleClass(a.hoverClass+" "+a.addClass,i)})},treeDestroy:function(e){this._superApply(arguments),e.widget.element.removeClass("ui-widget ui-widget-content ui-corner-all")},nodeRenderStatus:function(s){var t={},a=s.node,i=e(a.tr?a.tr:a.span),l=s.options.themeroller;this._super(s),t[l.activeClass]=!1,t[l.focusClass]=!1,t[l.selectedClass]=!1,a.isActive()&&(t[l.activeClass]=!0),a.hasFocus()&&(t[l.focusClass]=!0),a.isSelected()&&!a.isActive()&&(t[l.selectedClass]=!0),i.toggleClass(l.activeClass,t[l.activeClass]),i.toggleClass(l.focusClass,t[l.focusClass]),i.toggleClass(l.selectedClass,t[l.selectedClass]),i.addClass(l.addClass)}}),e.ui.fancytree});

/*! Extension 'jquery.fancytree.wide.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(e){"use strict";function t(t,a){var n=e("#"+(t="fancytree-style-"+t));if(!a)return n.remove(),null;n.length||(n=e("<style />").attr("id",t).addClass("fancytree-style").prop("type","text/css").appendTo("head"));try{n.html(a)}catch(e){n[0].styleSheet.cssText=a}return n}function a(e,t,a,n,l,i){var s,r="#"+e+" span.fancytree-level-",c=[];for(s=0;s<t;s++)c.push(r+(s+1)+" span.fancytree-title { padding-left: "+(s*a+n)+i+"; }");return c.push("#"+e+" div.ui-effects-wrapper ul li span.fancytree-title, #"+e+" li.fancytree-animating span.fancytree-title { padding-left: "+l+i+"; position: static; width: auto; }"),c.join("\n")}var n=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;return e.ui.fancytree.registerExtension({name:"wide",version:"2.28.0",options:{iconWidth:null,iconSpacing:null,labelSpacing:null,levelOfs:null},treeCreate:function(l){this._superApply(arguments),this.$container.addClass("fancytree-ext-wide");var i,s,r,c,o,p=l.options.wide,u=e("<li id='fancytreeTemp'><span class='fancytree-node'><span class='fancytree-icon' /><span class='fancytree-title' /></span><ul />").appendTo(l.tree.$container),f=u.find(".fancytree-icon"),h=u.find("ul"),d=p.iconSpacing||f.css("margin-left"),y=p.iconWidth||f.css("width"),m=p.labelSpacing||"3px",_=p.levelOfs||h.css("padding-left");u.remove(),s=d.match(n)[2],d=parseFloat(d,10),r=m.match(n)[2],m=parseFloat(m,10),c=y.match(n)[2],y=parseFloat(y,10),o=_.match(n)[2],s===c&&o===c&&r===c||e.error("iconWidth, iconSpacing, and levelOfs must have the same css measure unit"),this._local.measureUnit=c,this._local.levelOfs=parseFloat(_),this._local.lineOfs=(1+(l.options.checkbox?1:0)+(!1===l.options.icon?0:1))*(y+d)+d,this._local.labelOfs=m,this._local.maxDepth=10,t(i=this.$container.uniqueId().attr("id"),a(i,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelOfs,this._local.measureUnit))},treeDestroy:function(e){return t(this.$container.attr("id"),null),this._superApply(arguments)},nodeRenderStatus:function(n){var l,i,s=n.node,r=s.getLevel();return i=this._super(n),r>this._local.maxDepth&&(l=this.$container.attr("id"),this._local.maxDepth*=2,s.debug("Define global ext-wide css up to level "+this._local.maxDepth),t(l,a(l,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelSpacing,this._local.measureUnit))),e(s.span).addClass("fancytree-level-"+r),i}}),e.ui.fancytree});
// Value returned by `require('jquery.fancytree')`
return $.ui.fancytree;
}));  // End of closure
